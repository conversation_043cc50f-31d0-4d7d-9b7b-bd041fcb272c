//http://192.168.1.42:8000/


// http://192.168.1.42:8000/api/salon-invitations/create/

// body
//   {
//     "jobseeker_id": 3,
//     "message": "We would like to invite you for an interview at our salon. Please contact us at 9876543210 to schedule a convenient time."
//   }

// response

// {
//     "id": 2,
//     "jobseeker": 3,
//     "jobseeker_name": "jobseeker 1",
//     "salon": 1,
//     "salon_name": "admin 1",
//     "salon_details": {
//         "salon_name": "admin 1",
//         "owner_name": "admin 1",
//         "location": "123 Main Street",
//         "contact_no": "9477642431",
//         "email": "<EMAIL>"
//     },
//     "message": "We would like to invite you for an interview at our salon. Please contact us at 9876543210 to schedule a convenient time.",
//     "status": "PENDING",
//     "created_at": "2025-05-20T17:25:43.803407+05:30"
// }

// add a invitaion button  with  a conformation dilog








//HiredEmployeesScreen


// import 'package:flutter/material.dart';
// import 'package:job/services/api_service.dart';
// import 'package:job/view/salonowner/employ_bottom/employee_details_screen.dart';
// import 'package:intl/intl.dart';

// class HiredEmployeesScreen extends StatefulWidget {
//   final List<Map<String, dynamic>>? hiredEmployees;

//   const HiredEmployeesScreen({Key? key, this.hiredEmployees}) : super(key: key);

//   @override
//   State<HiredEmployeesScreen> createState() => _HiredEmployeesScreenState();
// }

// class _HiredEmployeesScreenState extends State<HiredEmployeesScreen> {
//   String _searchQuery = '';
//   final TextEditingController _searchController = TextEditingController();
//   List<Map<String, dynamic>> _apiHiredEmployees = [];
//   bool _isLoading = true;
//   String _errorMessage = '';
  
//   // Add a set to track employee IDs from the main employer page
//   Set<int> _existingEmployeeIds = {};

//   @override
//   void initState() {
//     super.initState();
//     _searchController.addListener(() {
//       setState(() {
//         _searchQuery = _searchController.text;
//       });
//     });

//     // If hiredEmployees is provided, use it, otherwise fetch from API
//     if (widget.hiredEmployees != null) {
//       _isLoading = false;
//       // Extract IDs from provided employees to avoid duplicates
//       _extractExistingEmployeeIds(widget.hiredEmployees!);
//     } else {
//       // First get existing employees from main page, then fetch hired employees
//       _getExistingEmployees().then((_) => _fetchHiredEmployees());
//     }
//   }

//   // Method to get existing employees from the main employer page
//   Future<void> _getExistingEmployees() async {
//     try {
//       final existingEmployees = await ApiService.fetchSalonEmployees();
//       setState(() {
//         // Extract IDs from existing employees
//         _existingEmployeeIds = existingEmployees
//             .where((employee) => employee['id'] != null)
//             .map<int>((employee) => employee['id'] as int)
//             .toSet();
//         print('Existing employee IDs: $_existingEmployeeIds');
//       });
//     } catch (e) {
//       print('Error fetching existing employees: $e');
//       // Continue with empty set if this fails
//     }
//   }

//   // Helper method to extract IDs from provided employees
//   void _extractExistingEmployeeIds(List<Map<String, dynamic>> employees) {
//     _existingEmployeeIds = employees
//         .map<int>((employee) => employee['id'] as int)
//         .toSet();
//   }

//   Future<void> _fetchHiredEmployees() async {
//     try {
//       setState(() {
//         _isLoading = true;
//         _errorMessage = '';
//       });

//       final hiredEmployees = await ApiService.fetchHiredEmployees();

//       // Create a list to store employees that are not already employed
//       List<Map<String, dynamic>> filteredEmployees = [];
      
//       // Filter out employees that are already in the main employer page
//       for (var employee in hiredEmployees) {
//         final employeeId = employee['id'];
//         final isExisting = employeeId != null && _existingEmployeeIds.contains(employeeId);
        
//         // Also check if the employee is already employed at your salon
//         final isAlreadyEmployed = employee['is_already_employed'] == true;
        
//         if (isExisting) {
//           print('Filtering out employee: ${employee['applicant_details']?['name'] ?? 'Unknown'} (ID: $employeeId) - Already in employer page');
//           continue;
//         }
        
//         if (isAlreadyEmployed) {
//           print('Filtering out employee: ${employee['applicant_details']?['name'] ?? 'Unknown'} (ID: $employeeId) - Already employed at salon');
//           continue;
//         }
        
//         // Add to filtered list
//         filteredEmployees.add(employee);
//       }

//       setState(() {
//         _apiHiredEmployees = filteredEmployees;
//         _isLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         _errorMessage = e.toString();
//         _isLoading = false;
//       });
//       print('Error fetching hired employees: $e');
//     }
//   }

//   @override
//   void dispose() {
//     _searchController.dispose();
//     super.dispose();
//   }

//   // Get the list of employees to display (either from widget prop or API)
//   List<Map<String, dynamic>> get allEmployees {
//     return widget.hiredEmployees ?? _apiHiredEmployees;
//   }

//   // Filter employees based on search query
//   List<Map<String, dynamic>> get filteredEmployees {
//     if (_searchQuery.isEmpty) {
//       return allEmployees;
//     } else {
//       return allEmployees.where((employee) {
//         // Get name from appropriate field based on data source
//         final name = employee.containsKey('applicant_details')
//             ? employee['applicant_details']['name'].toString()
//             : employee['name'].toString();

//         // Get position from appropriate field based on data source
//         final position = employee.containsKey('job_title')
//             ? employee['job_title'].toString()
//             : employee['position'].toString();

//         return name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
//             position.toLowerCase().contains(_searchQuery.toLowerCase());
//       }).toList();
//     }
//   }

//   // Format employee data for display
//   Map<String, dynamic> _formatEmployeeData(Map<String, dynamic> employee) {
//     if (employee.containsKey('applicant_details')) {
//       // API data format
//       final applicantDetails = employee['applicant_details'];
//       return {
//         'id': employee['id'],
//         'jobseeker_id': employee['jobseeker_id'],
//         'name': applicantDetails['name'] ?? 'Unknown',
//         'position': employee['job_title'] ?? 'Unknown Position',
//         'image':
//             applicantDetails['profile_picture'] ??
//             'https://img.icons8.com/ios-filled/50/user.png',
//         'contact_no': applicantDetails['contact_no'] ?? 'Unknown',
//         'email': applicantDetails['email'] ?? 'Unknown',
//         'experience': '${applicantDetails['years_of_experience'] ?? 0} years',
//         'original_data': employee,
//       };
//     } else {
//       // Local data format (already formatted)
//       return employee;
//     }
//   }

//   void _showAddOfficialEmployeeDialog(Map<String, dynamic> employee) {
//     // Check if this employee is already employed at your salon
//     final originalData = employee['original_data'];
//     if (originalData != null && originalData['is_already_employed'] == true) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('${employee['name']} is already an employee at your salon'),
//           backgroundColor: Colors.orange,
//         ),
//       );
//       return;
//     }

//     final jobTitleController = TextEditingController(
//       text: employee['position'] ?? '',
//     );
//     final salaryController = TextEditingController();
//     final joinDateController = TextEditingController(
//       text: DateFormat('yyyy-MM-dd').format(DateTime.now()),
//     );
//     bool isSubmitting = false;

//     showDialog(
//       context: context,
//       builder: (context) {
//         return StatefulBuilder(
//           builder: (context, setDialogState) {
//             return AlertDialog(
//               title: const Text('Add as Official Employee'),
//               content: SingleChildScrollView(
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     // Employee name (non-editable)
//                     const Text(
//                       'Employee Name',
//                       style: TextStyle(
//                         fontWeight: FontWeight.w500,
//                         fontSize: 14,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     Container(
//                       padding: const EdgeInsets.all(12),
//                       decoration: BoxDecoration(
//                         color: Colors.grey[100],
//                         borderRadius: BorderRadius.circular(8),
//                       ),
//                       child: Row(
//                         children: [
//                           CircleAvatar(
//                             radius: 16,
//                             backgroundImage: NetworkImage(employee['image']),
//                           ),
//                           const SizedBox(width: 12),
//                           Expanded(
//                             child: Text(
//                               employee['name'],
//                               style: const TextStyle(
//                                 fontSize: 16,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                     const SizedBox(height: 16),

//                     // Job Title
//                     const Text(
//                       'Job Title',
//                       style: TextStyle(
//                         fontWeight: FontWeight.w500,
//                         fontSize: 14,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     TextField(
//                       controller: jobTitleController,
//                       decoration: InputDecoration(
//                         filled: true,
//                         fillColor: Colors.grey[100],
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(8),
//                           borderSide: BorderSide.none,
//                         ),
//                         hintText: 'Enter job title',
//                       ),
//                     ),
//                     const SizedBox(height: 16),

//                     // Salary
//                     const Text(
//                       'Monthly Salary (₹)',
//                       style: TextStyle(
//                         fontWeight: FontWeight.w500,
//                         fontSize: 14,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     TextField(
//                       controller: salaryController,
//                       keyboardType: TextInputType.number,
//                       decoration: InputDecoration(
//                         filled: true,
//                         fillColor: Colors.grey[100],
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(8),
//                           borderSide: BorderSide.none,
//                         ),
//                         hintText: 'Enter monthly salary',
//                         prefixText: '₹ ',
//                       ),
//                     ),
//                     const SizedBox(height: 16),

//                     // Join Date
//                     const Text(
//                       'Join Date',
//                       style: TextStyle(
//                         fontWeight: FontWeight.w500,
//                         fontSize: 14,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     TextField(
//                       controller: joinDateController,
//                       readOnly: true,
//                       decoration: InputDecoration(
//                         filled: true,
//                         fillColor: Colors.grey[100],
//                         border: OutlineInputBorder(
//                           borderRadius: BorderRadius.circular(8),
//                           borderSide: BorderSide.none,
//                         ),
//                         hintText: 'Select join date',
//                         suffixIcon: const Icon(Icons.calendar_today),
//                       ),
//                       onTap: () async {
//                         final DateTime? picked = await showDatePicker(
//                           context: context,
//                           initialDate: DateTime.now(),
//                           firstDate: DateTime(2000),
//                           lastDate: DateTime(2101),
//                         );
//                         if (picked != null) {
//                           setDialogState(() {
//                             joinDateController.text = DateFormat(
//                               'yyyy-MM-dd',
//                             ).format(picked);
//                           });
//                         }
//                       },
//                     ),
//                   ],
//                 ),
//               ),
//               actions: [
//                 TextButton(
//                   onPressed: () => Navigator.pop(context),
//                   child: const Text('Cancel'),
//                 ),
//                 ElevatedButton(
//                   onPressed: isSubmitting
//                       ? null
//                       : () async {
//                           // Validate inputs
//                           if (jobTitleController.text.isEmpty ||
//                               salaryController.text.isEmpty ||
//                               joinDateController.text.isEmpty) {
//                             ScaffoldMessenger.of(context).showSnackBar(
//                               const SnackBar(
//                                 content: Text('Please fill all fields'),
//                                 backgroundColor: Colors.red,
//                               ),
//                             );
//                             return;
//                           }

//                           // Set loading state
//                           setDialogState(() {
//                             isSubmitting = true;
//                           });

//                           try {
//                             // Get jobseeker ID
//                             final jobseekerId = employee['jobseeker_id'];
//                             if (jobseekerId == null) {
//                               throw Exception('Jobseeker ID not found');
//                             }

//                             // Call API to add official employee
//                             await ApiService.addOfficialEmployee(
//                               jobseekerId: jobseekerId,
//                               joinDate: joinDateController.text,
//                               jobTitle: jobTitleController.text,
//                               currentSalary: salaryController.text,
//                             );

//                             // Close dialog
//                             if (context.mounted) Navigator.pop(context);

//                             // Refresh employee list
//                             _fetchHiredEmployees();

//                             // Show success message
//                             if (context.mounted) {
//                               ScaffoldMessenger.of(context).showSnackBar(
//                                 SnackBar(
//                                   content: Row(
//                                     children: [
//                                       const Icon(
//                                         Icons.check_circle_outline,
//                                         color: Colors.white,
//                                       ),
//                                       const SizedBox(width: 12),
//                                       Expanded(
//                                         child: Text(
//                                           '${employee['name']} added as official employee',
//                                           style: const TextStyle(
//                                             color: Colors.white,
//                                             fontSize: 14,
//                                             fontWeight: FontWeight.w500,
//                                           ),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   behavior: SnackBarBehavior.floating,
//                                   backgroundColor: Colors.green.shade700,
//                                   elevation: 6,
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(10),
//                                   ),
//                                   duration: const Duration(seconds: 3),
//                                 ),
//                               );
//                             }
//                           } catch (e) {
//                             // Reset loading state
//                             setDialogState(() {
//                               isSubmitting = false;
//                             });

//                             // Show error message
//                             if (context.mounted) {
//                               ScaffoldMessenger.of(context).showSnackBar(
//                                 SnackBar(
//                                   content: Text(
//                                     'Failed to add official employee: ${e.toString().replaceAll('Exception: ', '')}',
//                                   ),
//                                   backgroundColor: Colors.red,
//                                 ),
//                               );
//                             }
//                           }
//                         },
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: Colors.black,
//                     foregroundColor: Colors.white,
//                   ),
//                   child: isSubmitting
//                       ? const SizedBox(
//                           width: 20,
//                           height: 20,
//                           child: CircularProgressIndicator(
//                             color: Colors.white,
//                             strokeWidth: 2,
//                           ),
//                         )
//                       : const Text('Add Employee'),
//                 ),
//               ],
//             );
//           },
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.grey[50],
//       appBar: AppBar(
//         backgroundColor: Colors.white,
//         elevation: 0,
//         title: const Text(
//           'Hired Employees',
//           style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
//         ),
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
//           onPressed: () => Navigator.of(context).pop(),
//         ),
//         centerTitle: true,
//         actions: [
//           IconButton(
//             icon: const Icon(Icons.refresh, color: Colors.black),
//             onPressed: _fetchHiredEmployees,
//           ),
//         ],
//       ),

//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Search bar
//               Container(
//                 decoration: BoxDecoration(
//                   color: Colors.white,
//                   borderRadius: BorderRadius.circular(12),
//                   boxShadow: [
//                     BoxShadow(
//                       color: Colors.grey.withOpacity(0.1),
//                       spreadRadius: 1,
//                       blurRadius: 4,
//                       offset: const Offset(0, 1),
//                     ),
//                   ],
//                 ),
//                 child: TextField(
//                   controller: _searchController,
//                   decoration: InputDecoration(
//                     hintText: 'Search employees...',
//                     prefixIcon: const Icon(Icons.search, color: Colors.grey),
//                     border: InputBorder.none,
//                     contentPadding: const EdgeInsets.symmetric(
//                       horizontal: 16,
//                       vertical: 16,
//                     ),
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 16),

//               // Employee count
//               Text(
//                 '${filteredEmployees.length} Hired Employees',
//                 style: TextStyle(
//                   color: Colors.grey[600],
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//               const SizedBox(height: 16),

//               // Employee list
//               Expanded(
//                 child: _isLoading
//                     ? const Center(child: CircularProgressIndicator())
//                     : _errorMessage.isNotEmpty
//                     ? Center(
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Icon(
//                               Icons.error_outline,
//                               size: 48,
//                               color: Colors.red[400],
//                             ),
//                             const SizedBox(height: 16),
//                             Text(
//                               'Error loading employees',
//                               style: TextStyle(
//                                 color: Colors.grey[800],
//                                 fontSize: 16,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                             const SizedBox(height: 8),
//                             Text(
//                               _errorMessage,
//                               style: TextStyle(
//                                 color: Colors.grey[600],
//                                 fontSize: 14,
//                               ),
//                               textAlign: TextAlign.center,
//                             ),
//                             const SizedBox(height: 16),
//                             ElevatedButton(
//                               onPressed: _fetchHiredEmployees,
//                               style: ElevatedButton.styleFrom(
//                                 backgroundColor: Colors.black,
//                                 foregroundColor: Colors.white,
//                               ),
//                               child: const Text('Retry'),
//                             ),
//                           ],
//                         ),
//                       )
//                     : filteredEmployees.isEmpty
//                     ? Center(
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Icon(
//                               Icons.person_off_outlined,
//                               size: 48,
//                               color: Colors.grey[400],
//                             ),
//                             const SizedBox(height: 16),
//                             Text(
//                               'No hired employees found',
//                               style: TextStyle(
//                                 color: Colors.grey[600],
//                                 fontSize: 16,
//                               ),
//                             ),
//                           ],
//                         ),
//                       )
//                     : ListView.builder(
//                         itemCount: filteredEmployees.length,
//                         itemBuilder: (context, index) {
//                           final employeeData = _formatEmployeeData(
//                             filteredEmployees[index],
//                           );
                          
//                           // Check if employee is already employed at the salon
//                           final originalData = employeeData['original_data'];
//                           final isAlreadyEmployed = originalData != null && 
//                                                    originalData['is_already_employed'] == true;

//                           return Container(
//                             margin: const EdgeInsets.only(bottom: 12),
//                             decoration: BoxDecoration(
//                               color: Colors.white,
//                               borderRadius: BorderRadius.circular(12),
//                               boxShadow: [
//                                 BoxShadow(
//                                   color: Colors.grey.withOpacity(0.1),
//                                   spreadRadius: 1,
//                                   blurRadius: 4,
//                                   offset: const Offset(0, 1),
//                                 ),
//                               ],
//                             ),
//                             child: ListTile(
//                               contentPadding: const EdgeInsets.symmetric(
//                                 horizontal: 16,
//                                 vertical: 8,
//                               ),
//                               leading: CircleAvatar(
//                                 radius: 24,
//                                 backgroundImage: NetworkImage(
//                                   employeeData['image'],
//                                 ),
//                               ),
//                               title: Text(
//                                 employeeData['name'],
//                                 style: const TextStyle(
//                                   fontWeight: FontWeight.w600,
//                                   fontSize: 16,
//                                 ),
//                               ),
//                               subtitle: Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     employeeData['position'],
//                                     style: TextStyle(
//                                       color: Colors.grey[600],
//                                       fontSize: 14,
//                                     ),
//                                   ),
//                                   const SizedBox(height: 4),
//                                   Text(
//                                     'Phone: ${employeeData['contact_no'] ?? '+91 ${9876540000 + index}'}',
//                                     style: TextStyle(
//                                       color: Colors.grey[500],
//                                       fontSize: 12,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                               trailing: !isAlreadyEmployed ? IconButton(
//                                 icon: const Icon(
//                                   Icons.person_add,
//                                   color: Colors.blue,
//                                 ),
//                                 onPressed: () {
//                                   _showAddOfficialEmployeeDialog(employeeData);
//                                 },
//                                 tooltip: 'Add as Official Employee',
//                               ) : null,
//                               isThreeLine: true,
//                               onTap: () {
//                                 Navigator.push(
//                                   context,
//                                   MaterialPageRoute(
//                                     builder: (context) => EmployeeDetailsScreen(
//                                       employee: employeeData,
//                                     ),
//                                   ),
//                                 );
//                               },
//                             ),
//                           );
//                         },
//                       ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
