import 'package:flutter/material.dart';
import 'package:job/uttilits/color_const.dart';

class JobseekerNotification extends StatefulWidget {
  const JobseekerNotification({super.key});

  @override
  State<JobseekerNotification> createState() => _JobseekerNotificationState();
}

class _JobseekerNotificationState extends State<JobseekerNotification> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.04;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Notifications',
          style: TextStyle(
            color: Colors.black,
            fontSize: size.width * 0.055,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Add clear all functionality
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(
                        Icons.check_circle_outline,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'All notifications cleared',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: Colors.black,
                  elevation: 6,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  duration: const Duration(seconds: 2),
                  margin: const EdgeInsets.only(
                    top: 50,
                    left: 10,
                    right: 10,
                  ), // Position at top
                ),
              );
              setState(() {
                // Here you would clear the notifications in a real app
              });
            },
            child: Text(
              'Clear All',
              style: TextStyle(
                color: ColorConstants.black,
                fontSize: size.width * 0.035,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: EdgeInsets.all(padding),
        children: [
          _buildDateSection(context, 'Today'),
          _buildNotificationItem(
            context,
            title: 'Application Status Update',
            description:
                'Your application for Hair Stylist at Glamour Salon has been viewed',
            time: '2 hours ago',
            icon: Icons.content_cut,
            isUnread: true,
          ),
          _buildNotificationItem(
            context,
            title: 'New Salon Job Match',
            description:
                'We found a job that matches your skills: Senior Colorist at Beauty Hub',
            time: '5 hours ago',
            icon: Icons.search,
            isUnread: true,
          ),

          _buildDateSection(context, 'Yesterday'),
          _buildNotificationItem(
            context,
            title: 'Interview Scheduled',
            description:
                'Your interview with Elegance Salon is scheduled for tomorrow at 2:00 PM',
            time: '1 day ago',
            icon: Icons.calendar_today,
            isUnread: false,
          ),
          _buildNotificationItem(
            context,
            title: 'Profile Views',
            description: 'Your profile has been viewed by 5 salon owners',
            time: '1 day ago',
            icon: Icons.remove_red_eye,
            isUnread: false,
          ),

          _buildDateSection(context, 'This Week'),
          _buildNotificationItem(
            context,
            title: 'Application Successful',
            description:
                'Your application for Nail Technician at Luxe Beauty has been submitted',
            time: '3 days ago',
            icon: Icons.check_circle_outline,
            isUnread: false,
          ),
          _buildNotificationItem(
            context,
            title: 'New Message',
            description:
                'You have a new message from the manager at Style Studio',
            time: '5 days ago',
            icon: Icons.message_outlined,
            isUnread: false,
          ),
          _buildNotificationItem(
            context,
            title: 'Skill Verification',
            description: 'Your makeup artist certification has been verified',
            time: '6 days ago',
            icon: Icons.verified_user_outlined,
            isUnread: false,
          ),
        ],
      ),
    );
  }

  Widget _buildDateSection(BuildContext context, String date) {
    final size = MediaQuery.of(context).size;

    return Container(
      margin: EdgeInsets.symmetric(vertical: size.height * 0.02),
      child: Row(
        children: [
          Text(
            date,
            style: TextStyle(
              fontSize: size.width * 0.045,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          SizedBox(width: size.width * 0.02),
          Expanded(
            child: Container(height: 1, color: Colors.grey.withOpacity(0.3)),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context, {
    required String title,
    required String description,
    required String time,
    required IconData icon,
    required bool isUnread,
  }) {
    final size = MediaQuery.of(context).size;

    return Container(
      margin: EdgeInsets.only(bottom: size.height * 0.015),
      decoration: BoxDecoration(
        color: isUnread ? ColorConstants.black.withOpacity(0.05) : Colors.white,
        borderRadius: BorderRadius.circular(size.width * 0.03),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(size.width * 0.03),
          onTap: () {
            // Handle notification tap
            setState(() {
              // Mark as read in a real app
            });

            // Show a snackbar to demonstrate the tap action
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Notification: $title'),
                backgroundColor: Colors.black,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: EdgeInsets.all(size.width * 0.04),
                duration: const Duration(seconds: 2),
              ),
            );
          },
          child: Padding(
            padding: EdgeInsets.all(size.width * 0.04),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(size.width * 0.025),
                  decoration: BoxDecoration(
                    color: ColorConstants.black.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(size.width * 0.02),
                  ),
                  child: Icon(
                    icon,
                    color: ColorConstants.black,
                    size: size.width * 0.06,
                  ),
                ),
                SizedBox(width: size.width * 0.03),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: size.width * 0.04,
                              ),
                            ),
                          ),
                          if (isUnread)
                            Container(
                              width: size.width * 0.02,
                              height: size.width * 0.02,
                              decoration: BoxDecoration(
                                color: ColorConstants.black,
                                shape: BoxShape.circle,
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: size.height * 0.005),
                      Text(
                        description,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: size.width * 0.035,
                        ),
                      ),
                      SizedBox(height: size.height * 0.01),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            time,
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: size.width * 0.03,
                            ),
                          ),
                          if (isUnread)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: size.width * 0.02,
                                vertical: size.width * 0.005,
                              ),
                              decoration: BoxDecoration(
                                color: ColorConstants.black.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(
                                  size.width * 0.01,
                                ),
                              ),
                              child: Text(
                                'NEW',
                                style: TextStyle(
                                  color: ColorConstants.black,
                                  fontSize: size.width * 0.03,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
