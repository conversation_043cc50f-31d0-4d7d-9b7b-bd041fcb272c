import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/uttilits/image_const.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io' show Platform;
import 'package:android_intent_plus/android_intent.dart' as android_intent;

class JobseekerHelpcenter extends StatefulWidget {
  const JobseekerHelpcenter({Key? key}) : super(key: key);

  @override
  State<JobseekerHelpcenter> createState() => _JobseekerHelpcenterState();
}

class _JobseekerHelpcenterState extends State<JobseekerHelpcenter> {
  // Sample data - in a real app, this would come from an API
  final Map<String, dynamic> adminDetails = {
    'name': 'MountVoq Support Team',
    'email': '<EMAIL>',
    'phone': '+91 9876543210',
    'whatsapp': '+91 9876543210',
  };

  final Map<String, dynamic> subscriptionDetails = {
    'plan': 'Premium',
    'startDate': DateTime.now().subtract(const Duration(days: 30)),
    'expiryDate': DateTime.now().add(const Duration(days: 335)),
    'features': [
      'Unlimited job postings',
      'Access to professional profiles',
      'Premium support',
      'Analytics dashboard',
      'Employee management',
    ],
  };

  final List<Map<String, dynamic>> faqList = [
    {
      'question': 'How do I post a new job vacancy?',
      'answer':
          'Go to "Our Vacancies" from the home screen and tap the + button at the bottom right. Fill in the job details and submit.',
    },
    {
      'question': 'How can I renew my subscription?',
      'answer':
          'Contact our support team via email or phone to renew your subscription before the expiry date.',
    },
    {
      'question': 'Can I upgrade my plan?',
      'answer':
          'Yes, you can upgrade your plan anytime. Contact our support team for assistance.',
    },
    {
      'question': 'How do I contact a professional?',
      'answer':
          'Go to "Find Professionals", select a professional, and use the contact options available on their profile.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Help Center',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Logo and welcome message with enhanced design
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  vertical: 24,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xffE65B00), Color(0xff3171C9)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xffE65B00).withOpacity(0.3),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.black,
                      radius: 40,
                      backgroundImage: AssetImage(
                        ImageConstants.backgroundColor,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Welcome to MountVoq Support',
                      style: GoogleFonts.inter(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'We\'re here to help you with any questions or issues',
                      textAlign: TextAlign.center,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              SizedBox(height: 20),

              // Contact support card with enhanced design
              _buildSectionCard(
                title: 'Contact Support',
                icon: Icons.support_agent,
                iconColor: Color(0xffE65B00),
                child: Column(
                  children: [
                    _buildContactButton(
                      icon: Icons.email_outlined,
                      title: 'Email Support',
                      subtitle: adminDetails['email'],
                      color: Colors.blue,
                      onTap: () => _launchEmail(adminDetails['email']),
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: Colors.grey.withOpacity(0.1),
                    ),
                    _buildContactButton(
                      icon: Icons.phone_outlined,
                      title: 'Call Support',
                      subtitle: adminDetails['phone'],
                      color: Colors.green,
                      onTap: () => _launchPhone(adminDetails['phone']),
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: Colors.grey.withOpacity(0.1),
                    ),
                    _buildContactButton(
                      icon: Icons.whatshot,
                      title: 'WhatsApp Support',
                      subtitle: adminDetails['whatsapp'],
                      color: Color(0xff25D366), // WhatsApp green
                      onTap: () => _launchWhatsApp(adminDetails['whatsapp']),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // FAQ section with enhanced design
              _buildSectionCard(
                title: 'Frequently Asked Questions',
                icon: Icons.question_answer,
                iconColor: Color(0xff5E469B),
                child: Column(
                  children: faqList.asMap().entries.map((entry) {
                    final index = entry.key;
                    final faq = entry.value;
                    return Container(
                      margin: EdgeInsets.only(
                        bottom: index < faqList.length - 1 ? 8 : 0,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ExpansionTile(
                        tilePadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        expandedCrossAxisAlignment: CrossAxisAlignment.start,
                        childrenPadding: EdgeInsets.zero,
                        title: Text(
                          faq['question'],
                          style: GoogleFonts.inter(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                        leading: Icon(
                          Icons.help_outline,
                          color: Color(0xff5E469B),
                        ),
                        children: [
                          Container(
                            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                            child: Text(
                              faq['answer'],
                              style: GoogleFonts.inter(
                                fontSize: 14,
                                color: Colors.black54,
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),

              const SizedBox(height: 24),

              // Support center information
              Center(
                child: Column(
                  children: [
                    Text(
                      'Need more help?',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Our support team is available 24/7',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () => _launchEmail(adminDetails['email']),
                      icon: Icon(Icons.email_outlined),
                      label: Text('Contact Us'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xffE65B00),
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    SizedBox(height: 32),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
    Color iconColor = Colors.black87,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 0,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: iconColor, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          Divider(height: 1, thickness: 1, color: Colors.grey.withOpacity(0.1)),
          Padding(padding: const EdgeInsets.all(16.0), child: child),
        ],
      ),
    );
  }

  Widget _buildContactButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  subtitle,
                  style: GoogleFonts.inter(fontSize: 12, color: Colors.black54),
                ),
              ],
            ),
            const Spacer(),
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.arrow_forward, size: 16, color: color),
            ),
          ],
        ),
      ),
    );
  }

  // Launch email app with improved handling
  void _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=Support Request&body=Hello MountVoq Support Team,',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        _showCopyDialog(email);
      }
    } catch (e) {
      print('Error launching email: $e');
      _showCopyDialog(email);
    }
  }

  // Launch phone app with improved handling for Android and iOS
  void _launchPhone(String phone) async {
    try {
      // Clean the phone number (remove spaces, etc.)
      final cleanedNumber = phone.replaceAll(RegExp(r'\s+'), '');

      if (Platform.isAndroid) {
        // For Android, use the intent package for better control
        final intent = android_intent.AndroidIntent(
          action: 'android.intent.action.DIAL',
          data: 'tel:$cleanedNumber',
        );
        await intent.launch();
      } else {
        // For iOS and other platforms
        final Uri launchUri = Uri(scheme: 'tel', path: cleanedNumber);
        if (await canLaunchUrl(launchUri)) {
          await launchUrl(launchUri);
        } else {
          throw 'Could not launch $launchUri';
        }
      }
    } catch (e) {
      print('Error launching phone dialer: $e');
      _showCopyDialog(phone);
    }
  }

  // Launch WhatsApp with improved handling
  void _launchWhatsApp(String phone) async {
    try {
      // Clean the phone number (remove spaces, +, etc.)
      final cleanedNumber = phone.replaceAll(RegExp(r'[^\d]'), '');

      // Create WhatsApp URL
      final whatsappUrl = 'https://wa.me/$cleanedNumber';
      final uri = Uri.parse(whatsappUrl);

      // Try different launch methods
      bool launched = false;

      // 1. Try using android_intent_plus for Android
      if (Platform.isAndroid) {
        try {
          final intent = android_intent.AndroidIntent(
            action: 'action_view',
            data: uri.toString(),
            package: 'com.whatsapp',
          );
          await intent.launch();
          print('Launched WhatsApp via Android Intent');
          return;
        } catch (e) {
          print('Android Intent for WhatsApp failed: $e');
          // Continue to other methods if this fails
        }
      }

      // 2. Try external application mode
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
        print('Launch result (external): $launched');

        if (launched) return;
      }

      // 3. Try platform default as fallback
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.platformDefault);
        print('Launch result (platform): $launched');

        if (!launched) {
          throw 'Could not launch WhatsApp';
        }
      } else {
        throw 'Could not launch WhatsApp';
      }
    } catch (e) {
      print('Error launching WhatsApp: $e');
      _showCopyDialog(phone);
    }
  }

  // Show dialog to copy contact info if launching fails
  void _showCopyDialog(String value) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cannot launch app'),
        content: Text('Would you like to copy "$value" to clipboard?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Clipboard.setData(ClipboardData(text: value));
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Copied to clipboard')),
              );
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }
}
