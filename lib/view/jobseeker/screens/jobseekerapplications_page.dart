import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/services/jobseeker_api_service.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:job/uttilits/api_constants.dart';
import 'package:job/view/jobseeker/screens/joining_requests_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class JobseekerapplicationPage extends StatefulWidget {
  const JobseekerapplicationPage({super.key});

  @override
  State<JobseekerapplicationPage> createState() =>
      _JobseekerapplicationPageState();
}

class _JobseekerapplicationPageState extends State<JobseekerapplicationPage> {
  List<Map<String, dynamic>> applications = [];
  List<Map<String, dynamic>> invitations = [];
  List<Map<String, dynamic>> requests = [];
  bool isLoading = true;

  // State variables for expanded sections
  bool _isPortalExpanded = false;
  bool _isInviteExpanded = false;
  bool _isRequestExpanded = false;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  Future<void> _fetchData() async {
    try {
      setState(() {
        isLoading = true;
      });

      List<Map<String, dynamic>> fetchedApplications = [];
      List<Map<String, dynamic>> fetchedInvitations = [];
      List<Map<String, dynamic>> fetchedSalonRequests = [];

      // Fetch data with individual try-catch blocks to handle failures gracefully
      try {
        fetchedApplications = await JobseekerApiService.getApplications();
        print('Fetched ${fetchedApplications.length} applications');
      } catch (e) {
        print('Error fetching applications: $e');
      }

      try {
        fetchedInvitations = await ApiService.getJobseekerInvitations();
        print('Fetched ${fetchedInvitations.length} invitations');
      } catch (e) {
        print('Error fetching invitations: $e');
      }

      // Use the direct API endpoint for salon requests
      try {
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('token');

        if (token == null) {
          throw Exception('Authentication token not found');
        }

        final response = await http.get(
          Uri.parse('${ApiConstants.baseUrl}/salon-requests-list/'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Token $token',
          },
        );

        if (response.statusCode == 200) {
          fetchedSalonRequests =
              List<Map<String, dynamic>>.from(json.decode(response.body));
          print('Fetched ${fetchedSalonRequests.length} salon requests');
        } else {
          throw Exception(
              'Failed to load salon requests: ${response.statusCode}');
        }
      } catch (e) {
        print('Error fetching salon requests: $e');
      }

      // Transform applications data
      final transformedApplications = fetchedApplications.map((app) {
        final applicantDetails = app['applicant_details'] ?? {};

        return {
          'id': app['id'],
          'jobTitle': app['job_title'] ?? 'Unknown Position',
          'company': app['salon_name'] ??
              'Salon', // Try to get salon name if available
          'location': applicantDetails['place'] ?? 'Unknown',
          'status': _mapApiStatusToUiStatus(app['status'] ?? 'PENDING'),
          'statusColor': _getStatusColor(app['status'] ?? 'PENDING'),
          'nextStep': _getNextStep(app['status'] ?? 'PENDING'),
          'contactNumber': applicantDetails['contact_no'] ?? 'No contact info',
          'source': 'Job Portal',
          'appliedDate':
              DateTime.parse(app['applied_on'] ?? DateTime.now().toString()),
          'originalData': app, // Keep original data for reference
        };
      }).toList();

      // Transform invitations data
      final transformedInvitations = fetchedInvitations.map((invite) {
        final salonDetails = invite['salon_details'] ?? {};

        return {
          'id': invite['id'],
          'jobTitle': 'Salon Invitation', // Generic title for invitations
          'company': invite['salon_name'] ?? 'Unknown Salon',
          'location': salonDetails['location'] ?? 'Unknown',
          'status': _mapApiStatusToUiStatus(invite['status'] ?? 'PENDING'),
          'statusColor': _getStatusColor(invite['status'] ?? 'PENDING'),
          'nextStep': _getNextStepForInvitation(invite['status'] ?? 'PENDING'),
          'contactNumber': salonDetails['contact_no'] ?? 'No contact info',
          'message': invite['message'] ?? 'No message provided',
          'source': 'Invite',
          'appliedDate':
              DateTime.parse(invite['created_at'] ?? DateTime.now().toString()),
          'originalData': invite, // Keep original data for reference
        };
      }).toList();

      // Transform requests data
      final transformedRequests = fetchedSalonRequests.map((request) {
        final salonDetails = request['salon_details'] ?? {};

        return {
          'id': request['id'],
          'jobTitle': 'Job Request', // Generic title for requests
          'company': request['salon_name'] ?? 'Unknown Salon',
          'location': salonDetails['location'] ?? 'Unknown',
          'status': _mapApiStatusToUiStatus(request['status'] ?? 'PENDING'),
          'statusColor': _getStatusColor(request['status'] ?? 'PENDING'),
          'nextStep': _getNextStepForRequest(request['status'] ?? 'PENDING'),
          'contactNumber': salonDetails['contact_no'] ?? 'No contact info',
          'message': request['message'] ?? 'No message provided',
          'source': 'Request',
          'appliedDate': DateTime.parse(
              request['created_at'] ?? DateTime.now().toString()),
          'originalData': request, // Keep original data for reference
        };
      }).toList();

      // Transform salon requests data
      final transformedSalonRequests = fetchedSalonRequests.map((request) {
        return {
          'id': request['id'],
          'jobTitle': 'Salon Request', // Generic title for salon requests
          'company': request['salon_name'] ?? 'Unknown Salon',
          'location': request['salon_location'] ?? 'Unknown',
          'status': 'Pending', // Default status since API doesn't provide it
          'statusColor': Colors.orange,
          'nextStep': 'Waiting for salon response',
          'message': request['message'] ?? 'No message provided',
          'source': 'Request',
          'appliedDate': DateTime.parse(
              request['created_at'] ?? DateTime.now().toString()),
          'originalData': request, // Keep original data for reference
        };
      }).toList();

      setState(() {
        applications = transformedApplications;
        invitations = transformedInvitations;
        requests = transformedSalonRequests;
        isLoading = false;
      });
    } catch (e) {
      print('Error in _fetchData: $e');
      setState(() {
        isLoading = false;
      });

      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load data. Please try again.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper method to get next step for invitations
  String _getNextStepForInvitation(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Respond to invitation';
      case 'ACCEPTED':
        return 'Contact salon for interview';
      case 'REJECTED':
        return 'Invitation declined';
      default:
        return 'Respond to invitation';
    }
  }

  // Helper method to get next step for requests
  String _getNextStepForRequest(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Waiting for salon response';
      case 'ACCEPTED':
        return 'Request accepted, contact salon';
      case 'REJECTED':
        return 'Request declined by salon';
      default:
        return 'Waiting for salon response';
    }
  }

  // Helper method to map API status to UI status
  String _mapApiStatusToUiStatus(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'ACCEPTED':
        return 'Accepted';
      case 'REJECTED':
        return 'Rejected';
      default:
        return 'Pending';
    }
  }

  // Helper method to get status color
  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'ACCEPTED':
        return Colors.green;
      case 'REJECTED':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  // Helper method to get next step based on status
  String _getNextStep(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Application under review';
      case 'ACCEPTED':
        return 'Contact employer for next steps';
      case 'REJECTED':
        return 'Application not selected';
      default:
        return 'Application under review';
    }
  }

  // Add helper method for salon request next steps
  String _getNextStepForSalonRequest(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Salon request pending';
      case 'ACCEPTED':
        return 'Request accepted by salon';
      case 'REJECTED':
        return 'Request declined';
      default:
        return 'Salon request pending';
    }
  }

  // Add helper method for joining request next steps
  String _getNextStepForJoiningRequest(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return 'Review job offer from salon';
      case 'ACCEPTED':
        return 'Offer accepted, contact salon to join';
      case 'REJECTED':
        return 'Offer declined';
      default:
        return 'Review job offer from salon';
    }
  }

  // Call function to launch phone dialer
  Future<void> _callNumber(String phoneNumber) async {
    final Uri url = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch dialer')),
      );
    }
  }

  // Add delete method for salon requests
  Future<void> _deleteSalonRequest(int requestId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.delete(
        Uri.parse('${ApiConstants.baseUrl}/salon-requests/$requestId/delete/'),
        headers: {
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        // Remove from local state
        setState(() {
          requests.removeWhere((request) => request['id'] == requestId);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Request deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Failed to delete request: ${response.statusCode}');
      }
    } catch (e) {
      print('Error deleting salon request: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete request: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Count applications by status (combining both applications and invitations)
    final allItems = [...applications, ...invitations, ...requests];
    final pendingCount =
        allItems.where((item) => item['status'] == 'Pending').length;
    final acceptedCount =
        allItems.where((item) => item['status'] == 'Accepted').length;
    final rejectedCount =
        allItems.where((item) => item['status'] == 'Rejected').length;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Requests & Alerts',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _fetchData,
              child: CustomScrollView(
                slivers: [
                  // Custom App Bar with Statistics
                  SliverToBoxAdapter(
                    child: Container(
                      padding: EdgeInsets.fromLTRB(20, 30, 20, 30),
                      decoration: BoxDecoration(
                        color: ColorConstants.black,
                        borderRadius: const BorderRadius.only(),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${allItems.length} Total Applications',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 25),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatCard('$pendingCount', 'Pending',
                                  Icons.pending_outlined),
                              _buildStatCard(
                                '$acceptedCount',
                                'Accepted',
                                Icons.check_circle_outline,
                              ),
                              _buildStatCard('$rejectedCount', 'Rejected',
                                  Icons.cancel_outlined),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Applications via Job Portal
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                      child: _buildApplicationSection(
                        title: 'Via Job Portal',
                        applications: applications,
                        isExpanded: _isPortalExpanded,
                        onToggleExpanded: () {
                          setState(() {
                            _isPortalExpanded = !_isPortalExpanded;
                          });
                        },
                      ),
                    ),
                  ),

                  // Applications via Invite
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                      child: _buildApplicationSection(
                        title: 'Via Invite',
                        applications: invitations,
                        isExpanded: _isInviteExpanded,
                        showActionButtons: true,
                        onToggleExpanded: () {
                          setState(() {
                            _isInviteExpanded = !_isInviteExpanded;
                          });
                        },
                        onAccept: (invitation) async {
                          try {
                            await ApiService.updateInvitationStatus(
                              invitationId: invitation['id'],
                              status: 'accepted',
                            );

                            setState(() {
                              // Update the local state
                              final index = invitations.indexOf(invitation);
                              if (index != -1) {
                                invitations[index]['status'] = 'Accepted';
                                invitations[index]['statusColor'] =
                                    Colors.green;
                                invitations[index]['nextStep'] =
                                    'Contact salon for interview';
                              }
                            });

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Invitation accepted'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Failed to accept invitation: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        onReject: (invitation) async {
                          try {
                            await ApiService.updateInvitationStatus(
                              invitationId: invitation['id'],
                              status: 'rejected',
                            );

                            setState(() {
                              // Update the local state
                              final index = invitations.indexOf(invitation);
                              if (index != -1) {
                                invitations[index]['status'] = 'Rejected';
                                invitations[index]['statusColor'] = Colors.red;
                                invitations[index]['nextStep'] =
                                    'Invitation declined';
                              }
                            });

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Invitation rejected'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Failed to reject invitation: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ),

                  // Applications via Request
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                      child: _buildApplicationSection(
                        title: 'Via Request',
                        applications: requests,
                        isExpanded: _isRequestExpanded,
                        onToggleExpanded: () {
                          setState(() {
                            _isRequestExpanded = !_isRequestExpanded;
                          });
                        },
                      ),
                    ),
                  ),

                  // Joining Requests Button
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(20),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const JoiningRequestsScreen(),
                                ),
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Container(
                                    width: 50,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: Colors.blue.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.business_center,
                                      color: ColorConstants.black,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Joining Requests',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'View and respond to salon joining offers',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.grey[400],
                                    size: 16,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Add some padding at the bottom
                  SliverToBoxAdapter(
                    child: SizedBox(height: 20),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildStatCard(String number, String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 24),
          const SizedBox(height: 8),
          Text(
            number,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationCard(
    BuildContext context,
    Map<String, dynamic> application, {
    bool showActionButtons = false,
    Function(Map<String, dynamic>)? onAccept,
    Function(Map<String, dynamic>)? onReject,
  }) {
    // Format the applied date
    final formattedDate =
        DateFormat('MMM d, yyyy').format(application['appliedDate']);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            // Show application details in a dialog
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text('Application for ${application['jobTitle']}'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Expertise Area: ${application['specialization_name'] ?? 'N/A'}',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(application['jobTitle']),
                      SizedBox(height: 16),
                      Text(
                        'Company:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(application['company']),
                      SizedBox(height: 16),
                      Text(
                        'Contact Number:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(application['contactNumber']),
                      SizedBox(height: 16),
                      Text(
                        'Location:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(application['location'] ?? 'Location not provided'),
                      SizedBox(height: 16),
                      Text(
                        'Status:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(application['status']),
                      SizedBox(height: 8),
                      Text(application['nextStep']),
                      SizedBox(height: 16),
                      Text(
                        'Applied on:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(formattedDate),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Close'),
                  ),
                ],
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getSalonJobIcon(application['jobTitle']),
                        color: ColorConstants.black,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                application['jobTitle'],
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            application['company'],
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Applied on $formattedDate',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildStatusChip(
                          application['status'],
                          application['statusColor'],
                        ),
                        if (application['contactNumber'] != null &&
                            application['contactNumber'] != 'No contact info' &&
                            application['contactNumber'].toString().isNotEmpty)
                          IconButton(
                            onPressed: () {
                              _callNumber(application['contactNumber']);
                            },
                            icon: CircleAvatar(
                              backgroundColor: Colors.green.withOpacity(.2),
                              child: Icon(
                                Icons.add_call,
                                color: Colors.green[600],
                                size: 20,
                              ),
                            ),
                            tooltip: 'Call ${application['contactNumber']}',
                          ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Text(
                    application['nextStep'],
                    style: TextStyle(
                      color: application['statusColor'],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  // Helper method to get appropriate icon for salon job
  IconData _getSalonJobIcon(String jobTitle) {
    jobTitle = jobTitle.toLowerCase();
    if (jobTitle.contains('hair') || jobTitle.contains('stylist')) {
      return Icons.content_cut;
    } else if (jobTitle.contains('makeup') || jobTitle.contains('beauty')) {
      return Icons.face;
    } else if (jobTitle.contains('massage') || jobTitle.contains('spa')) {
      return Icons.spa;
    } else if (jobTitle.contains('nail')) {
      return Icons.brush;
    } else if (jobTitle.contains('manager') || jobTitle.contains('admin')) {
      return Icons.business;
    } else {
      return Icons.work;
    }
  }

  Widget _buildApplicationSection({
    required String title,
    required List<Map<String, dynamic>> applications,
    required bool isExpanded,
    Function()? onToggleExpanded,
    bool showActionButtons = false,
    Function(Map<String, dynamic>)? onAccept,
    Function(Map<String, dynamic>)? onReject,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: onToggleExpanded,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$title (${applications.length})',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                  if (title == 'Via Job Portal')
                    Text(
                      "Applications submitted through job posts",
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  if (title == 'Via Invite')
                    Text(
                      "Direct invitations received from salons",
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  if (title == 'Via Request')
                    Text(
                      "Requests sent to salons for job opportunities",
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
              Icon(
                isExpanded
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey,
              ),
            ],
          ),
        ),
        SizedBox(height: 10),
        if (isExpanded)
          applications.isEmpty
              ? Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No ${title.toLowerCase()} found',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Column(
                  children: applications.map((application) {
                    // Use different card types based on the source
                    if (title == 'Via Invite') {
                      return _buildInvitationCard(
                        context,
                        application,
                        showActionButtons: showActionButtons &&
                            application['status'] == 'Pending',
                        onAccept: onAccept,
                        onReject: onReject,
                      );
                    } else if (title == 'Via Request') {
                      return _buildRequestCard(
                        context,
                        application,
                      );
                    } else {
                      return _buildApplicationCard(
                        context,
                        application,
                      );
                    }
                  }).toList(),
                ),
      ],
    );
  }

  Widget _buildInvitationCard(
    BuildContext context,
    Map<String, dynamic> invitation, {
    bool showActionButtons = false,
    Function(Map<String, dynamic>)? onAccept,
    Function(Map<String, dynamic>)? onReject,
  }) {
    // Format the invitation date
    final formattedDate =
        DateFormat('MMM d, yyyy').format(invitation['appliedDate']);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            // Show invitation details in a dialog
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text('Invitation from ${invitation['company']}'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Message:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(invitation['message'] ?? 'No message provided'),
                      SizedBox(height: 16),
                      Text(
                        'Contact Information:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('Phone: ${invitation['contactNumber']}'),
                      if (invitation['originalData']?['salon_details']
                              ?['email'] !=
                          null)
                        Text(
                            'Email: ${invitation['originalData']['salon_details']['email']}'),
                      SizedBox(height: 16),
                      Text(
                        'Location:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(invitation['location'] ?? 'Location not provided'),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Close'),
                  ),
                ],
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.store,
                        color: ColorConstants.black,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                invitation['company'],
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              // Call button at the top
                              if (invitation['contactNumber'] != null &&
                                  invitation['contactNumber'] !=
                                      'No contact info' &&
                                  invitation['contactNumber']
                                      .toString()
                                      .isNotEmpty)
                                IconButton(
                                  onPressed: () {
                                    _callNumber(invitation['contactNumber']);
                                  },
                                  icon: CircleAvatar(
                                    backgroundColor:
                                        Colors.green.withOpacity(.2),
                                    child: Icon(
                                      Icons.add_call,
                                      color: Colors.green[600],
                                      size: 20,
                                    ),
                                  ),
                                  tooltip:
                                      'Call ${invitation['contactNumber']}',
                                ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            invitation['location'] ?? 'Location not available',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Received on $formattedDate',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.phone,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                invitation['contactNumber'] ??
                                    'No contact info',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (invitation['message'] != null &&
                    invitation['message'].isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      invitation['message'],
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                if (showActionButtons && invitation['status'] == 'Pending')
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Row(
                      children: [
                        // Expanded(
                        //   child: OutlinedButton(
                        //     onPressed: () {
                        //       if (onReject != null) {
                        //         onReject(invitation);
                        //       }
                        //     },
                        //     style: OutlinedButton.styleFrom(
                        //       foregroundColor: Colors.red,
                        //       side: const BorderSide(color: Colors.red),
                        //       shape: RoundedRectangleBorder(
                        //         borderRadius: BorderRadius.circular(8),
                        //       ),
                        //     ),
                        //     child: const Text('Decline'),
                        //   ),
                        // ),
                        // const SizedBox(width: 12),
                        // Expanded(
                        //   child: ElevatedButton(
                        //     onPressed: () {
                        //       if (onAccept != null) {
                        //         onAccept(invitation);
                        //       }
                        //     },
                        //     style: ElevatedButton.styleFrom(
                        //       backgroundColor: Colors.green,
                        //       foregroundColor: Colors.white,
                        //       shape: RoundedRectangleBorder(
                        //         borderRadius: BorderRadius.circular(8),
                        //       ),
                        //     ),
                        //     child: const Text('Accept'),
                        //   ),
                        // ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRequestCard(
    BuildContext context,
    Map<String, dynamic> request,
  ) {
    // Format the request date
    final formattedDate =
        DateFormat('MMM d, yyyy').format(request['appliedDate']);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            // Show request details in a dialog
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text('Request to ${request['company']}'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Message:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(request['message'] ?? 'No message provided'),
                      SizedBox(height: 16),
                      if (request['source'] == 'Joining Request' &&
                          request['salary'] != null) ...[
                        Text(
                          'Offered Salary:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 8),
                        Text('₹${request['salary']}/month'),
                        SizedBox(height: 16),
                      ],
                      Text(
                        'Contact Information:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('Phone: ${request['contactNumber']}'),
                      if (request['originalData']?['salon_details']?['email'] !=
                          null)
                        Text(
                            'Email: ${request['originalData']['salon_details']['email']}'),
                      SizedBox(height: 16),
                      Text(
                        'Location:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(request['location'] ?? 'Location not provided'),
                      SizedBox(height: 16),
                      Text(
                        'Status:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(request['status']),
                      SizedBox(height: 8),
                      Text(request['nextStep']),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Close'),
                  ),
                ],
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.send,
                        color: ColorConstants.black,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            request['company'],
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            request['source'] == 'Joining Request'
                                ? '${request['jobTitle']} • ₹${request['salary']}/month'
                                : request['location'] ??
                                    'Location not available',
                            style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today_outlined,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'Sent on $formattedDate',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 13,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    _buildStatusChip(
                      request['status'],
                      request['statusColor'],
                    ),
                  ],
                ),
                if (request['message'] != null && request['message'].isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      request['message'],
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Text(
                    request['nextStep'],
                    style: TextStyle(
                      color: request['statusColor'],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                // Show call button for accepted requests
                if (request['status'] == 'Accepted' &&
                    request['contactNumber'] != null &&
                    request['contactNumber'] != 'No contact info' &&
                    request['contactNumber'].toString().isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () {
                            _callNumber(request['contactNumber']);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          icon: const Icon(Icons.phone, size: 16),
                          label: const Text('Call'),
                        ),
                      ],
                    ),
                  ),
                // Add delete button for requests
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton.icon(
                        onPressed: () {
                          // Show confirmation dialog
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Delete Request'),
                              content: const Text(
                                'Are you sure you want to delete this request? This action cannot be undone.',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Cancel'),
                                ),
                                ElevatedButton(
                                  onPressed: () {
                                    Navigator.pop(context);
                                    _deleteSalonRequest(request['id']);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('Delete'),
                                ),
                              ],
                            ),
                          );
                        },
                        style: OutlinedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        icon: const Icon(Icons.delete_outline, size: 12),
                        label: const Text(
                          'Delete Request',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
