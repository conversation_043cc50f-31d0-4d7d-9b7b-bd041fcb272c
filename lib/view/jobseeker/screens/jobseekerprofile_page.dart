import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/uttilits/work_status_manager.dart';
import 'package:job/view/common/welcome_screen/welcome_screen.dart';
import 'package:job/view/jobseeker/details/profile_details_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:job/services/api_service.dart';

class JobseekerprofilePage extends StatefulWidget {
  const JobseekerprofilePage({super.key});

  @override
  State<JobseekerprofilePage> createState() => _JobseekerprofilePageState();
}

class _JobseekerprofilePageState extends State<JobseekerprofilePage> {
  bool _showTitle = false;
  final ScrollController _scrollController = ScrollController();
  final WorkStatusManager _workStatusManager = WorkStatusManager();
  File? _profileImage;
  static const String _profileImagePathKey = 'profile_image_path';
  Map<String, dynamic> _profileData = {};

  // Add these controllers to store profile data
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _ageController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _placeController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _experienceController = TextEditingController();
  final TextEditingController _socialLinkController = TextEditingController();
  final TextEditingController _aboutController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadProfileImage();
    _fetchProfileData();
  }

  Future<void> _loadProfileImage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final imagePath = prefs.getString(_profileImagePathKey);

      if (imagePath != null) {
        final file = File(imagePath);
        if (await file.exists()) {
          setState(() {
            _profileImage = file;
          });
        }
      }
    } catch (e) {
      print('Error loading profile image: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _phoneController.dispose();
    _placeController.dispose();
    _emailController.dispose();
    _dobController.dispose();
    _experienceController.dispose();
    _socialLinkController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 180 && !_showTitle) {
      setState(() => _showTitle = true);
    } else if (_scrollController.offset <= 180 && _showTitle) {
      setState(() => _showTitle = false);
    }
  }

  void _setCurrentlyWorking() async {
    try {
      final result = await ApiService.toggleJobseekerWorkStatus();

      if (result['is_employee'] == true) {
        _workStatusManager.setWorkStatus(true);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result['message'] ??
                  'You are now working. Jobs and Applications are disabled.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.black,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(10),
          ),
        );
      }
    } catch (e) {
      print('Error setting work status: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update work status: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _setOpenToWork() async {
    try {
      final result = await ApiService.toggleJobseekerWorkStatus();

      if (result['is_employee'] == false) {
        _workStatusManager.setWorkStatus(false);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result['message'] ??
                  'You are now open to work. All features are enabled.',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.black,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            margin: const EdgeInsets.all(10),
          ),
        );
      }
    } catch (e) {
      print('Error setting work status: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update work status: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.logout, color: Colors.redAccent),
              ),
              const SizedBox(width: 12),
              Text(
                'Logout',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          content: Text(
            'Are you sure you want to logout from your account?',
            style: GoogleFonts.poppins(fontSize: 15, color: Colors.black54),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  // Set login status to false
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.setBool('is_logged_in', false);

                  // Close the dialog
                  Navigator.pop(dialogContext);

                  // Navigate to welcome screen
                  Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const WelcomeScreen(),
                    ),
                    (route) => false,
                  );
                } catch (e) {
                  print('Error during logout: $e');
                  // Show error message if logout fails
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Logout failed: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.redAccent,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
              ),
              child: Text(
                'Logout',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _fetchProfileData() async {
    try {
      setState(() {});

      final profileData = await ApiService.fetchJobseekerProfile();

      setState(() {
        _profileData = profileData;

        // Update controllers with fetched data
        _nameController.text = profileData['name'] ?? '';
        _ageController.text = profileData['age']?.toString() ?? '';
        _phoneController.text = profileData['contact_no'] ?? '';
        _placeController.text = profileData['place'] ?? '';
        _emailController.text = profileData['email'] ?? '';
        _dobController.text = profileData['date_of_birth'] ?? '';
        _experienceController.text = profileData['years_of_experience'] ?? '';
        _socialLinkController.text = profileData['social_link'] ?? '';
        _aboutController.text = profileData['about'] ?? '';

        // Set work status
        if (profileData['work_status'] == 'WORKING') {
          _workStatusManager.setWorkStatus(true);
        } else {
          _workStatusManager.setWorkStatus(false);
        }
      });
    } catch (e) {
      print('Error fetching profile data: $e');
      setState(() {});
    }
  }

  // Helper function to format experience from decimal to "Xy Ym" format
  // Treats decimal part directly as months (e.g., 46.11 = 46y 11m)
  String _formatExperience(dynamic experience) {
    if (experience == null || experience.toString().isEmpty) return '0y 0m';

    try {
      double expValue;

      // Handle different input types
      if (experience is String) {
        expValue = double.parse(experience);
      } else if (experience is int) {
        expValue = experience.toDouble();
      } else if (experience is double) {
        expValue = experience;
      } else {
        expValue = double.parse(experience.toString());
      }

      int years = expValue.floor();
      // Extract decimal part and convert to months (treat decimal directly as months)
      double decimalPart = expValue - years;
      int months = (decimalPart * 100)
          .round(); // Multiply by 100 to get the decimal as whole number

      // Ensure months is between 0 and 99 (since we're treating .11 as 11 months)
      if (months < 0) months = 0;
      if (months > 99) {
        years += 1;
        months = 0;
      }

      return '${years}y ${months.toString().padLeft(2, '0')}m';
    } catch (e) {
      print('Error formatting experience: $e, value: $experience');
      return '0y 0m';
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Modern App Bar with Gradient
          SliverAppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new,
                  color: Colors.white, size: 20),
              onPressed: () => Navigator.pop(context),
            ),
            expandedHeight: size.height * 0.30,
            floating: false,
            pinned: true,
            stretch: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            title: _showTitle
                ? Text(
                    _nameController.text.isNotEmpty
                        ? _nameController.text
                        : 'Profile',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                : null,
            flexibleSpace: FlexibleSpaceBar(
              stretchModes: const [
                StretchMode.zoomBackground,
                StretchMode.blurBackground,
              ],
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.black,
                      Colors.blueGrey,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned(
                      top: -50,
                      right: -50,
                      child: Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -30,
                      left: -30,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white.withOpacity(0.1),
                        ),
                      ),
                    ),

                    // Profile Content
                    Padding(
                      padding: const EdgeInsets.only(top: 10, left: 115),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Profile Image with modern design
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 4,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  spreadRadius: 5,
                                  blurRadius: 15,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: CircleAvatar(
                              radius: size.width * 0.12,
                              backgroundImage: _profileData.isNotEmpty &&
                                      _profileData['profile_picture'] != null
                                  ? NetworkImage(
                                      _profileData['profile_picture'])
                                  : _profileImage != null
                                      ? FileImage(_profileImage!)
                                          as ImageProvider
                                      : const NetworkImage(
                                          "https://img.icons8.com/ios-filled/50/user.png"),
                              backgroundColor: Colors.grey[200],
                            ),
                          ),
                          const SizedBox(height: 20),

                          // Name with modern typography
                          Text(
                            _nameController.text.isNotEmpty
                                ? _nameController.text
                                : 'Your Name',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Email with subtle styling
                          Text(
                            _emailController.text.isNotEmpty
                                ? _emailController.text
                                : '<EMAIL>',
                            style: GoogleFonts.poppins(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Work status indicator
                          ValueListenableBuilder<bool>(
                            valueListenable: _workStatusManager.isWorking,
                            builder: (context, isWorking, child) {
                              return Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: isWorking
                                      ? Colors.green.withOpacity(0.2)
                                      : Colors.blue.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color:
                                        isWorking ? Colors.green : Colors.blue,
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      isWorking
                                          ? Icons.work
                                          : Icons.work_outline,
                                      color: isWorking
                                          ? Colors.green
                                          : Colors.blue,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      isWorking
                                          ? 'Currently Working'
                                          : 'Open to Work',
                                      style: GoogleFonts.poppins(
                                        color: isWorking
                                            ? Colors.green
                                            : Colors.blue,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Profile Stats Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 20, 16, 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  _buildStatItem(
                    icon: Icons.location_on_outlined,
                    title: 'Location',
                    value: _placeController.text.isNotEmpty
                        ? _placeController.text
                        : 'Not set',
                    color: Colors.blue,
                  ),
                  Container(
                    width: 1,
                    height: 50,
                    color: Colors.grey.withOpacity(0.3),
                  ),
                  _buildStatItem(
                    icon: Icons.work_outline,
                    title: 'Experience',
                    value: _experienceController.text.isNotEmpty
                        ? _formatExperience(_experienceController.text)
                        : 'Not set',
                    color: Colors.orange,
                  ),
                  Container(
                    width: 1,
                    height: 50,
                    color: Colors.grey.withOpacity(0.3),
                  ),
                  _buildStatItem(
                    icon: Icons.phone_outlined,
                    title: 'Contact',
                    value: _phoneController.text.isNotEmpty
                        ? _phoneController.text
                        : 'Not set',
                    color: Colors.green,
                  ),
                ],
              ),
            ),
          ),

          // About Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.purple.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.person_outline,
                          color: Colors.purple,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'About Me',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _profileData['about']?.isNotEmpty == true
                        ? _profileData['about']
                        : 'No information provided. Add some details about yourself to help others get to know you better.',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.black54,
                      height: 1.6,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Quick Actions Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.indigo.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.settings_outlined,
                          color: Colors.indigo,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Quick Actions',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),

                  // Edit Profile Button
                  _buildActionButton(
                    icon: Icons.edit_outlined,
                    title: 'Edit Profile',
                    subtitle: 'Update your personal information',
                    color: Colors.blue,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ProfileDetailsScreen(),
                        ),
                      ).then((_) {
                        setState(() {
                          _fetchProfileData();
                        });
                      });
                    },
                  ),

                  const SizedBox(height: 12),

                  // Work Status Toggle
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.grey.withOpacity(0.1),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            Icons.work_outline,
                            color: Colors.green,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Work Status',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 2),
                              ValueListenableBuilder<bool>(
                                valueListenable: _workStatusManager.isWorking,
                                builder: (context, isWorking, child) {
                                  return Text(
                                    isWorking
                                        ? 'Currently Working'
                                        : 'Open to Work',
                                    style: GoogleFonts.poppins(
                                      fontSize: 13,
                                      color: isWorking
                                          ? Colors.green
                                          : Colors.blue,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        ValueListenableBuilder<bool>(
                          valueListenable: _workStatusManager.isWorking,
                          builder: (context, isWorking, child) {
                            return Switch(
                              value: isWorking,
                              onChanged: (value) {
                                if (value) {
                                  _setCurrentlyWorking();
                                } else {
                                  _setOpenToWork();
                                }
                              },
                              activeColor: Colors.green,
                              inactiveThumbColor: Colors.blue,
                              activeTrackColor: Colors.green.withOpacity(0.3),
                              inactiveTrackColor: Colors.blue.withOpacity(0.3),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Logout Section
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 0, 16, 30),
              child: _buildActionButton(
                icon: Icons.logout,
                title: 'Logout',
                subtitle: 'Sign out from your account',
                color: Colors.red,
                onTap: () => _handleLogout(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 11,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color == Colors.red
              ? Colors.red.withOpacity(0.05)
              : Colors.grey.withOpacity(0.05),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color == Colors.red
                ? Colors.red.withOpacity(0.2)
                : Colors.grey.withOpacity(0.1),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }
}
