import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:job/services/api_service.dart';
import 'package:job/uttilits/color_const.dart';

class JoiningRequestsScreen extends StatefulWidget {
  const JoiningRequestsScreen({Key? key}) : super(key: key);

  @override
  State<JoiningRequestsScreen> createState() => _JoiningRequestsScreenState();
}

class _JoiningRequestsScreenState extends State<JoiningRequestsScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _joiningRequests = [];

  @override
  void initState() {
    super.initState();
    _fetchJoiningRequests();
  }

  Future<void> _fetchJoiningRequests() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final requests = await ApiService.getJoiningRequestsList();
      setState(() {
        _joiningRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      print('Error fetching joining requests: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load joining requests: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _respondToRequest(int requestId, bool accept) async {
    try {
      await ApiService.respondToJoiningRequest(
        requestId: requestId,
        accept: accept,
      );

      // Update the local state
      setState(() {
        final index =
            _joiningRequests.indexWhere((req) => req['id'] == requestId);
        if (index != -1) {
          _joiningRequests[index]['status'] = accept ? 'ACCEPTED' : 'REJECTED';
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(accept ? 'Request accepted' : 'Request rejected'),
            backgroundColor: accept ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to respond to request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'ACCEPTED':
        return Colors.green;
      case 'REJECTED':
        return Colors.red;
      case 'PENDING':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Joining Requests',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.black),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _fetchJoiningRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _joiningRequests.isEmpty
              ? Center(
                  child: Text(
                    'No joining requests found',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _joiningRequests.length,
                  itemBuilder: (context, index) {
                    final request = _joiningRequests[index];
                    final formattedDate = DateFormat('MMM d, yyyy').format(
                      DateTime.parse(request['created_at']),
                    );
                    final isPending =
                        request['status'].toString().toUpperCase() == 'PENDING';

                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: ColorConstants.PRIMARYCOLOR,
                                  child: Text(
                                    request['salon_name'][0],
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        request['salon_name'],
                                        style: GoogleFonts.inter(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        'Sent on $formattedDate',
                                        style: GoogleFonts.inter(
                                          color: Colors.grey[600],
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(request['status']),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    request['status'],
                                    style: GoogleFonts.inter(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Position: ${request['job_title']}',
                              style: GoogleFonts.inter(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              'Salary: ₹${request['salary']}',
                              style: GoogleFonts.inter(),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Message:',
                              style: GoogleFonts.inter(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              request['message'] ?? 'No message provided',
                              style: GoogleFonts.inter(),
                            ),
                            if (isPending) ...[
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () => _respondToRequest(
                                          request['id'], false),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      child: const Text('Reject'),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: ElevatedButton(
                                      onPressed: () => _respondToRequest(
                                          request['id'], true),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      child: const Text('Accept'),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}
