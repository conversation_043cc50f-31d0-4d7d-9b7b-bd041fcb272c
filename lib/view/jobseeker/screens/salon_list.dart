import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/uttilits/api_constants.dart';
import 'package:job/view/jobseeker/details/salon_details_screen.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class SalonList extends StatefulWidget {
  const SalonList({super.key});

  @override
  State<SalonList> createState() => _SalonListState();
}

class _SalonListState extends State<SalonList> {
  final TextEditingController _searchController = TextEditingController();
  String selectedLocation = 'All';
  String selectedSalonType = 'All';
  bool showOnlyWithContact = false;
  bool isLoading = true;
  List<Map<String, dynamic>> salonsList = [];

  // Locations list will be populated from API data
  List<String> locations = ['All'];
  List<String> salonTypes = ['All', 'MALE', 'FEMALE', 'UNISEX'];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        // This will trigger a rebuild with the new search results
      });
    });
    _fetchSalons();
  }

  Future<void> _fetchSalons() async {
    setState(() {
      isLoading = true;
    });

    try {
      print('Fetching salons from: ${ApiConstants.salonsForJobseekers}');

      // Get token from SharedPreferences if available
      String? token;
      try {
        final prefs = await SharedPreferences.getInstance();
        token = prefs.getString('token');
      } catch (e) {
        print('Error getting token: $e');
      }

      // Prepare headers with or without token
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
      };

      // Add token if available
      if (token != null) {
        headers['Authorization'] = 'Token $token';
      }

      final response = await http.get(
        Uri.parse(ApiConstants.salonsForJobseekers),
        headers: headers,
      );

      print('Salon API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // Rest of your code for handling successful response
        final List<dynamic> data = jsonDecode(response.body);
        print('Successfully decoded JSON. Found ${data.length} salons');

        // Debug: Print first salon data
        if (data.isNotEmpty) {
          print('=== FIRST SALON DATA DEBUG ===');
          print('Raw salon data: ${data.first}');
          print('Salon name from API: ${data.first['salon_name']}');
          print('Profile picture from API: ${data.first['profile_picture']}');
          print('Salon images from API: ${data.first['salon_images']}');
          print('=== END FIRST SALON DEBUG ===');
        }

        // Clear locations list and add 'All' option
        locations = ['All'];

        final List<Map<String, dynamic>> salons = [];

        for (var salon in data) {
          // Extract unique locations for filter
          if (!locations.contains(salon['location'])) {
            locations.add(salon['location']);
          }

          final salonData = {
            'id': salon['id'],
            'name': salon['salon_name'] ?? 'Unknown Salon',
            'owner_name': salon['owner_name'] ?? 'Unknown Owner',
            'location': salon['location'] ?? 'Unknown Location',
            'specialties': salon['salon_type'] != null
                ? [salon['salon_type']]
                : ['General'],
            'image': salon['profile_picture'],
            'openPositions': 1, // Default
            'contact_no': salon['contact_no'] ?? 'Contact not available',
            'email': salon['email'] ?? 'N/A',
            'unique_id': salon['unique_id'] ?? '',
            'salon_type': salon['salon_type'] ?? 'General',
            'google_map_link': salon['google_map_link'] ?? '',
            'pin_code': salon['pin_code'] ?? '',
            'state': salon['state'] ?? '',
            'about': salon['about'] ?? '',

            'profile_picture': salon['profile_picture'] ?? '',
            'salon_images': salon['salon_images'] ?? [],
            'salon_name': salon['salon_name'] ?? '',
            'created_at': salon['created_at'] ?? '',
            'updated_at': salon['updated_at'] ?? '',
            'is_approved': salon['is_approved'] ?? false,
          };

          // Validate required fields
          if (salon['contact_no'] == null ||
              salon['contact_no'].toString().trim().isEmpty) {
            print(
                'Warning: Salon ${salon['salon_name']} has no contact number');
            // You can choose to skip this salon or handle it differently
            // For now, we'll include it but mark it as unavailable
            salonData['contact_no'] = 'Contact not available';
          }

          // Debug: Print processed salon data
          print('=== PROCESSED SALON DATA ===');
          print('Salon name: ${salonData['salon_name']}');
          print('Profile picture: ${salonData['profile_picture']}');
          print('Salon images: ${salonData['salon_images']}');
          print('=== END PROCESSED DATA ===');

          salons.add(salonData);
        }

        setState(() {
          salonsList = salons;
          isLoading = false;
        });

        print('Successfully processed ${salons.length} salons');
      } else if (response.statusCode == 401) {
        print('Authentication error: Unauthorized access');
        setState(() {
          isLoading = false;
        });
        // You might want to handle this by showing a login prompt
      } else {
        print('Failed to load salons: ${response.statusCode}');
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching salons: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  List<Map<String, dynamic>> get filteredSalons {
    List<Map<String, dynamic>> results = List.from(salonsList);

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      results = results.where((salon) {
        final name = salon['name'].toString().toLowerCase();
        final location = salon['location'].toString().toLowerCase();
        final specialties = (salon['specialties'] as List)
            .map((s) => s.toString().toLowerCase())
            .toList();

        return name.contains(query) ||
            location.contains(query) ||
            specialties.any((specialty) => specialty.contains(query));
      }).toList();
    }

    // Apply location filter
    if (selectedLocation != 'All') {
      results = results.where((salon) {
        final salonLocation = salon['location']?.toString().toLowerCase() ?? '';
        final selectedLocationLower = selectedLocation.toLowerCase();
        return salonLocation.contains(selectedLocationLower) ||
            selectedLocationLower.contains(salonLocation);
      }).toList();
    }

    // Apply salon type filter
    if (selectedSalonType != 'All') {
      results = results.where((salon) {
        final salonType = salon['salon_type']?.toString().toUpperCase() ?? '';
        return salonType == selectedSalonType.toUpperCase();
      }).toList();
    }

    // Apply show only with contact filter
    if (showOnlyWithContact) {
      results = results.where((salon) {
        final hasContact = salon['contact_no'] != null &&
            salon['contact_no'].toString().trim().isNotEmpty &&
            salon['contact_no'] != 'Contact not available';
        return hasContact;
      }).toList();
    }

    return results;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  final List<String> categories = ['Vacancies', 'All Salons'];

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.04;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: ColorConstants.black.withOpacity(0.05),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new,
                color: ColorConstants.black, size: 18),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        title: Text(
          'Search Salons',
          style: GoogleFonts.poppins(
            color: ColorConstants.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Modern Header with Search
            Container(
              padding: EdgeInsets.all(padding),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.04),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isLoading
                                ? 'Loading salons...'
                                : '${filteredSalons.length} salons found',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Find your perfect salon',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Modern Search Bar
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF8F9FA),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.grey.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search salons, locations...',
                              hintStyle: GoogleFonts.poppins(
                                color: Colors.grey[400],
                                fontSize: 14,
                              ),
                              prefixIcon: Container(
                                margin: const EdgeInsets.all(12),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: ColorConstants.black.withOpacity(0.05),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Icon(
                                  Icons.search,
                                  color: Colors.grey[500],
                                  size: 20,
                                ),
                              ),
                              suffixIcon: _searchController.text.isNotEmpty
                                  ? IconButton(
                                      icon: Container(
                                        padding: const EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: Colors.grey.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child:
                                            const Icon(Icons.clear, size: 16),
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _searchController.clear();
                                        });
                                      },
                                    )
                                  : null,
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 16,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          height: 56,
                          width: 56,
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                ColorConstants.black,
                                ColorConstants.black.withOpacity(0.9),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: ColorConstants.black.withOpacity(0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.tune,
                                color: Colors.white, size: 20),
                            onPressed: _showFilterBottomSheet,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Salons List
            Expanded(
              child: isLoading
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: ColorConstants.black.withOpacity(0.05),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  ColorConstants.black),
                              strokeWidth: 3,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Loading salons...',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )
                  : _buildSalonsList(),
            ),
          ],
        ),
      ),
    );
  }

  // Salons list view
  Widget _buildSalonsList() {
    final padding = MediaQuery.of(context).size.width * 0.04;
    return filteredSalons.isEmpty
        ? _buildEmptyState(isSalon: true)
        : ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: padding),
            itemCount: filteredSalons.length,
            itemBuilder: (context, index) {
              return _buildSalonCard(filteredSalons[index]);
            },
          );
  }

  Widget _buildEmptyState({bool isSalon = false}) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: ColorConstants.black.withOpacity(0.05),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                isSalon
                    ? Icons.store_mall_directory_outlined
                    : Icons.search_off_rounded,
                size: 60,
                color: ColorConstants.black.withOpacity(0.4),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              isSalon ? 'No salons found' : 'No jobs found',
              style: GoogleFonts.poppins(
                fontSize: 22,
                fontWeight: FontWeight.w600,
                color: ColorConstants.black,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Try adjusting your search criteria or filters',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  Widget _buildSalonCard(Map<String, dynamic> salon) {
    // Debug: Print salon data being passed to card
    print('=== SALON CARD DEBUG ===');
    print('Salon name in card: ${salon['salon_name']}');
    print('Profile picture in card: ${salon['profile_picture']}');
    print('Salon images in card: ${salon['salon_images']}');
    print('Contact number in card: ${salon['contact_no']}');
    print('=== END SALON CARD DEBUG ===');

    // Validate contact number
    final hasContactNumber = salon['contact_no'] != null &&
        salon['contact_no'].toString().trim().isNotEmpty &&
        salon['contact_no'] != 'Contact not available';

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Navigate to salon details page with the complete salon data
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SalonDetailsScreen(salon: salon),
              ),
            );
          },
          borderRadius: BorderRadius.circular(24),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Salon image or icon
                    Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        color: ColorConstants.black.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(16),
                        image: salon['image'] != null
                            ? DecorationImage(
                                image: NetworkImage(salon['image']),
                                fit: BoxFit.cover,
                              )
                            : null,
                      ),
                      child: salon['image'] == null
                          ? Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: ColorConstants.black.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Icon(
                                Icons.spa,
                                color: ColorConstants.black.withOpacity(0.6),
                                size: 24,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            salon['name'] ??
                                salon['salon_name'] ??
                                'Unknown Salon',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: ColorConstants.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Colors.grey.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.location_on,
                                  size: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  salon['location'] ?? 'Location not available',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // Contact number display
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: hasContactNumber
                                      ? Colors.green.withOpacity(0.1)
                                      : Colors.grey.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.phone,
                                  size: 14,
                                  color: hasContactNumber
                                      ? Colors.green[600]
                                      : Colors.grey[400],
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  hasContactNumber
                                      ? salon['contact_no']
                                      : 'Contact not available',
                                  style: GoogleFonts.poppins(
                                    fontSize: 13,
                                    color: hasContactNumber
                                        ? Colors.grey[700]
                                        : Colors.grey[500],
                                    fontStyle: hasContactNumber
                                        ? FontStyle.normal
                                        : FontStyle.italic,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Contact availability indicator
                    if (!hasContactNumber)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.orange.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'No Contact',
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            color: Colors.orange[700],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                // Salon type badges
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      (salon['specialties'] as List? ?? []).map((specialty) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: ColorConstants.black.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: ColorConstants.black.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        specialty.toString(),
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: ColorConstants.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),

                // View details section
                const SizedBox(height: 16),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.grey.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'View Details',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          color: ColorConstants.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: ColorConstants.black.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: ColorConstants.black,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(24)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Filter Salons',
                            style: GoogleFonts.poppins(
                              fontSize: 22,
                              fontWeight: FontWeight.w600,
                              color: ColorConstants.black,
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: ColorConstants.black.withOpacity(0.05),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: IconButton(
                              icon: const Icon(Icons.close, size: 20),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Location Filter
                      _buildFilterSection(
                          'Location', locations, selectedLocation, (value) {
                        setState(() => selectedLocation = value);
                      }),
                      const SizedBox(height: 24),

                      // Salon Type Filter
                      _buildFilterSection(
                          'Salon Type', salonTypes, selectedSalonType, (value) {
                        setState(() => selectedSalonType = value);
                      }),
                      const SizedBox(height: 24),

                      // Contact number filter

                      // Action Buttons
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: ColorConstants.black.withOpacity(0.2),
                                  width: 1,
                                ),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: TextButton(
                                onPressed: () {
                                  setState(() {
                                    selectedLocation = 'All';
                                    selectedSalonType = 'All';
                                    showOnlyWithContact = false;
                                  });
                                },
                                style: TextButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Text(
                                  'Reset',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: ColorConstants.black,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    ColorConstants.black,
                                    ColorConstants.black.withOpacity(0.9),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color:
                                        ColorConstants.black.withOpacity(0.2),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: ElevatedButton(
                                onPressed: () {
                                  this.setState(() {});
                                  Navigator.pop(context);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.white,
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  elevation: 0,
                                  shadowColor: Colors.transparent,
                                ),
                                child: Text(
                                  'Apply Filters',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFilterSection(
    String title,
    List<String> options,
    String selectedValue,
    Function(String) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: ColorConstants.black,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: options.map((option) {
            final isSelected = option == selectedValue;
            return GestureDetector(
              onTap: () => onChanged(option),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? ColorConstants.black
                      : Colors.grey.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected
                        ? ColorConstants.black
                        : Colors.grey.withOpacity(0.2),
                    width: 1.5,
                  ),
                ),
                child: Text(
                  option,
                  style: GoogleFonts.poppins(
                    color: isSelected ? Colors.white : ColorConstants.black,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

class StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  StickyTabBarDelegate({required this.child});

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return child;
  }

  @override
  double get maxExtent => 60;

  @override
  double get minExtent => 60;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
