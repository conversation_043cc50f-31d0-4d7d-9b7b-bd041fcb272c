import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/view/jobseeker/details/job_details_screen.dart';

class JobseekerjobsPage extends StatefulWidget {
  const JobseekerjobsPage({super.key});

  @override
  State<JobseekerjobsPage> createState() => _JobseekerjobsPageState();
}

class _JobseekerjobsPageState extends State<JobseekerjobsPage> {
  final TextEditingController _searchController = TextEditingController();
  String searchQuery = '';
  String selectedLocation = 'All';
  String selectedGender = 'All';
  String selectedExpertiseArea = 'All';
  String selectedSalonType = 'All';

  // Filter lists
  final List<String> locations = ['All']; // Will be populated from API data
  final List<String> genderOptions = ['All', 'Male', 'Female', 'Anyone'];
  final List<String> expertiseAreas = [
    'All'
  ]; // Will be populated from API data
  List<String> salonTypes = [
    'All',
    'MALE',
    'FEMALE',
    'UNISEX'
  ]; // Will be populated from API data

  // API jobs data
  List<Map<String, dynamic>> _jobs = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        // This will trigger a rebuild with the new search results
      });
    });
    _fetchJobs();
    _fetchSpecializations();
  }

  Future<void> _fetchJobs() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final jobsData = await ApiService.fetchJobsForJobseekers();

      // Debug: Print first job to see available fields
      if (jobsData.isNotEmpty) {
        print('=== JOB DATA DEBUG ===');
        print('First job keys: ${jobsData.first.keys.toList()}');
        print(
            'First job profile_picture: ${jobsData.first['profile_picture']}');
        print('First job logo_url: ${jobsData.first['logo_url']}');
        print('First job salon_name: ${jobsData.first['salon_name']}');
        print('=== END DEBUG ===');
      }

      // Populate filter lists from API data
      final Set<String> uniqueLocations = <String>{};
      final Set<String> uniqueSalonTypes = <String>{};
      final Set<String> uniqueExpertiseAreas = <String>{};

      for (final job in jobsData) {
        // Add locations
        if (job['location'] != null && job['location'].toString().isNotEmpty) {
          uniqueLocations.add(job['location'].toString());
        }

        // Add salon types using salon_type field
        if (job['salon_type'] != null &&
            job['salon_type'].toString().isNotEmpty) {
          uniqueSalonTypes.add(job['salon_type'].toString().toUpperCase());
        }

        // Add expertise areas from job titles
        if (job['title'] != null && job['title'].toString().isNotEmpty) {
          uniqueExpertiseAreas.add(job['title'].toString());
        }
      }

      setState(() {
        _jobs = jobsData;
        locations.clear();
        locations.addAll(['All', ...uniqueLocations.toList()..sort()]);

        // Update salon types list with actual data from API
        salonTypes.clear();
        salonTypes.addAll(['All', ...uniqueSalonTypes.toList()..sort()]);

        expertiseAreas.clear();
        expertiseAreas.add('All');
        expertiseAreas.addAll(uniqueExpertiseAreas.toList()..sort());
        expertiseAreas.add('Unspecified');

        _isLoading = false;
      });
    } catch (e) {
      print('Error fetching jobs: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load jobs: $e';
      });
    }
  }

  Future<void> _fetchSpecializations() async {
    try {
      final specializationsData = await ApiService.fetchSpecializations();

      setState(() {
        // Update expertise areas list with API data
        // This part is now handled in _fetchJobs
      });
    } catch (e) {
      print('Error fetching specializations: $e');
      // If specializations fail to load, we'll just use the default 'All' option
    }
  }

  List<Map<String, dynamic>> get filteredJobs {
    List<Map<String, dynamic>> results = List.from(_jobs);

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      results = results.where((job) {
        final title = job['title']?.toString().toLowerCase() ?? '';
        final salonName = job['salon_name']?.toString().toLowerCase() ?? '';
        final location = job['location']?.toString().toLowerCase() ?? '';
        final experience = job['experience']?.toString().toLowerCase() ?? '';

        return title.contains(query) ||
            salonName.contains(query) ||
            location.contains(query) ||
            experience.contains(query);
      }).toList();
    }

    // Apply additional filters
    if (selectedLocation != 'All') {
      results = results.where((job) {
        final jobLocation = job['location']?.toString().toLowerCase() ?? '';
        final selectedLocationLower = selectedLocation.toLowerCase();
        return jobLocation.contains(selectedLocationLower) ||
            selectedLocationLower.contains(jobLocation);
      }).toList();
    }
    if (selectedGender != 'All') {
      results = results.where((job) {
        final jobGender = job['gender']?.toString().toLowerCase() ?? '';
        final selectedGenderLower = selectedGender.toLowerCase();
        return jobGender == selectedGenderLower ||
            (selectedGenderLower == 'anyone' && jobGender == 'anyone') ||
            (selectedGenderLower == 'male' && jobGender == 'male') ||
            (selectedGenderLower == 'female' && jobGender == 'female');
      }).toList();
    }
    if (selectedExpertiseArea != 'All') {
      results = results.where((job) {
        final jobTitle = job['title']?.toString() ?? '';
        if (jobTitle.isEmpty) {
          return selectedExpertiseArea.toLowerCase() == 'unspecified';
        }
        return jobTitle
            .toLowerCase()
            .contains(selectedExpertiseArea.toLowerCase());
      }).toList();
    }
    if (selectedSalonType != 'All') {
      results = results.where((job) {
        final salonType = job['salon_type']?.toString().toUpperCase() ?? '';
        return salonType == selectedSalonType.toUpperCase();
      }).toList();
    }

    return results;
  }

  // Method to sort jobs based on criteria

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.04;

    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            backgroundColor: Colors.white,
            floating: true,
            pinned: true,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(24)),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              'Search Jobs',
              style: GoogleFonts.poppins(
                color: Colors.black,
                fontSize: size.width * 0.055,
                fontWeight: FontWeight.w700,
              ),
            ),
            centerTitle: false,
            expandedHeight: 170,
            flexibleSpace: FlexibleSpaceBar(
              background: Padding(
                padding: EdgeInsets.only(
                  top: 100,
                  left: padding,
                  right: padding,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    Material(
                      elevation: 2,
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                controller: _searchController,
                                decoration: InputDecoration(
                                  hintText: 'Search for jobs, companies...',
                                  hintStyle: GoogleFonts.poppins(
                                      color: Colors.grey[400]),
                                  prefixIcon: Icon(
                                    Icons.search,
                                    color: Colors.grey[400],
                                  ),
                                  suffixIcon: _searchController.text.isNotEmpty
                                      ? IconButton(
                                          icon: const Icon(Icons.clear),
                                          onPressed: () {
                                            setState(() {
                                              _searchController.clear();
                                            });
                                          },
                                        )
                                      : null,
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 15,
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              height: 48,
                              width: 48,
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    ColorConstants.black,
                                    ColorConstants.black.withOpacity(0.8)
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color:
                                        ColorConstants.black.withOpacity(0.12),
                                    blurRadius: 8,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: IconButton(
                                icon:
                                    const Icon(Icons.tune, color: Colors.white),
                                onPressed: _showFilterBottomSheet,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 14, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            _isLoading
                                ? 'Loading jobs...'
                                : '${filteredJobs.length} jobs available',
                            style: GoogleFonts.poppins(
                              fontSize: size.width * 0.028,
                              color: Colors.grey[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(child: SizedBox(height: 12)),
          _isLoading
              ? SliverFillRemaining(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              : _errorMessage != null
                  ? SliverFillRemaining(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 60,
                              color: Colors.red[300],
                            ),
                            SizedBox(height: 16),
                            Text(
                              'Error loading jobs',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              _errorMessage!,
                              textAlign: TextAlign.center,
                              style:
                                  GoogleFonts.poppins(color: Colors.grey[600]),
                            ),
                            SizedBox(height: 24),
                            ElevatedButton(
                              onPressed: _fetchJobs,
                              child: Text('Try Again'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorConstants.black,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : filteredJobs.isEmpty
                      ? SliverFillRemaining(child: _buildEmptyState())
                      : SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              return Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: padding,
                                ),
                                child: _buildModernJobCard(filteredJobs[index]),
                              );
                            },
                            childCount: filteredJobs.length,
                          ),
                        ),
        ],
      ),
    );
  }

  Widget _buildEmptyState({bool isSalon = false}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isSalon
                ? Icons.store_mall_directory_outlined
                : Icons.search_off_rounded,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            isSalon ? 'No salons found' : 'No jobs found',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildModernJobCard(Map<String, dynamic> job) {
    // Use a placeholder if no logo is available
    final String? logoUrl = job['logo_url'];

    // Get profile picture with better handling of nested structure
    String? profilePicture;
    if (job['salon_details'] != null &&
        job['salon_details']['profile_picture'] != null) {
      profilePicture = job['salon_details']['profile_picture'];
    } else if (job['profile_picture'] != null) {
      profilePicture = job['profile_picture'];
    }

    final Color avatarColor = ColorConstants.black.withOpacity(0.08);
    final String companyInitial =
        (job['salon_name'] ?? 'S').toString().isNotEmpty
            ? (job['salon_name'] ?? 'S').toString()[0].toUpperCase()
            : 'S';

    // Debug: Print image data for this job
    print('Job: ${job['title']}');
    print('  profile_picture: $profilePicture');
    print('  logo_url: $logoUrl');
    print('  salon_name: ${job['salon_name']}');
    print('  salon_details: ${job['salon_details']}');

    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[100]!, width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => JobDetailsScreen(
                  jobDetails: job,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(18),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Large, colorful avatar
                CircleAvatar(
                  radius: 32,
                  backgroundColor: avatarColor,
                  backgroundImage:
                      profilePicture != null && profilePicture.isNotEmpty
                          ? NetworkImage(profilePicture)
                          : (logoUrl != null && logoUrl.isNotEmpty
                              ? NetworkImage(logoUrl)
                              : null),
                  child: (profilePicture == null || profilePicture.isEmpty) &&
                          (logoUrl == null || logoUrl.isEmpty)
                      ? Text(
                          companyInitial,
                          style: GoogleFonts.poppins(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: ColorConstants.black,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 18),
                // Job info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        job['title'] ?? '',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          fontSize: 19,
                          color: Colors.black87,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        job['salon_name'] ?? '',
                        style: GoogleFonts.poppins(
                          color: Colors.grey[700],
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on_outlined,
                            size: 16,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              job['location'] ?? '',
                              style: GoogleFonts.poppins(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          Icon(
                            Icons.person_outline,
                            size: 16,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            job['gender'] ?? 'Anyone',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),

                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            'Salon Type :',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[500],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color:
                                  _getSalonTypeColor(job['salon_type'] ?? ''),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              job['salon_type'] ?? 'Anyone',
                              style: GoogleFonts.poppins(
                                color: Colors.black,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),

                      Row(
                        children: [
                          Text(
                            'Posted Date :',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[500],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            job['posted_date'] ?? 'N/A',
                            style: GoogleFonts.poppins(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      // Minimal, right-aligned button
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Spacer(),
                          TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => JobDetailsScreen(
                                    jobDetails: job,
                                  ),
                                ),
                              );
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: ColorConstants.black,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 0),
                              minimumSize: Size(0, 32),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'View Details',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 13,
                                  ),
                                ),
                                SizedBox(width: 4),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 13,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Filter Jobs',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  _buildFilterSection('Location', locations, selectedLocation, (
                    value,
                  ) {
                    setState(() => selectedLocation = value);
                  }),
                  _buildFilterSection('Gender', genderOptions, selectedGender, (
                    value,
                  ) {
                    setState(() => selectedGender = value);
                  }),
                  _buildFilterSection(
                      'Expertise Area', expertiseAreas, selectedExpertiseArea, (
                    value,
                  ) {
                    setState(() => selectedExpertiseArea = value);
                  }),
                  _buildFilterSection(
                      'Salon Type', salonTypes, selectedSalonType, (
                    value,
                  ) {
                    setState(() => selectedSalonType = value);
                  }),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            setState(() {
                              selectedLocation = 'All';
                              selectedGender = 'All';
                              selectedExpertiseArea = 'All';
                              selectedSalonType = 'All';
                            });
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: const Text('Reset'),
                        ),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            this.setState(() {});
                            Navigator.pop(context);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstants.black,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          child: const Text('Apply'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildFilterSection(
    String title,
    List<String> options,
    String selectedValue,
    Function(String) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = option == selectedValue;
            return FilterChip(
              label: Text(option),
              selected: isSelected,
              onSelected: (bool selected) {
                onChanged(option);
              },
              backgroundColor: Colors.grey[100],
              selectedColor: ColorConstants.black,
              labelStyle: GoogleFonts.poppins(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Color _getSalonTypeColor(String salonType) {
    switch (salonType.toUpperCase()) {
      case 'MALE':
        return const Color.fromARGB(255, 219, 222, 224); // Blue for male
      case 'FEMALE':
        return const Color.fromARGB(255, 211, 209, 210); // Pink for female
      case 'UNISEX':
        return const Color.fromARGB(255, 242, 238, 243); // Purple for unisex
      default:
        return Colors.grey;
    }
  }
}
