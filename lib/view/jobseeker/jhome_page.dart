import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_fonts/google_fonts.dart';
import 'dart:io' show Platform, File;
import 'package:job/uttilits/image_const.dart';
import 'package:job/view/common/home_screen/pages/consultation_screen.dart';
import 'package:job/view/common/home_screen/pages/products_screen.dart';
import 'package:job/view/common/home_screen/pages/training_screen.dart';
import 'package:job/view/common/home_screen/pages/whats_new_screen.dart';
import 'package:job/view/jobseeker/screens/jobseeker_helpcenter.dart';
import 'package:job/view/jobseeker/screens/jobseekerapplications_page.dart';
import 'package:job/view/jobseeker/screens/jobseekerjobs_page.dart';
import 'package:job/view/jobseeker/screens/jobseekerprofile_page.dart';
import 'package:job/view/jobseeker/screens/salon_list.dart';
import 'package:job/view/salonowner2/screens/business_income_screen.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:job/services/api_service.dart';

// Class to store menu item data
class MenuItemData {
  final String title;
  final IconData icon;
  final List<Color> colors;
  final String screenType;

  MenuItemData({
    required this.title,
    required this.icon,
    required this.colors,
    required this.screenType,
  });
}

class JhomePage extends StatefulWidget {
  const JhomePage({super.key});

  @override
  State<JhomePage> createState() => _JhomePageState();
}

class _JhomePageState extends State<JhomePage> {
  final TextEditingController _searchController = TextEditingController();

  // List to store menu items that can be reordered
  List<MenuItemData> menuItems = [];

  String selectedCategory = 'What\'s New';
  bool isLoggedIn = false;
  String userType = ''; // 'jobseeker' or 'salonowner'

  Map<String, dynamic> _profileData = {};
  File? _profileImage;

  // Helper method to safely check platform
  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isAndroid => !kIsWeb && Platform.isAndroid;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
    _initializeMenuItems();
    _loadProfileImage();
    _fetchProfileData();

    // Set status bar to match iOS style when on iOS
    if (!kIsWeb && isIOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
          statusBarBrightness:
              Brightness.light, // iOS: controls status bar text color
        ),
      );
    }
  }

  void _initializeMenuItems() {
    menuItems = [
      // MenuItemData(
      //   title: 'Business Income',
      //   icon: Icons.business_center_outlined,
      //   colors: [Color(0xff3171C9), Color(0xff3171C9)],
      //   screenType: 'income',
      // ),
      MenuItemData(
        title: 'Vacancies',
        icon: Icons.content_cut,
        colors: [Color(0xff2BA85C), Color(0xff2BA85C)],
        screenType: 'vacancies',
      ),
      MenuItemData(
        title: 'Requests & Alerts',
        icon: Icons.work_outline_rounded,
        colors: [Color(0xffF03D88), Color(0xffF03D88)],
        screenType: 'Applications',
      ),

      // MenuItemData(
      //   title: 'Recruitment Hub',
      //   icon: Icons.receipt_rounded,
      //   colors: [Color(0xff7AC03C), Color(0xff3171C9)],
      //   screenType: 'recruitment_hub',
      // ),
      MenuItemData(
        title: 'Salons',
        icon: Icons.shopify_outlined,
        colors: [Color(0xffF6AC32), Color(0xffC42F62)],
        screenType: 'salon_list',
      ),

      // MenuItemData(
      //   title: 'Salon Expenses',
      //   icon: Icons.attach_money_rounded,
      //   colors: [Color(0xffFA4A06), Color(0xff3171C9)],
      //   screenType: 'expenses',
      // ),
      MenuItemData(
        title: 'Training Hub',
        icon: Icons.play_arrow,
        colors: [Color(0xffF4B102), Color(0xffF4B102)],
        screenType: 'training',
      ),
      MenuItemData(
        title: 'Consultation',
        icon: Icons.chat_bubble,
        colors: [Colors.pink, Color(0xff3171C9)],
        screenType: 'consultation',
      ),
      MenuItemData(
        title: 'Products',
        icon: Icons.shopping_bag,
        colors: [Color(0xffE65B00), Color(0xffE65B00)],
        screenType: 'products',
      ),
      MenuItemData(
        title: 'What\'s Trending',
        icon: Icons.trending_up,
        colors: [Color(0xff3171C9), Color(0xff5E469B)],
        screenType: 'trending',
      ),

      // MenuItemData(
      //   title: 'Training Hub',
      //   icon: Icons.play_arrow,
      //   colors: [Color(0xffF4B102), Color(0xffF4B102)],
      //   screenType: 'training',
      // ),
      // MenuItemData(
      //   title: 'Training Hub',
      //   icon: Icons.play_arrow,
      //   colors: [Color(0xffF03D88), Color(0xffF03D88)],
      //   screenType: 'training',
      // ),
      // MenuItemData(
      //   title: 'Training Hub',
      //   icon: Icons.play_arrow,
      //   colors: [Color(0xff4123BF), Color(0xff4123BF)],
      //   screenType: 'training',
      // ),
      MenuItemData(
        title: 'Help Center',
        icon: Icons.help,
        colors: [Colors.purple, Colors.red],
        screenType: 'help_center',
      ),
    ];
  }

  // Method to check login status and user type
  void _checkLoginStatus() async {
    // First check shared preferences for login status
    try {
      final prefs = await SharedPreferences.getInstance();
      final bool? isLoggedInPref = prefs.getBool('is_logged_in');
      final String? storedUserType = prefs.getString('user_type');

      if (isLoggedInPref == true && storedUserType != null) {
        setState(() {
          isLoggedIn = true;
          userType = storedUserType.toLowerCase();
        });
        print('User type from prefs: $userType');
        return; // Exit early if we found login status in prefs
      }
    } catch (e) {
      print('Error checking login status: $e');
    }

    // Fallback to checking parent widgets
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = this.context;
      bool isInJobseeker = false;
      bool isInSalonowner = false;

      // Walk up the widget tree to find parent widgets
      context.visitAncestorElements((element) {
        final widgetStr = element.widget.toString();
        if (widgetStr.contains('Jobseeker')) {
          isInJobseeker = true;
          return false; // Stop traversing
        }
        if (widgetStr.contains('Salonowner')) {
          isInSalonowner = true;
          return false; // Stop traversing
        }
        return true; // Continue traversing
      });

      setState(() {
        isLoggedIn = isInJobseeker || isInSalonowner;
        if (isInJobseeker) userType = 'jobseeker';
        if (isInSalonowner) userType = 'salonowner';
        if (!isInJobseeker && !isInSalonowner) userType = '';
        print('User type from widget tree: $userType');
      });
    });
  }

  Future<void> _fetchProfileData() async {
    try {
      // Fetch profile data from API
      final response = await ApiService.fetchJobseekerProfile();

      setState(() {
        _profileData = response;
      });
    } catch (e) {
      print('Error fetching profile: $e');
    }
  }

  Future<void> _loadProfileImage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final imagePath = prefs.getString('profile_image_path');

      if (imagePath != null) {
        final file = File(imagePath);
        if (await file.exists()) {
          setState(() {
            _profileImage = file;
          });
        }
      }
    } catch (e) {
      print('Error loading profile image: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final size = MediaQuery.of(context).size;
        final padding = size.width * 0.04;

        // Platform-adaptive behavior with web fallback
        if (kIsWeb) {
          return _buildAndroidLayout(
            size,
            padding,
          ); // Use Android layout for web
        } else {
          return _buildAndroidLayout(size, padding);
        }
      },
    );
  }

  // Original Android layout (keeping for cross-platform compatibility)
  Widget _buildAndroidLayout(Size size, double padding) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xFF333333),
        centerTitle: true,
        actionsPadding: EdgeInsets.only(right: 16),
        toolbarHeight: 80,
        title: Container(
          width: 256,
          height: 90,
          child: Image.asset(
            ImageConstants.splash,
            width: 286,
            height: 150,
            fit: BoxFit.contain,
          ),
        ),
        actions: [
          InkWell(
            onTap: () {
              // Navigate to profile page
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const JobseekerprofilePage(),
                ),
              );
            },
            child: CircleAvatar(
              radius: 150 * 0.15,
              backgroundImage: _profileData.isNotEmpty &&
                      _profileData['profile_picture'] != null
                  ? NetworkImage(_profileData['profile_picture'])
                  : _profileImage != null
                      ? FileImage(_profileImage!) as ImageProvider
                      : const NetworkImage(
                          "https://img.icons8.com/ios-filled/50/user.png"),
              backgroundColor: Colors.grey[200],
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Full-screen background image
          Positioned.fill(
            child: Image.asset(ImageConstants.wall, fit: BoxFit.cover),
          ),

          // Gradient overlay for better text visibility
          Positioned.fill(child: Container(decoration: BoxDecoration())),

          // Main content
          Column(
            children: [
              // Grid of menu items
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(padding),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 66),
                    child: Theme(
                      data: Theme.of(context).copyWith(
                        // Make the drag feedback background transparent
                        canvasColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        scaffoldBackgroundColor: Colors.transparent,
                      ),
                      child: ReorderableGridView.count(
                        crossAxisCount: 3,
                        crossAxisSpacing: 14,
                        mainAxisSpacing: 10,
                        childAspectRatio: 0.8,
                        dragWidgetBuilder: (index, child) {
                          // Custom drag widget with transparent background
                          final item = menuItems[index];
                          return Material(
                            color: Colors.transparent,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: item.colors,
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                  child: Icon(
                                    item.icon,
                                    color: Colors.white,
                                    size: 30,
                                  ),
                                ),
                                SizedBox(height: 12),
                                Text(
                                  item.title,
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                        children: List.generate(menuItems.length, (index) {
                          final item = menuItems[index];
                          return _buildDraggableMenuCard(
                            key: ValueKey(index),
                            title: item.title,
                            icon: item.icon,
                            colors: item.colors,
                            onTap: () => _handleMenuItemTap(item.screenType),
                          );
                        }),
                        onReorder: (oldIndex, newIndex) {
                          setState(() {
                            if (oldIndex < newIndex) {
                              newIndex -= 1;
                            }
                            final item = menuItems.removeAt(oldIndex);
                            menuItems.insert(newIndex, item);
                          });
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDraggableMenuCard({
    required Key key,
    required String title,
    required IconData icon,
    required List<Color> colors,
    required VoidCallback onTap,
  }) {
    return InkWell(
      key: key,
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: colors,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(14),
            ),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
          SizedBox(height: 12),
          Text(
            title,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuItemTap(String screenType) {
    switch (screenType) {
      case 'training':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const TrainingScreen()),
        );
        break;
      case 'consultation':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ConsultationScreen()),
        );
        break;
      case 'products':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ProductsScreen()),
        );
        break;
      case 'trending':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const WhatsNewScreen()),
        );
        break;
      case 'income':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const BusinessIncomeScreen()),
        );
        break;

      case 'employers':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const JobseekerjobsPage()),
        );
        break;
      case 'Applications':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const JobseekerapplicationPage(),
          ),
        );
        break;
      case 'vacancies':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const JobseekerjobsPage()),
        );
        break;
      case 'salon_list':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SalonList()),
        );
        break;
      case 'help_center':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const JobseekerHelpcenter()),
        );
        break;

      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$screenType feature coming soon!')),
        );
    }
  }

  // Handle sign out
}
