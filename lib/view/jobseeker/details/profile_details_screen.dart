import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/view/jobseeker/edit/edit_profile_screen.dart';

class ProfileDetailsScreen extends StatefulWidget {
  const ProfileDetailsScreen({super.key});

  @override
  State<ProfileDetailsScreen> createState() => _ProfileDetailsScreenState();
}

class _ProfileDetailsScreenState extends State<ProfileDetailsScreen> {
  bool isLoading = true;
  Map<String, dynamic> profileData = {};
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _fetchProfileData();
  }

  Future<void> _fetchProfileData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      // Fetch profile data from API
      final response = await ApiService.fetchJobseekerProfile();

      setState(() {
        profileData = response;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load profile: $e';
        isLoading = false;
      });
      print('Error fetching profile: $e');
    }
  }

  // Helper function to format experience from decimal to "Xy Ym" format
  // Treats decimal part directly as months (e.g., 46.11 = 46y 11m)
  String _formatExperience(dynamic experience) {
    if (experience == null || experience.toString().isEmpty) return '0y 0m';

    try {
      double expValue;

      // Handle different input types
      if (experience is String) {
        expValue = double.parse(experience);
      } else if (experience is int) {
        expValue = experience.toDouble();
      } else if (experience is double) {
        expValue = experience;
      } else {
        expValue = double.parse(experience.toString());
      }

      int years = expValue.floor();
      // Extract decimal part and convert to months (treat decimal directly as months)
      double decimalPart = expValue - years;
      int months = (decimalPart * 100)
          .round(); // Multiply by 100 to get the decimal as whole number

      // Ensure months is between 0 and 99 (since we're treating .11 as 11 months)
      if (months < 0) months = 0;
      if (months > 99) {
        years += 1;
        months = 0;
      }

      return '${years}y ${months.toString().padLeft(2, '0')}m';
    } catch (e) {
      print('Error formatting experience: $e, value: $experience');
      return '0y 0m';
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    // Get profile picture URL or use a default image
    final String profileImageUrl = profileData['profile_picture'] != null
        ? profileData['profile_picture'].startsWith('http')
            ? profileData['profile_picture']
            : 'baseUrl${profileData['profile_picture']}'
        : 'https://img.icons8.com/ios-filled/50/user.png';

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: Colors.black,
              ),
            )
          : errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        errorMessage,
                        style: GoogleFonts.poppins(
                          color: Colors.red,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : CustomScrollView(
                  slivers: [
                    // Modern App Bar with Gradient
                    SliverAppBar(
                      leading: Container(
                        margin: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.arrow_back_ios_new,
                              color: Colors.white, size: 20),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),
                      expandedHeight: size.height * 0.30,
                      floating: false,
                      pinned: true,
                      stretch: true,
                      backgroundColor: Colors.transparent,
                      elevation: 0,
                      actions: [
                        Container(
                          margin: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.edit_outlined,
                                color: Colors.white, size: 20),
                            onPressed: () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => EditProfileScreen(
                                    profileData: profileData,
                                  ),
                                ),
                              );

                              // If returned with success result, refresh the profile data
                              if (result == true) {
                                _fetchProfileData();
                              }
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      flexibleSpace: FlexibleSpaceBar(
                        stretchModes: const [
                          StretchMode.zoomBackground,
                          StretchMode.blurBackground,
                        ],
                        background: Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.black,
                                Colors.blueGrey,
                              ],
                            ),
                          ),
                          child: Stack(
                            children: [
                              // Background pattern
                              Positioned(
                                top: -50,
                                right: -50,
                                child: Container(
                                  width: 150,
                                  height: 150,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                ),
                              ),
                              Positioned(
                                bottom: -30,
                                left: -30,
                                child: Container(
                                  width: 100,
                                  height: 100,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withOpacity(0.1),
                                  ),
                                ),
                              ),

                              // Profile Content
                              Padding(
                                padding: const EdgeInsets.only(
                                    bottom: 30, left: 155),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    // Profile Image with modern design
                                    Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 4,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.2),
                                            spreadRadius: 5,
                                            blurRadius: 15,
                                            offset: const Offset(0, 8),
                                          ),
                                        ],
                                      ),
                                      child: CircleAvatar(
                                        radius: size.width * 0.12,
                                        backgroundImage:
                                            NetworkImage(profileImageUrl),
                                        backgroundColor: Colors.grey[200],
                                      ),
                                    ),
                                    const SizedBox(height: 20),

                                    // Name with modern typography
                                    Text(
                                      profileData['name'] ?? 'Your Name',
                                      style: GoogleFonts.poppins(
                                        color: Colors.white,
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                    const SizedBox(height: 8),

                                    // Unique ID with subtle styling
                                    Text(
                                      profileData['unique_id'] ??
                                          'ID: Not specified',
                                      style: GoogleFonts.poppins(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // Profile Stats Section
                    SliverToBoxAdapter(
                      child: Container(
                        margin: const EdgeInsets.fromLTRB(16, 20, 18, 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 20,
                              spreadRadius: 0,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            _buildStatItem(
                              icon: Icons.work_outline,
                              title: 'Experience',
                              value: _formatExperience(
                                  profileData['years_of_experience']),
                              color: Colors.orange,
                            ),
                            Container(
                              width: 1,
                              height: 50,
                              color: Colors.grey.withOpacity(0.3),
                            ),
                            _buildStatItem(
                              icon: Icons.category_outlined,
                              title: 'Expertise',
                              value: profileData['expertise_areas_data']
                                          ?.isNotEmpty ==
                                      true
                                  ? '${profileData['expertise_areas_data'].length} areas'
                                  : 'Not set',
                              color: Colors.purple,
                            ),
                            Container(
                              width: 1,
                              height: 50,
                              color: Colors.grey.withOpacity(0.3),
                            ),
                            _buildStatItem(
                              icon: Icons.work,
                              title: 'Status',
                              value: profileData['work_status'] ?? 'Not set',
                              color: Colors.green,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Personal Information Section
                    SliverToBoxAdapter(
                      child: Container(
                        margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 20,
                              spreadRadius: 0,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.person_outline,
                                    color: Colors.blue,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Personal Information',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            _buildDetailRow(
                              icon: Icons.phone_outlined,
                              title: 'Phone Number',
                              value:
                                  profileData['contact_no'] ?? 'Not specified',
                              color: Colors.blue,
                            ),
                            _buildDetailRow(
                              icon: Icons.email_outlined,
                              title: 'Email Address',
                              value: profileData['email'] ?? 'Not specified',
                              color: Colors.green,
                            ),
                            _buildDetailRow(
                              icon: Icons.location_on_outlined,
                              title: 'Location',
                              value: profileData['place'] ?? 'Not specified',
                              color: Colors.orange,
                            ),
                            if (profileData['date_of_birth'] != null)
                              _buildDetailRow(
                                icon: Icons.cake_outlined,
                                title: 'Date of Birth',
                                value: profileData['date_of_birth'] ??
                                    'Not specified',
                                color: Colors.pink,
                              ),
                            if (profileData['gender'] != null)
                              _buildDetailRow(
                                icon: Icons.person_outline,
                                title: 'Gender',
                                value: profileData['gender'] == 'M'
                                    ? 'Male'
                                    : profileData['gender'] == 'F'
                                        ? 'Female'
                                        : profileData['gender'] == 'O'
                                            ? 'Other'
                                            : 'Not specified',
                                color: Colors.purple,
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Professional Information Section
                    SliverToBoxAdapter(
                      child: Container(
                        margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 20,
                              spreadRadius: 0,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.indigo.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.work_outline,
                                    color: Colors.indigo,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Professional Information',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            _buildDetailRow(
                              icon: Icons.work_outline,
                              title: 'Years of Experience',
                              value: _formatExperience(
                                  profileData['years_of_experience']),
                              color: Colors.indigo,
                            ),
                            _buildDetailRow(
                              icon: Icons.category_outlined,
                              title: 'Expertise Areas',
                              value: profileData['expertise_areas_data']
                                          ?.isNotEmpty ==
                                      true
                                  ? profileData['expertise_areas_data']
                                      .map((area) => area['name'])
                                      .join(', ')
                                  : 'Not specified',
                              color: Colors.teal,
                            ),
                            _buildDetailRow(
                              icon: Icons.link_outlined,
                              title: 'Social Link',
                              value:
                                  profileData['social_link']?.isNotEmpty == true
                                      ? profileData['social_link']
                                      : 'Not specified',
                              color: Colors.blue,
                              isLink: profileData['social_link']?.isNotEmpty ==
                                  true,
                            ),
                            _buildDetailRow(
                              icon: Icons.work,
                              title: 'Work Status',
                              value:
                                  profileData['work_status'] ?? 'Not specified',
                              color: Colors.green,
                            ),
                          ],
                        ),
                      ),
                    ),

                    // About Section
                    if (profileData['about']?.isNotEmpty == true)
                      SliverToBoxAdapter(
                        child: Container(
                          margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.08),
                                blurRadius: 20,
                                spreadRadius: 0,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.purple.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.info_outline,
                                      color: Colors.purple,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'About Me',
                                    style: GoogleFonts.poppins(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                profileData['about'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: Colors.black54,
                                  height: 1.6,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                    // Work Portfolio Section
                    if (profileData['work_pictures_data'] != null &&
                        (profileData['work_pictures_data'] as List).isNotEmpty)
                      SliverToBoxAdapter(
                        child: Container(
                          margin: const EdgeInsets.fromLTRB(16, 0, 16, 30),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.08),
                                blurRadius: 20,
                                spreadRadius: 0,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.amber.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.photo_library_outlined,
                                      color: Colors.amber,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'Work Portfolio',
                                    style: GoogleFonts.poppins(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 3,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                ),
                                itemCount:
                                    (profileData['work_pictures_data'] as List)
                                        .length,
                                itemBuilder: (context, index) {
                                  final workPicture =
                                      profileData['work_pictures_data'][index];
                                  return GestureDetector(
                                    onTap: () {
                                      // Show full-screen image view when tapped
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => Scaffold(
                                            backgroundColor: Colors.black,
                                            appBar: AppBar(
                                              backgroundColor: Colors.black,
                                              iconTheme: const IconThemeData(
                                                  color: Colors.white),
                                              title: Text(
                                                'Work Portfolio',
                                                style: GoogleFonts.poppins(
                                                    color: Colors.white),
                                              ),
                                            ),
                                            body: Center(
                                              child: InteractiveViewer(
                                                child: Image.network(
                                                  workPicture['image'],
                                                  fit: BoxFit.contain,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.1),
                                            blurRadius: 8,
                                            spreadRadius: 0,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                        image: DecorationImage(
                                          image: NetworkImage(
                                              workPicture['image']),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 11,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    bool isLink = false,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: isLink ? onTap : null,
        borderRadius: BorderRadius.circular(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, size: 20, color: color),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: isLink ? Colors.blue : Colors.black87,
                      decoration: isLink ? TextDecoration.underline : null,
                    ),
                  ),
                ],
              ),
            ),
            if (isLink)
              Icon(
                Icons.open_in_new,
                size: 16,
                color: Colors.grey[400],
              ),
          ],
        ),
      ),
    );
  }
}
