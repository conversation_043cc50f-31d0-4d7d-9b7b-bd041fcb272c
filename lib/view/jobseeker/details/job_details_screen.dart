import 'package:flutter/material.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/services/api_service.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class JobDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> jobDetails;

  const JobDetailsScreen({
    Key? key,
    required this.jobDetails,
  }) : super(key: key);

  @override
  State<JobDetailsScreen> createState() => _JobDetailsScreenState();
}

class _JobDetailsScreenState extends State<JobDetailsScreen> {
  bool _isLoading = true;
  bool _isApplying = false;
  String? _errorMessage;
  bool _hasApplied = false;
  Map<String, dynamic> _fullJobDetails = {};

  @override
  void initState() {
    super.initState();
    _fetchFullJobDetails();
    _checkApplicationStatus();
  }

  Future<void> _checkApplicationStatus() async {
    try {
      // Get the job ID from the job details
      final int? jobId = widget.jobDetails['id'];

      if (jobId != null) {
        // You'll need to add this method to your ApiService
        // For now, we'll handle it when the user tries to apply
        // final hasApplied = await ApiService.checkIfApplied(jobId);
        // setState(() {
        //   _hasApplied = hasApplied;
        // });
      }
    } catch (e) {
      print('Error checking application status: $e');
    }
  }

  Future<void> _fetchFullJobDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Start with the basic job details passed to this screen
      _fullJobDetails = Map.from(widget.jobDetails);

      // Fetch detailed job information
      final jobId = widget.jobDetails['id'];
      if (jobId != null) {
        final detailedJob = await ApiService.fetchJobDetailsById(jobId);
        if (mounted) {
          setState(() {
            _fullJobDetails = detailedJob;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'Job ID not found';
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load job details: $e';
        });
      }
      print('Error fetching job details: $e');
    }
  }

  // Helper method to get salon details (handles both direct and nested structure)
  Map<String, dynamic>? get _salonDetails {
    // First try to get from nested salon_details
    if (_fullJobDetails['salon_details'] != null) {
      return _fullJobDetails['salon_details'];
    }
    // If not available, return null
    return null;
  }

  // Helper method to get profile picture (prioritizes salon_details.profile_picture)
  String? get _profilePicture {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['profile_picture'] != null) {
      return _salonDetails!['profile_picture'];
    }
    // Fallback to direct profile_picture field
    if (_fullJobDetails['profile_picture'] != null) {
      return _fullJobDetails['profile_picture'];
    }
    return null;
  }

  // Helper method to get salon owner name (prioritizes salon_details.owner_name)
  String? get _salonOwnerName {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['owner_name'] != null) {
      return _salonDetails!['owner_name'];
    }
    // Fallback to direct salon_owner_name field
    if (_fullJobDetails['salon_owner_name'] != null) {
      return _fullJobDetails['salon_owner_name'];
    }
    return null;
  }

  // Helper method to get salon contact number (prioritizes salon_details.contact_no)
  String? get _salonContactNo {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['contact_no'] != null) {
      return _salonDetails!['contact_no'];
    }
    // Fallback to direct salon_contact_no field
    if (_fullJobDetails['salon_contact_no'] != null) {
      return _fullJobDetails['salon_contact_no'];
    }
    return null;
  }

  // Helper method to get salon email (prioritizes salon_details.email)
  String? get _salonEmail {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['email'] != null) {
      return _salonDetails!['email'];
    }
    // Fallback to direct salon_email field
    if (_fullJobDetails['salon_email'] != null) {
      return _fullJobDetails['salon_email'];
    }
    return null;
  }

  // Helper method to get salon location (prioritizes salon_details.location)
  String? get _salonLocation {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['location'] != null) {
      return _salonDetails!['location'];
    }
    // Fallback to direct location field
    if (_fullJobDetails['location'] != null) {
      return _fullJobDetails['location'];
    }
    return null;
  }

  // Helper method to get salon pin code (prioritizes salon_details.pin_code)
  String? get _salonPinCode {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['pin_code'] != null) {
      return _salonDetails!['pin_code'];
    }
    // Fallback to direct salon_pin_code field
    if (_fullJobDetails['salon_pin_code'] != null) {
      return _fullJobDetails['salon_pin_code'];
    }
    return null;
  }

  // Helper method to get salon google map link (prioritizes salon_details.google_map_link)
  String? get _salonGoogleMapLink {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['google_map_link'] != null) {
      return _salonDetails!['google_map_link'];
    }
    // Fallback to direct salon_google_map_link field
    if (_fullJobDetails['salon_google_map_link'] != null) {
      return _fullJobDetails['salon_google_map_link'];
    }
    return null;
  }

  // Helper method to get salon type (prioritizes salon_details.salon_type)
  String? get _salonType {
    // First try to get from nested salon_details
    if (_salonDetails != null && _salonDetails!['salon_type'] != null) {
      return _salonDetails!['salon_type'];
    }
    // Fallback to direct salon_type field
    if (_fullJobDetails['salon_type'] != null) {
      return _fullJobDetails['salon_type'];
    }
    return null;
  }

  // Helper method to get salon about (from salon_details.about)
  String? get _salonAbout {
    if (_salonDetails != null && _salonDetails!['about'] != null) {
      return _salonDetails!['about'];
    }
    return null;
  }

  Future<void> _applyForJob() async {
    try {
      setState(() {
        _isApplying = true;
        _errorMessage = null;
      });

      // Get the job ID from the job details
      final int? jobId = _fullJobDetails['id'];

      if (jobId == null) {
        throw Exception('Job ID not found');
      }

      // Apply for the job
      await ApiService.applyForJob(jobId: jobId);

      if (mounted) {
        setState(() {
          _isApplying = false;
          _hasApplied = true;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Application sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isApplying = false;
        });

        // Check if the error is about already having applied
        final errorMessage = e.toString().toLowerCase();
        if (errorMessage.contains('already applied') ||
            errorMessage.contains('you have already applied for this job')) {
          setState(() {
            _hasApplied = true;
            _errorMessage = null; // Don't show as error
          });

          // Show informational message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('You have already applied for this job'),
              backgroundColor: Colors.blue[600],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              margin: const EdgeInsets.all(10),
            ),
          );
        } else {
          // For other errors, show as error
          setState(() {
            _errorMessage = e.toString();
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to apply: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Job Details',
          style: GoogleFonts.poppins(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _errorMessage != null && _fullJobDetails.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 60,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Error loading job details',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32.0),
                          child: Text(
                            _errorMessage!,
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: _fetchFullJobDetails,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstants.black,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Try Again'),
                        ),
                      ],
                    ),
                  )
                : SingleChildScrollView(
                    child: Column(children: [
                      // Job Header Card
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Expertise Area and Salon
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 30,
                                  backgroundColor: Colors.grey[200],
                                  backgroundImage: _profilePicture != null
                                      ? NetworkImage(_profilePicture!)
                                      : null,
                                  child: _profilePicture == null
                                      ? Icon(
                                          Icons.person,
                                          size: 30,
                                          color: Colors.grey[600],
                                        )
                                      : null,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        _fullJobDetails['title'] ??
                                            'Expertise Area',
                                        style: GoogleFonts.poppins(
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        _fullJobDetails['salon_name'] ??
                                            'Salon Name',
                                        style: GoogleFonts.poppins(
                                          fontSize: 16,
                                          color: Colors.grey[600],
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Job Tags
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                _buildTag(
                                  Icons.location_on_outlined,
                                  _salonLocation ?? 'Location',
                                  Colors.blue,
                                ),
                                _buildTag(
                                  Icons.person_outline,
                                  _fullJobDetails['gender'] ?? 'Anyone',
                                  Colors.green,
                                ),
                                if (_fullJobDetails['specialization_name'] !=
                                    null)
                                  _buildTag(
                                    Icons.category_outlined,
                                    _fullJobDetails['specialization_name'],
                                    Colors.purple,
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Job Summary Card
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.description_outlined,
                                    color: Colors.orange,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Job Summary',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _fullJobDetails['job_summary'] ??
                                  'No job summary provided.',
                              style: GoogleFonts.poppins(
                                fontSize: 15,
                                color: Colors.grey[700],
                                height: 1.6,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // About Section Card (if available)
                      if (_salonAbout != null && _salonAbout!.isNotEmpty)
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.indigo.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.info_outline,
                                      color: Colors.indigo,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'About',
                                    style: GoogleFonts.poppins(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _salonAbout!,
                                style: GoogleFonts.poppins(
                                  fontSize: 15,
                                  color: Colors.grey[700],
                                  height: 1.6,
                                ),
                              ),
                            ],
                          ),
                        ),

                      if (_salonAbout != null && _salonAbout!.isNotEmpty)
                        const SizedBox(height: 16),

                      // Salon Information Card
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.store,
                                    color: Colors.green,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Salon Information',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Salon Type Heading
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: _getSalonTypeColor(_salonType)
                                        .withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Icon(
                                    Icons.category_outlined,
                                    color: _getSalonTypeColor(_salonType),
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  '${_salonType != null ? '$_salonType' : 'UNISEX'} Salon',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: _getSalonTypeColor(_salonType),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Salon Details
                            _buildInfoRow(
                              Icons.person_outline,
                              'Owner',
                              _salonOwnerName ?? 'Not specified',
                            ),
                            _buildInfoRow(
                              Icons.phone_outlined,
                              'Contact',
                              _salonContactNo ?? 'Not specified',
                            ),
                            _buildInfoRow(
                              Icons.email_outlined,
                              'Email',
                              _salonEmail ?? 'Not specified',
                            ),
                            _buildInfoRow(
                              Icons.location_on_outlined,
                              'Location',
                              _salonLocation ?? 'Not specified',
                            ),
                            if (_salonPinCode != null &&
                                _salonPinCode!.isNotEmpty)
                              _buildInfoRow(
                                Icons.pin_drop_outlined,
                                'Pin Code',
                                _salonPinCode!,
                              ),
                            if (_fullJobDetails['state'] != null &&
                                _fullJobDetails['state'].toString().isNotEmpty)
                              _buildInfoRow(
                                Icons.location_city_outlined,
                                'State',
                                _fullJobDetails['state'],
                              ),
                            if (_fullJobDetails['experience'] != null &&
                                _fullJobDetails['experience']
                                    .toString()
                                    .isNotEmpty)
                              if (_salonGoogleMapLink != null &&
                                  _salonGoogleMapLink!.isNotEmpty)
                                _buildInfoRowWithLink(
                                  Icons.map_outlined,
                                  'Map Link',
                                  'View on Google Maps',
                                  () async {
                                    // Launch Google Maps URL
                                    await _launchMapUrl(_salonGoogleMapLink!);
                                  },
                                ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Debug: Print salon images data
                      Builder(
                        builder: (context) {
                          final salonImages = _salonDetails?['salon_images'] ??
                              _fullJobDetails['salon_images'] ??
                              [];
                          print(
                              'Salon images from _fullJobDetails: ${_fullJobDetails['salon_images']}');
                          print(
                              'Salon images from _salonDetails: ${_salonDetails?['salon_images']}');
                          print('Final salon images: $salonImages');
                          return const SizedBox.shrink();
                        },
                      ),
                      // Gallery Section
                      _buildInfoSection(
                        title: 'Gallery',
                        items: [
                          _buildGalleryItem(_salonDetails?['salon_images'] ??
                              _fullJobDetails['salon_images'] ??
                              []),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // const SizedBox(height: 10),

                      // Posted Date Card
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 12),
                        child: Container(
                          width: double.infinity,
                          // margin: const EdgeInsets.symmetric(horizontal: 16),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.purple.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.calendar_today,
                                  color: Colors.purple,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Row(
                                  children: [
                                    Text(
                                      'Posted Date :',
                                      style: GoogleFonts.poppins(
                                        color: Colors.grey[500],
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      _fullJobDetails['posted_date'] ?? 'N/A',
                                      style: GoogleFonts.poppins(
                                        color: Colors.grey[600],
                                        fontSize: 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ]),
                  ),
      ),
      bottomNavigationBar: _isLoading ||
              (_errorMessage != null && _fullJobDetails.isEmpty)
          ? null
          : Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SizedBox(
                height: 55,
                child: ElevatedButton(
                  onPressed: _hasApplied || _isApplying ? null : _applyForJob,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _hasApplied ? Colors.blue[600] : ColorConstants.black,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    disabledBackgroundColor:
                        _hasApplied ? Colors.blue[600] : Colors.grey,
                    disabledForegroundColor: Colors.white,
                  ),
                  child: _isApplying
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (_hasApplied) ...[
                              const Icon(Icons.check_circle, size: 20),
                              const SizedBox(width: 8),
                            ],
                            Text(
                              _hasApplied ? 'Already Applied' : 'Apply Now',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ),
            ),
    );
  }

  Widget _buildTag(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 6),
          Text(
            label,
            style: GoogleFonts.poppins(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    color: Colors.grey[700],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowWithLink(
      IconData icon, String label, String value, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withOpacity(0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue.withOpacity(0.2),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      value,
                      style: GoogleFonts.poppins(
                        color: Colors.blue,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.open_in_new,
                size: 16,
                color: Colors.blue,
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatPostedDate(String? date) {
    if (date == null) return 'Date not available';
    final DateTime postedDateTime = DateTime.parse(date);
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(postedDateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  Color _getSalonTypeColor(String? salonType) {
    if (salonType == null) return Colors.grey;
    switch (salonType.toLowerCase()) {
      case 'male':
        return Colors.blue;
      case 'female':
        return Colors.pink;
      case 'unisex':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // Helper function to handle Google Maps URLs
  Future<void> _launchMapUrl(String url) async {
    try {
      Uri uri = Uri.parse(url);

      // Check if it's a Google Maps URL and try to optimize it
      if (url.contains('g.co') || url.contains('google.com/maps')) {
        // Try to launch with external application first
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          return;
        }
      }

      // Fallback to platform default
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.platformDefault);
      } else {
        throw Exception('No app found to handle this URL');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open map: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Copy URL',
              textColor: Colors.white,
              onPressed: () {
                // You can add clipboard functionality here
                print('URL to copy: $url');
              },
            ),
          ),
        );
      }
    }
  }

  Widget _buildInfoSection({
    required String title,
    required List<Widget> items,
  }) {
    return Container(
      // margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.photo_library_outlined,
                  color: Colors.purple,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...items,
        ],
      ),
    );
  }

  Widget _buildGalleryItem(List<dynamic> images) {
    // Debug: Print the images data to understand the structure
    print('Gallery images data: $images');
    print('Images type: ${images.runtimeType}');

    // Handle different data structures
    List<String> imageUrls = [];

    if (images is List) {
      for (var item in images) {
        if (item is String) {
          imageUrls.add(item);
        } else if (item is Map) {
          // Handle case where images might be objects with URL properties
          if (item['url'] != null) {
            imageUrls.add(item['url'].toString());
          } else if (item['image_url'] != null) {
            imageUrls.add(item['image_url'].toString());
          } else if (item['image'] != null) {
            imageUrls.add(item['image'].toString());
          }
        }
      }
    }

    print('Processed image URLs: $imageUrls');

    if (imageUrls.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.photo_library_outlined,
                color: Colors.grey[400],
                size: 40,
              ),
              const SizedBox(height: 8),
              Text(
                'No images available',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.5,
      ),
      itemCount: imageUrls.length,
      itemBuilder: (context, index) {
        final imageUrl = imageUrls[index];
        print('Loading image: $imageUrl');

        return GestureDetector(
          onTap: () {
            _showImageViewer(context, imageUrls, index);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                children: [
                  Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      print('Image error for $imageUrl: $error');
                      return Container(
                        color: Colors.grey[200],
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 30,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Failed to load',
                              style: GoogleFonts.poppins(
                                color: Colors.grey[500],
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded /
                                    loadingProgress.expectedTotalBytes!
                                : null,
                            strokeWidth: 2,
                          ),
                        ),
                      );
                    },
                  ),
                  // Overlay with zoom icon
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        Icons.zoom_in,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showImageViewer(
      BuildContext context, List<String> imageUrls, int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              '${initialIndex + 1} of ${imageUrls.length}',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            centerTitle: true,
          ),
          body: PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (BuildContext context, int index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: NetworkImage(imageUrls[index]),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained * 0.8,
                maxScale: PhotoViewComputedScale.covered * 2.0,
                heroAttributes: PhotoViewHeroAttributes(tag: imageUrls[index]),
              );
            },
            itemCount: imageUrls.length,
            loadingBuilder: (context, event) => Center(
              child: CircularProgressIndicator(
                value: event == null
                    ? 0
                    : event.cumulativeBytesLoaded /
                        (event.expectedTotalBytes ?? 1),
                color: Colors.white,
              ),
            ),
            backgroundDecoration: const BoxDecoration(
              color: Colors.black,
            ),
            pageController: PageController(initialPage: initialIndex),
          ),
        ),
      ),
    );
  }
}
