import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:job/services/api_service.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';

class EditProfileScreen extends StatefulWidget {
  final Map<String, dynamic> profileData;

  const EditProfileScreen({super.key, required this.profileData});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  // Add a list to store specializations from API
  List<Map<String, dynamic>> _specializations = [];
  bool _isLoadingSpecializations = false;

  // Controllers
  late TextEditingController _nameController;
  late TextEditingController _dobController;
  late TextEditingController _contactController;
  late TextEditingController _socialLinkController;
  late TextEditingController _placeController;
  late TextEditingController _aboutController;

  String _selectedGender = 'Male';
  List<int> _selectedExpertiseAreas = [];
  File? _profileImage;
  File? _resumeFile;

  // Experience dropdowns
  int _selectedYears = 0;
  int _selectedMonths = 0;

  // Work pictures
  List<dynamic> _workPictures = [];
  List<File> _newWorkPictures = [];
  List<int> _workPicturesToDelete = [];

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _fetchSpecializations();
  }

  void _loadProfile() {
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController =
        TextEditingController(text: widget.profileData['name'] ?? '');
    _dobController =
        TextEditingController(text: widget.profileData['date_of_birth'] ?? '');
    _contactController =
        TextEditingController(text: widget.profileData['contact_no'] ?? '');
    _socialLinkController =
        TextEditingController(text: widget.profileData['social_link'] ?? '');
    _placeController =
        TextEditingController(text: widget.profileData['place'] ?? '');
    _aboutController =
        TextEditingController(text: widget.profileData['about'] ?? '');

    _selectedGender = widget.profileData['gender'] == 'M'
        ? 'Male'
        : widget.profileData['gender'] == 'F'
            ? 'Female'
            : 'Male';

    // Initialize expertise areas - will be validated after specializations are loaded
    if (widget.profileData['expertise_areas'] != null) {
      _selectedExpertiseAreas =
          List<int>.from(widget.profileData['expertise_areas']);
    }

    // Initialize experience from existing data
    _initializeExperience();

    // Initialize work pictures
    if (widget.profileData['work_pictures_data'] != null) {
      _workPictures =
          List<dynamic>.from(widget.profileData['work_pictures_data']);
    }
  }

  void _initializeExperience() {
    String experienceStr = widget.profileData['years_of_experience'] ?? '0.00';
    try {
      // Parse format like "46.11" (46 years 11 months)
      List<String> parts = experienceStr.split('.');
      if (parts.length == 2) {
        int years = int.parse(parts[0]);
        int months = int.parse(parts[1]);

        // Ensure values are within valid ranges
        _selectedYears = years.clamp(0, 50);
        _selectedMonths = months.clamp(0, 11);
      } else {
        // Fallback for old format or invalid data
        _selectedYears = 0;
        _selectedMonths = 0;
      }
    } catch (e) {
      _selectedYears = 0;
      _selectedMonths = 0;
    }
  }

  String _formatExperienceForBackend() {
    // Format as "years.months" (e.g., 46.11 for 46 years 11 months)
    return '$_selectedYears.${_selectedMonths.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _dobController.dispose();
    _contactController.dispose();
    _socialLinkController.dispose();
    _placeController.dispose();
    _aboutController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _profileImage = File(image.path);
      });
    }
  }

  Future<void> _pickResume() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
      );

      if (result != null) {
        setState(() {
          _resumeFile = File(result.files.single.path!);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking file: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dobController.text.isNotEmpty
          ? DateFormat('yyyy-MM-dd').parse(_dobController.text)
          : DateTime.now(),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        _dobController.text = DateFormat('yyyy-MM-dd').format(picked);
      });
    }
  }

  Future<void> _pickWorkPicture() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _newWorkPictures.add(File(image.path));
      });
    }
  }

  void _removeWorkPicture(int index) {
    setState(() {
      if (index < _workPictures.length) {
        // Mark existing picture for deletion
        _workPicturesToDelete.add(_workPictures[index]['id']);
        _workPictures.removeAt(index);
      } else {
        // Remove newly added picture
        _newWorkPictures.removeAt(index - _workPictures.length);
      }
    });
  }

  Future<void> _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      // Validate experience
      if (_selectedYears == 0 && _selectedMonths == 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least some experience'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        // First update the profile data
        await ApiService.updateJobseekerProfile(
          name: _nameController.text,
          dateOfBirth: _dobController.text,
          gender: _selectedGender == 'Male'
              ? 'M'
              : _selectedGender == 'Female'
                  ? 'F'
                  : 'O',
          expertiseAreas: _selectedExpertiseAreas,
          yearsOfExperience: _formatExperienceForBackend(),
          contactNo: _contactController.text,
          socialLink: _socialLinkController.text,
          place: _placeController.text,
          about: _aboutController.text,
        );

        // Upload profile picture if selected
        if (_profileImage != null) {
          await ApiService.uploadProfilePicture(_profileImage!);
        }

        // Upload resume if selected
        if (_resumeFile != null) {
          await ApiService.uploadResumeFile(_resumeFile!);
        }

        // Delete work pictures if any marked for deletion
        for (int id in _workPicturesToDelete) {
          await ApiService.deleteWorkPicture(id);
        }

        // Upload new work pictures if any
        for (File workPicture in _newWorkPictures) {
          await ApiService.uploadWorkPicture(workPicture);
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.pop(context, true); // Return true to indicate success
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Add this method to fetch specializations
  Future<void> _fetchSpecializations() async {
    setState(() {
      _isLoadingSpecializations = true;
    });

    try {
      final specializations = await ApiService.fetchSpecializations();

      // Debug logging
      print('Fetched ${specializations.length} specializations');
      print(
          'Specialization IDs: ${specializations.map((s) => s['id']).toList()}');
      print(
          'Selected expertise areas before validation: $_selectedExpertiseAreas');

      // Remove duplicates and validate selected expertise areas
      final validSpecializationIds =
          specializations.map((s) => s['id'] as int).toSet();

      // Filter out invalid expertise area IDs
      _selectedExpertiseAreas = _selectedExpertiseAreas
          .where((id) => validSpecializationIds.contains(id))
          .toList();

      print(
          'Selected expertise areas after validation: $_selectedExpertiseAreas');

      setState(() {
        _specializations = specializations;
        _isLoadingSpecializations = false;
      });
    } catch (e) {
      print('Error fetching specializations: $e');
      setState(() {
        _isLoadingSpecializations = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load specializations: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Edit Profile', style: GoogleFonts.poppins()),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: Text(
              'Save',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Image
                    Center(
                      child: Stack(
                        children: [
                          CircleAvatar(
                            radius: 60,
                            backgroundImage: _profileImage != null
                                ? FileImage(_profileImage!)
                                : widget.profileData['profile_picture'] != null
                                    ? NetworkImage(
                                        widget.profileData['profile_picture']
                                            as String) as ImageProvider
                                    : null,
                            child: _profileImage == null &&
                                    widget.profileData['profile_picture'] ==
                                        null
                                ? Icon(Icons.person, size: 60)
                                : null,
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            child: GestureDetector(
                              onTap: _pickImage,
                              child: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.black,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(Icons.camera_alt,
                                    color: Colors.white, size: 20),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 24),

                    // Personal Information Section
                    _buildSectionTitle('Personal Information'),
                    SizedBox(height: 16),

                    // Name
                    _buildTextField(
                      controller: _nameController,
                      label: 'Full Name',
                      hint: 'Enter your full name',
                      icon: Icons.person,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your name';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16),

                    // Date of Birth
                    GestureDetector(
                      onTap: _selectDate,
                      child: AbsorbPointer(
                        child: _buildTextField(
                          controller: _dobController,
                          label: 'Date of Birth',
                          hint: 'YYYY-MM-DD',
                          icon: Icons.calendar_today,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your date of birth';
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    SizedBox(height: 16),

                    // Gender
                    _buildDropdownField(
                      label: 'Gender',
                      value: _selectedGender,
                      items: ['Male', 'Female', 'Other'],
                      onChanged: (value) {
                        setState(() {
                          _selectedGender = value!;
                        });
                      },
                    ),
                    SizedBox(height: 16),

                    // Contact
                    _buildTextField(
                      controller: _contactController,
                      label: 'Contact Number',
                      hint: 'Enter your phone number',
                      icon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your contact number';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16),

                    // Place
                    _buildTextField(
                      controller: _placeController,
                      label: 'Location',
                      hint: 'Enter your location',
                      icon: Icons.location_on,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter your location';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 24),

                    // Professional Information
                    _buildSectionTitle('Professional Information'),
                    SizedBox(height: 16),

                    // Experience
                    _buildExperienceSection(),
                    SizedBox(height: 16),

                    // Expertise Areas (Multi-select)
                    _buildExpertiseAreasSection(),
                    SizedBox(height: 16),

                    // Social Link
                    _buildTextField(
                      controller: _socialLinkController,
                      label: 'Social Media Link',
                      hint: 'e.g., Instagram handle',
                      icon: Icons.link,
                    ),
                    SizedBox(height: 16),

                    // Resume Upload
                    _buildFileUploadField(
                      label: 'Resume',
                      fileName: _resumeFile?.path.split('/').last ??
                          'No file selected',
                      onTap: _pickResume,
                    ),
                    SizedBox(height: 24),

                    // About
                    _buildSectionTitle('About Me'),
                    SizedBox(height: 16),
                    TextFormField(
                      controller: _aboutController,
                      maxLines: 5,
                      decoration: InputDecoration(
                        hintText: 'Tell us about yourself...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                    SizedBox(height: 40),

                    // Work Pictures
                    _buildSectionTitle('Work Portfolio'),
                    SizedBox(height: 16),

                    // Display existing and new work pictures
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Upload images of your work',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: 16),

                          // Grid of work pictures
                          GridView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 8,
                              mainAxisSpacing: 8,
                            ),
                            itemCount: _workPictures.length +
                                _newWorkPictures.length +
                                1,
                            itemBuilder: (context, index) {
                              // Add button
                              if (index ==
                                  _workPictures.length +
                                      _newWorkPictures.length) {
                                return GestureDetector(
                                  onTap: _pickWorkPicture,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(8),
                                      border:
                                          Border.all(color: Colors.grey[300]!),
                                    ),
                                    child: Icon(
                                      Icons.add_photo_alternate,
                                      color: Colors.grey[600],
                                      size: 40,
                                    ),
                                  ),
                                );
                              }

                              // Existing or new work picture
                              return Stack(
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: index < _workPictures.length
                                            ? NetworkImage(
                                                _workPictures[index]['image'])
                                            : FileImage(_newWorkPictures[index -
                                                    _workPictures.length])
                                                as ImageProvider,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Positioned(
                                    top: 0,
                                    right: 0,
                                    child: GestureDetector(
                                      onTap: () => _removeWorkPicture(index),
                                      child: Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.black.withOpacity(0.7),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 24),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.black54),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.black, width: 2),
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButton<String>(
            value: value,
            isExpanded: true,
            underline: SizedBox(),
            items: items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildFileUploadField({
    required String label,
    required String fileName,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8),
        InkWell(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(Icons.attach_file, color: Colors.black54),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    fileName,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(Icons.upload, color: Colors.black54),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExperienceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Years of Experience',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Years',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 4),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: DropdownButton<int>(
                      value: _selectedYears,
                      isExpanded: true,
                      underline: SizedBox(),
                      items:
                          List.generate(51, (index) => index).map((int year) {
                        return DropdownMenuItem<int>(
                          value: year,
                          child: Text(year.toString()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedYears = value ?? 0;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Months',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 4),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: DropdownButton<int>(
                      value: _selectedMonths,
                      isExpanded: true,
                      underline: SizedBox(),
                      items:
                          List.generate(12, (index) => index).map((int month) {
                        return DropdownMenuItem<int>(
                          value: month,
                          child: Text(month.toString()),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedMonths = value ?? 0;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Text(
          'Total: ${_selectedYears} years ${_selectedMonths} months (${_formatExperienceForBackend()})',
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildExpertiseAreasSection() {
    if (_isLoadingSpecializations) {
      return Center(child: CircularProgressIndicator());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Expertise Areas',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _specializations.map((specialization) {
                  return _buildExpertiseChip(
                    specialization['name'],
                    specialization['id'],
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExpertiseChip(String label, int id) {
    final isSelected = _selectedExpertiseAreas.contains(id);

    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.poppins(
          color: isSelected ? Colors.white : Colors.black87,
          fontSize: 13,
        ),
      ),
      selected: isSelected,
      checkmarkColor: Colors.white,
      selectedColor: Colors.black,
      backgroundColor: Colors.grey[200],
      onSelected: (selected) {
        setState(() {
          if (selected) {
            _selectedExpertiseAreas.add(id);
          } else {
            _selectedExpertiseAreas.remove(id);
          }
        });
      },
    );
  }
}
