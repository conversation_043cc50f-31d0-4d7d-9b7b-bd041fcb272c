import 'package:flutter/material.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/uttilits/image_const.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';
import 'package:android_intent_plus/android_intent.dart' as android_intent;

class WhatsNewDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> blogPost;

  const WhatsNewDetailsScreen({Key? key, required this.blogPost})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get image from API or use local fallback
    String? imageUrl;
    bool hasYoutubeVideo = false;
    String? youtubeVideoUrl;

    // First check for images from API
    if (blogPost['images'] != null && (blogPost['images'] as List).isNotEmpty) {
      imageUrl = (blogPost['images'] as List).first['image'];
    }
    // Then check for YouTube thumbnail if available
    else if (blogPost['youtube_video_url'] != null &&
        blogPost['youtube_video_url'].toString().isNotEmpty) {
      String videoId = '';
      String url = blogPost['youtube_video_url'];
      hasYoutubeVideo = true;
      youtubeVideoUrl = url;

      // Extract video ID from YouTube URL
      if (url.contains('youtu.be')) {
        videoId = url.split('/').last.split('?').first;
      } else if (url.contains('youtube.com')) {
        videoId = Uri.parse(url).queryParameters['v'] ?? '';
      }

      if (videoId.isNotEmpty) {
        imageUrl = 'https://img.youtube.com/vi/$videoId/0.jpg';
      }
    }
    // Finally use local image if available
    else if (blogPost['image'] != null) {
      imageUrl = blogPost['image'];
    }

    // Format date from API or use the one from local data
    String displayDate = blogPost['created_at'] != null
        ? _formatDate(blogPost['created_at'])
        : blogPost['date'] ?? 'Date not available';

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  imageUrl != null && imageUrl.startsWith('http')
                      ? Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              ImageConstants.image1,
                              fit: BoxFit.cover,
                            );
                          },
                        )
                      : Image.asset(
                          imageUrl ?? ImageConstants.image1,
                          fit: BoxFit.cover,
                        ),
                  if (hasYoutubeVideo)
                    Center(
                      child: IconButton(
                        icon: Icon(
                          Icons.play_circle_fill,
                          size: 64,
                          color: Colors.white.withOpacity(0.8),
                        ),
                        onPressed: () =>
                            _launchYoutubeVideo(context, youtubeVideoUrl!),
                      ),
                    ),
                ],
              ),
            ),
            backgroundColor: Colors.black,
            leading: IconButton(
              icon: Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.arrow_back, color: Colors.white),
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),

          // Content
          SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category and date
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 5,
                        ),
                        decoration: BoxDecoration(
                          color: ColorConstants.PRIMARYCOLOR.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          blogPost['category'] ?? "What's Trending",
                          style: TextStyle(
                            color: ColorConstants.PRIMARYCOLOR,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                      SizedBox(width: 10),
                      Text(
                        displayDate,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 15),

                  // Title
                  Text(
                    blogPost['title'] ?? 'No Title',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 20),

                  // Content
                  Text(
                    blogPost['description'] ??
                        blogPost['content'] ??
                        'No content available',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                      height: 1.6,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Format date from API
  String _formatDate(String apiDate) {
    try {
      final date = DateTime.parse(apiDate);
      return '${_getMonth(date.month)} ${date.day}, ${date.year}';
    } catch (e) {
      return apiDate;
    }
  }

  // Get month name
  String _getMonth(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  // Launch YouTube video with improved error handling
  Future<void> _launchYoutubeVideo(BuildContext context, String url) async {
    try {
      print('Original YouTube URL: $url');

      // Ensure the URL is properly formatted
      Uri uri;

      // Check if it's a youtu.be short link or regular youtube.com link
      if (url.contains('youtu.be')) {
        // Extract video ID from youtu.be URL
        final videoId = url.split('/').last.split('?').first;
        uri = Uri.parse('https://www.youtube.com/watch?v=$videoId');
      } else {
        uri = Uri.parse(url);
      }

      print('Processed URL: ${uri.toString()}');

      // Try different launch methods
      bool launched = false;

      // 1. Try using android_intent_plus for Android
      if (Platform.isAndroid) {
        try {
          final intent = android_intent.AndroidIntent(
            action: 'action_view',
            data: uri.toString(),
            package: 'com.google.android.youtube',
          );
          await intent.launch();
          print('Launched via Android Intent');
          return;
        } catch (e) {
          print('Android Intent failed: $e');
          // Continue to other methods if this fails
        }
      }

      // 2. Try external application
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
        print('Launch result (external): $launched');

        if (launched) return;
      }

      // 3. Try platform default as fallback
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.platformDefault);
        print('Launch result (platform): $launched');

        if (launched) return;
      }

      // 4. Last resort: try opening in browser
      final browserUri = Uri.parse(
        'https://www.youtube.com/watch?v=${uri.queryParameters['v'] ?? uri.pathSegments.last}',
      );
      if (await canLaunchUrl(browserUri)) {
        launched = await launchUrl(
          browserUri,
          mode: LaunchMode.externalApplication,
        );
        print('Launch result (browser): $launched');

        if (!launched) {
          throw 'Could not launch YouTube video';
        }
      } else {
        throw 'Could not parse YouTube URL';
      }
    } catch (e) {
      print('Error launching YouTube video: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Could not open video: $e')));
    }
  }
}
