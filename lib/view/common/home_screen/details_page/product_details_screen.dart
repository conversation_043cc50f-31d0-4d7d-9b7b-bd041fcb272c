import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/uttilits/image_const.dart';

class ProductDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> product;

  const ProductDetailsScreen({Key? key, required this.product})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get image URL from API
    String? imageUrl;
    if (product['images'] != null && (product['images'] as List).isNotEmpty) {
      imageUrl = (product['images'] as List).first['image'];
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        actionsPadding: EdgeInsets.only(right: 16),
        toolbarHeight: 50,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 22),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Product Details',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ),
      body: CustomScrollView(
        slivers: [
          // App bar with image
          SliverAppBar(
            expandedHeight: 350,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: imageUrl != null
                  ? Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          ImageConstants.image1,
                          fit: BoxFit.cover,
                        );
                      },
                    )
                  : Image.asset(
                      product['image'] ?? ImageConstants.image1,
                      fit: BoxFit.cover,
                    ),
            ),
            automaticallyImplyLeading: false,
            backgroundColor: Colors.black,
          ),

          // Content
          SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      color: ColorConstants.PRIMARYCOLOR.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      product['category'] ?? 'Product',
                      style: GoogleFonts.poppins(
                        color: ColorConstants.PRIMARYCOLOR,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  SizedBox(height: 15),

                  // Price
                  Text(
                    '₹${product['rate'] ?? product['price'] ?? '0.00'}',
                    style: GoogleFonts.poppins(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.PRIMARYCOLOR,
                    ),
                  ),
                  SizedBox(height: 10),

                  // Title
                  Text(
                    product['product_name'] ?? product['title'] ?? 'Product',
                    style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        color: ColorConstants.black),
                  ),
                  SizedBox(height: 5),

                  // Brand
                  if (product['brand'] != null) ...[
                    Text(
                      'Brand: ${product['brand']}',
                      style: GoogleFonts.poppins(
                          fontSize: 16, color: Colors.grey[600]),
                    ),
                    SizedBox(height: 15),
                  ],

                  // Description
                  Text(
                    'Description',
                    style: GoogleFonts.poppins(
                        fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 10),
                  Text(
                    product['description'] ??
                        product['content'] ??
                        'No description available',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: Colors.grey[800],
                      height: 1.5,
                    ),
                  ),
                  SizedBox(height: 20),

                  // How to use
                  if (product['how_to_use'] != null) ...[
                    Text(
                      'How to Use',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      product['how_to_use'],
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[800],
                        height: 1.5,
                      ),
                    ),
                    SizedBox(height: 20),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
