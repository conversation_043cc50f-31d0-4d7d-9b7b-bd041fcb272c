import 'package:flutter/material.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/services/api_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:io';
import 'package:android_intent_plus/android_intent.dart' as android_intent;

class TrainingPostDetailScreen extends StatefulWidget {
  final Map<String, dynamic> post;

  const TrainingPostDetailScreen({Key? key, required this.post})
      : super(key: key);

  @override
  State<TrainingPostDetailScreen> createState() =>
      _TrainingPostDetailScreenState();
}

class _TrainingPostDetailScreenState extends State<TrainingPostDetailScreen> {
  bool isLoading = false;
  String errorMessage = '';
  Map<String, dynamic> detailedPost = {};
  int _currentImageIndex = 0;
  final CarouselSliderController _carouselController =
      CarouselSliderController();

  @override
  void initState() {
    super.initState();
    detailedPost = widget.post;

    print('Initial post data: ${widget.post}');

    // If we have an ID, fetch the detailed post
    if (widget.post['id'] != null) {
      print('Post ID found: ${widget.post['id']}');
      _fetchDetailedPost(widget.post['id']);
    } else {
      print('No post ID found in the widget post data');
    }
  }

  Future<void> _fetchDetailedPost(int id) async {
    try {
      print('Fetching detailed post for ID: $id');
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final post = await ApiService.fetchTrainingBlogDetail(id);
      print('Received detailed post: $post');

      setState(() {
        // Make sure post is a Map before spreading
        detailedPost = {...widget.post, ...post};
        isLoading = false;
      });
      print('Updated detailed post: $detailedPost');
    } catch (e) {
      print('Error in _fetchDetailedPost: $e');
      setState(() {
        errorMessage = 'Failed to load training details: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Format date from API or use the one from local data
    String displayDate = detailedPost['created_at'] != null
        ? _formatDate(detailedPost['created_at'])
        : detailedPost['date'] ?? 'Date not available';

    // Get images from API or use local fallback
    List<dynamic> carouselItems = [];

    // 1. Add images first if available
    if (detailedPost['images'] != null &&
        (detailedPost['images'] as List).isNotEmpty) {
      carouselItems.addAll(
        (detailedPost['images'] as List).map(
          (img) => {'type': 'image', 'url': img['image'] as String},
        ),
      );
    } else if (detailedPost['image'] != null) {
      // Add local image if available
      carouselItems.add({'type': 'image', 'url': detailedPost['image']});
    }

    // 2. Add YouTube video as the last item if available
    if (detailedPost['youtube_video_url'] != null &&
        detailedPost['youtube_video_url'].toString().isNotEmpty) {
      carouselItems.add({
        'type': 'video',
        'url': detailedPost['youtube_video_url'].toString(),
      });
    }

    // If no items, add placeholder
    if (carouselItems.isEmpty) {
      carouselItems.add({
        'type': 'image',
        'url': 'assets/images/placeholder.jpg',
      });
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        actionsPadding: EdgeInsets.only(right: 16),
        toolbarHeight: 50,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 22),
          onPressed: () => Navigator.pop(context),
        ),
        title: Padding(
          padding: const EdgeInsets.only(right: 180),
          child: Text(
            'Training Details',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ColorConstants.black),
                    strokeWidth: 2.5,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading training details...',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            )
          : errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Something went wrong',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      SizedBox(height: 8),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.poppins(
                            color: Colors.grey.shade600,
                            fontSize: 13,
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () {
                          if (detailedPost['id'] != null) {
                            _fetchDetailedPost(detailedPost['id']);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorConstants.black,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          'Try Again',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : CustomScrollView(
                  slivers: [
                    // App Bar with Image Carousel
                    SliverAppBar(
                      expandedHeight: 250,
                      pinned: true,
                      automaticallyImplyLeading: false,
                      backgroundColor: Colors.white,
                      flexibleSpace: FlexibleSpaceBar(
                        background: Stack(
                          children: [
                            // Image Carousel
                            CarouselSlider(
                              carouselController: _carouselController,
                              options: CarouselOptions(
                                height: 300,
                                viewportFraction: 1.0,
                                enlargeCenterPage: false,
                                autoPlay: carouselItems.length > 1,
                                autoPlayInterval: Duration(seconds: 5),
                                onPageChanged: (index, reason) {
                                  setState(() {
                                    _currentImageIndex = index;
                                  });
                                },
                              ),
                              items: carouselItems.map((item) {
                                return Builder(
                                  builder: (BuildContext context) {
                                    if (item['type'] == 'image') {
                                      String url = item['url'];
                                      return url.startsWith('http')
                                          ? Image.network(
                                              url,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  width: double.infinity,
                                                  color: Colors.grey.shade100,
                                                  child: Icon(
                                                    Icons.image,
                                                    size: 50,
                                                    color: Colors.grey.shade400,
                                                  ),
                                                );
                                              },
                                            )
                                          : Image.asset(
                                              url,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                            );
                                    } else {
                                      // YouTube video thumbnail with play button overlay
                                      String videoId = '';

                                      // Extract video ID from YouTube URL
                                      if (item['url'].contains('youtu.be')) {
                                        videoId = item['url']
                                            .split('/')
                                            .last
                                            .split('?')
                                            .first;
                                      } else if (item['url'].contains(
                                        'youtube.com',
                                      )) {
                                        videoId = Uri.parse(
                                              item['url'],
                                            ).queryParameters['v'] ??
                                            '';
                                      }

                                      // YouTube thumbnail URL
                                      String thumbnailUrl =
                                          'https://img.youtube.com/vi/$videoId/0.jpg';

                                      return Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          // YouTube thumbnail
                                          Image.network(
                                            thumbnailUrl,
                                            fit: BoxFit.cover,
                                            width: double.infinity,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Container(
                                                width: double.infinity,
                                                color: Colors.grey.shade100,
                                                child: Icon(
                                                  Icons.play_circle_outline,
                                                  size: 60,
                                                  color: Colors.grey.shade400,
                                                ),
                                              );
                                            },
                                          ),
                                          // Play button overlay
                                          InkWell(
                                            onTap: () {
                                              _launchYoutubeVideo(item['url']);
                                            },
                                            child: Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: Colors.red,
                                                shape: BoxShape.circle,
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.2),
                                                    blurRadius: 8,
                                                    offset: Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                              child: Icon(
                                                Icons.play_arrow,
                                                color: Colors.white,
                                                size: 30,
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    }
                                  },
                                );
                              }).toList(),
                            ),

                            // Page indicator
                            if (carouselItems.length > 1)
                              Positioned(
                                bottom: 20,
                                left: 0,
                                right: 0,
                                child: Center(
                                  child: AnimatedSmoothIndicator(
                                    activeIndex: _currentImageIndex,
                                    count: carouselItems.length,
                                    effect: ExpandingDotsEffect(
                                      dotHeight: 6,
                                      dotWidth: 6,
                                      activeDotColor: ColorConstants.black,
                                      dotColor: Colors.grey.shade400,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Content
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title
                            Text(
                              detailedPost['title'] ?? 'No Title',
                              style: GoogleFonts.poppins(
                                fontSize: 22,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                                height: 1.3,
                              ),
                            ),
                            SizedBox(height: 12),

                            // Date and Author info
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 16,
                                  color: Colors.grey.shade600,
                                ),
                                SizedBox(width: 6),
                                Text(
                                  displayDate,
                                  style: GoogleFonts.poppins(
                                    color: Colors.grey.shade600,
                                    fontSize: 13,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                if (detailedPost['author'] != null) ...[
                                  SizedBox(width: 20),
                                  Icon(
                                    Icons.person,
                                    size: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                  SizedBox(width: 6),
                                  Text(
                                    detailedPost['author'],
                                    style: GoogleFonts.poppins(
                                      color: Colors.grey.shade600,
                                      fontSize: 13,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            SizedBox(height: 20),

                            // Price if available
                            if (detailedPost['price'] != null) ...[
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: ColorConstants.black.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color:
                                        ColorConstants.black.withOpacity(0.2),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  'Price: ${detailedPost['price']}',
                                  style: GoogleFonts.poppins(
                                    color: ColorConstants.black,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              SizedBox(height: 20),
                            ],

                            // Description
                            Text(
                              detailedPost['description'] ??
                                  detailedPost['content'] ??
                                  'No description available',
                              style: GoogleFonts.poppins(
                                fontSize: 15,
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.w400,
                                height: 1.5,
                              ),
                            ),

                            SizedBox(height: 30),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
      bottomNavigationBar: !isLoading && errorMessage.isEmpty
          ? Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Colors.grey.shade200, width: 1),
                ),
              ),
              child: ElevatedButton(
                onPressed: () => _showRegistrationDialog(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorConstants.black,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  'Register for Training',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            )
          : null,
    );
  }

  // Format date from API
  String _formatDate(String apiDate) {
    try {
      final date = DateTime.parse(apiDate);
      return '${_getMonth(date.month)} ${date.day}, ${date.year}';
    } catch (e) {
      return apiDate;
    }
  }

  // Get month name
  String _getMonth(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  // Launch YouTube video
  Future<void> _launchYoutubeVideo(String url) async {
    try {
      print('Original YouTube URL: $url');

      // Ensure the URL is properly formatted
      Uri uri;

      // Check if it's a youtu.be short link or regular youtube.com link
      if (url.contains('youtu.be')) {
        // Extract video ID from youtu.be URL
        final videoId = url.split('/').last.split('?').first;
        uri = Uri.parse('https://www.youtube.com/watch?v=$videoId');
      } else {
        uri = Uri.parse(url);
      }

      print('Processed URL: ${uri.toString()}');

      // Try different launch methods
      bool launched = false;

      // 1. Try using android_intent_plus for Android
      if (Platform.isAndroid) {
        try {
          final intent = android_intent.AndroidIntent(
            action: 'action_view',
            data: uri.toString(),
            package: 'com.google.android.youtube',
          );
          await intent.launch();
          print('Launched via Android Intent');
          return;
        } catch (e) {
          print('Android Intent failed: $e');
          // Continue to other methods if this fails
        }
      }

      // 2. Try external application
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
        print('Launch result (external): $launched');

        if (launched) return;
      }

      // 3. Try platform default as fallback
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.platformDefault);
        print('Launch result (platform): $launched');

        if (launched) return;
      }

      // 4. Last resort: try opening in browser
      final browserUri = Uri.parse(
        'https://www.youtube.com/watch?v=${uri.queryParameters['v']}',
      );
      if (await canLaunchUrl(browserUri)) {
        launched = await launchUrl(
          browserUri,
          mode: LaunchMode.externalApplication,
        );
        print('Launch result (browser): $launched');

        if (!launched) {
          throw 'Could not launch YouTube video';
        }
      } else {
        throw 'Could not parse YouTube URL';
      }
    } catch (e) {
      print('Error launching YouTube video: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Could not open video: $e')));
    }
  }

  Widget _buildConfirmationDetail(IconData icon, String label, String value,
      {int maxLines = 2}) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade700),
        SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w400,
              ),
            ),
            Text(
              value,
              maxLines: maxLines,
              overflow: TextOverflow.ellipsis,
              style: GoogleFonts.poppins(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showRegistrationDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final phoneController = TextEditingController();
    final pinCodeController = TextEditingController();
    final salonNameController = TextEditingController();
    final locationController = TextEditingController();
    bool isSubmitting = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 0,
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200, width: 1),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: ColorConstants.black.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.event_available,
                              color: ColorConstants.black,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Register for Training',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: Icon(Icons.close, size: 20),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                      SizedBox(height: 16),

                      // Training info summary
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border:
                              Border.all(color: Colors.grey.shade200, width: 1),
                        ),
                        child: Row(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.asset(
                                widget.post['image'] ??
                                    'assets/images/placeholder.jpg',
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 50,
                                    height: 50,
                                    color: Colors.grey.shade200,
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey.shade400,
                                      size: 20,
                                    ),
                                  );
                                },
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.post['title'] ?? 'Training Program',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                      color: Colors.black87,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.calendar_today,
                                        size: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                      SizedBox(width: 4),
                                      Text(
                                        detailedPost['training_date'] ??
                                            'Date TBD',
                                        style: GoogleFonts.poppins(
                                          fontSize: 12,
                                          color: Colors.grey.shade600,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      if (detailedPost['price'] != null) ...[
                                        SizedBox(width: 12),
                                        Icon(
                                          Icons.attach_money,
                                          size: 12,
                                          color: Colors.grey.shade600,
                                        ),
                                        SizedBox(width: 4),
                                        Text(
                                          detailedPost['price'],
                                          style: GoogleFonts.poppins(
                                            fontSize: 12,
                                            color: Colors.grey.shade600,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 20),

                      // Form
                      Form(
                        key: formKey,
                        child: Column(
                          children: [
                            TextFormField(
                              controller: nameController,
                              decoration: InputDecoration(
                                labelText: 'Full Name',
                                hintText: 'Enter your full name',
                                prefixIcon: Icon(
                                  Icons.person,
                                  color: ColorConstants.black,
                                  size: 20,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorConstants.black,
                                    width: 1.5,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 12),
                                labelStyle: GoogleFonts.poppins(fontSize: 14),
                                hintStyle: GoogleFonts.poppins(
                                    fontSize: 14, color: Colors.grey.shade500),
                              ),
                              style: GoogleFonts.poppins(fontSize: 14),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your name';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),
                            TextFormField(
                              controller: phoneController,
                              decoration: InputDecoration(
                                labelText: 'Phone Number',
                                hintText: 'Enter your phone number',
                                prefixIcon: Icon(
                                  Icons.phone,
                                  color: ColorConstants.black,
                                  size: 20,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorConstants.black,
                                    width: 1.5,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 12),
                                labelStyle: GoogleFonts.poppins(fontSize: 14),
                                hintStyle: GoogleFonts.poppins(
                                    fontSize: 14, color: Colors.grey.shade500),
                              ),
                              style: GoogleFonts.poppins(fontSize: 14),
                              keyboardType: TextInputType.phone,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your phone number';
                                }
                                if (!RegExp(r'^\+?[0-9]{10,12}$')
                                    .hasMatch(value)) {
                                  return 'Please enter a valid phone number';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),
                            TextFormField(
                              controller: pinCodeController,
                              decoration: InputDecoration(
                                labelText: 'Pin Code',
                                hintText: 'Enter pin code',
                                prefixIcon: Icon(
                                  Icons.pin_drop,
                                  color: ColorConstants.black,
                                  size: 20,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorConstants.black,
                                    width: 1.5,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 12),
                                labelStyle: GoogleFonts.poppins(fontSize: 14),
                                hintStyle: GoogleFonts.poppins(
                                    fontSize: 14, color: Colors.grey.shade500),
                              ),
                              style: GoogleFonts.poppins(fontSize: 14),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your pin code';
                                }
                                if (value.length != 6) {
                                  return 'Pin code must be 6 digits';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),
                            TextFormField(
                              controller: salonNameController,
                              decoration: InputDecoration(
                                labelText: 'Salon Name',
                                hintText: 'Enter your salon name',
                                prefixIcon: Icon(
                                  Icons.business,
                                  color: ColorConstants.black,
                                  size: 20,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorConstants.black,
                                    width: 1.5,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 12),
                                labelStyle: GoogleFonts.poppins(fontSize: 14),
                                hintStyle: GoogleFonts.poppins(
                                    fontSize: 14, color: Colors.grey.shade500),
                              ),
                              style: GoogleFonts.poppins(fontSize: 14),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your salon name';
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16),
                            TextFormField(
                              controller: locationController,
                              decoration: InputDecoration(
                                labelText: 'Location',
                                hintText: 'Enter your location',
                                prefixIcon: Icon(
                                  Icons.location_on,
                                  color: ColorConstants.black,
                                  size: 20,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: ColorConstants.black,
                                    width: 1.5,
                                  ),
                                ),
                                filled: true,
                                fillColor: Colors.grey.shade50,
                                contentPadding: EdgeInsets.symmetric(
                                    vertical: 12, horizontal: 12),
                                labelStyle: GoogleFonts.poppins(fontSize: 14),
                                hintStyle: GoogleFonts.poppins(
                                    fontSize: 14, color: Colors.grey.shade500),
                              ),
                              style: GoogleFonts.poppins(fontSize: 14),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your location';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 24),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: isSubmitting
                                  ? null
                                  : () => Navigator.pop(context),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: ColorConstants.black,
                                side: BorderSide(color: ColorConstants.black),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: EdgeInsets.symmetric(vertical: 14),
                              ),
                              child: Text(
                                'Cancel',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isSubmitting
                                  ? null
                                  : () async {
                                      if (formKey.currentState!.validate()) {
                                        try {
                                          setState(() {
                                            isSubmitting = true;
                                          });

                                          final trainingId = detailedPost['id'];
                                          if (trainingId == null) {
                                            throw Exception(
                                                'Training ID not found');
                                          }

                                          final response = await ApiService
                                              .registerForTraining(
                                            trainingId: trainingId,
                                            ownerName: nameController.text,
                                            salonName: salonNameController.text,
                                            phoneNo: phoneController.text,
                                            pinCode: pinCodeController.text,
                                            location: locationController.text,
                                          );

                                          Navigator.pop(context);

                                          _showConfirmationDialog(context, {
                                            'name': response['owner_name'] ??
                                                nameController.text,
                                            'phone': response['phone_no'] ??
                                                phoneController.text,
                                            'trainingTitle':
                                                response['training_title'] ??
                                                    detailedPost['title'],
                                            'trainingDate':
                                                detailedPost['training_date'] ??
                                                    'Date will be communicated',
                                            'registrationId':
                                                response['id']?.toString() ??
                                                    'Pending',
                                            'location': response['location'] ??
                                                locationController.text,
                                          });
                                        } catch (e) {
                                          setState(() {
                                            isSubmitting = false;
                                          });

                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                  'Registration failed: $e'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorConstants.black,
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                padding: EdgeInsets.symmetric(vertical: 14),
                              ),
                              child: isSubmitting
                                  ? SizedBox(
                                      height: 18,
                                      width: 18,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                Colors.white),
                                      ),
                                    )
                                  : Text(
                                      'Register',
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showConfirmationDialog(
    BuildContext context,
    Map<String, String> registrationData,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade200, width: 1),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Success icon
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Registration Successful!',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Registration details
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildConfirmationDetail(
                          Icons.confirmation_number,
                          'Registration ID',
                          registrationData['registrationId']!,
                        ),
                        Divider(height: 16),
                        _buildConfirmationDetail(
                          Icons.person,
                          'Name',
                          registrationData['name']!,
                        ),
                        Divider(height: 16),
                        _buildConfirmationDetail(
                          Icons.phone,
                          'Phone',
                          registrationData['phone']!,
                        ),
                        Divider(height: 16),
                        _buildConfirmationDetail(
                          Icons.location_on,
                          'Location',
                          registrationData['location']!,
                        ),
                        Divider(height: 16),
                        _buildConfirmationDetail(
                          Icons.event_note,
                          'Training',
                          registrationData['trainingTitle']!,
                          maxLines: 1, // Add this to prevent overflow
                        ),
                        Divider(height: 16),
                        _buildConfirmationDetail(
                          Icons.calendar_today,
                          'Date',
                          registrationData['trainingDate']!,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Info box
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withOpacity(0.15)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue, size: 18),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'You will receive payment details via SMS shortly.',
                            style: GoogleFonts.poppins(fontSize: 13),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),

                  // OK button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        elevation: 0,
                      ),
                      child: Text(
                        'OK',
                        style: GoogleFonts.poppins(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
