// ignore_for_file: use_super_parameters

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/services/api_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class ConsultationDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> consultation;

  const ConsultationDetailsScreen({Key? key, required this.consultation})
      : super(key: key);

  @override
  State<ConsultationDetailsScreen> createState() =>
      _ConsultationDetailsScreenState();
}

class _ConsultationDetailsScreenState extends State<ConsultationDetailsScreen> {
  bool isLoading = false;
  String errorMessage = '';
  Map<String, dynamic> detailedConsultation = {};
  int _currentImageIndex = 0;

  @override
  void initState() {
    super.initState();
    detailedConsultation = widget.consultation;

    // If we have an ID, fetch the detailed consultation
    if (widget.consultation['id'] != null) {
      _fetchDetailedConsultation(widget.consultation['id']);
    }
  }

  Future<void> _fetchDetailedConsultation(int id) async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final consultation = await ApiService.fetchConsultationDetail(id);

      setState(() {
        detailedConsultation = {...widget.consultation, ...consultation};
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load consultation details: $e';
        isLoading = false;
      });
      print('Error fetching consultation detail: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Format date from API or use the one from local data
    String displayDate = detailedConsultation['created_at'] != null
        ? _formatDate(detailedConsultation['created_at'])
        : '';

    // Get images for carousel
    List<dynamic> carouselItems = [];
    bool hasVideo = false;

    // Add images first if available
    if (detailedConsultation['images'] != null &&
        (detailedConsultation['images'] as List).isNotEmpty) {
      carouselItems.addAll(
        (detailedConsultation['images'] as List).map(
          (img) => {'type': 'image', 'url': img['image'] as String},
        ),
      );
    } else if (detailedConsultation['image'] != null) {
      // Add local image if available
      carouselItems.add({
        'type': 'image',
        'url': detailedConsultation['image'],
      });
    }

    // Add YouTube video if available (only once)
    if (detailedConsultation['youtube_video_url'] != null &&
        detailedConsultation['youtube_video_url'].toString().isNotEmpty &&
        !hasVideo) {
      carouselItems.add({
        'type': 'video',
        'url': detailedConsultation['youtube_video_url'],
      });
      hasVideo = true;
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black87, size: 20),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Consultation Details',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 20,
                          offset: Offset(0, 8),
                        ),
                      ],
                    ),
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(ColorConstants.black),
                      strokeWidth: 3,
                    ),
                  ),
                  SizedBox(height: 24),
                  Text(
                    'Loading details...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : errorMessage.isNotEmpty
              ? Center(
                  child: Container(
                    margin: EdgeInsets.all(24),
                    padding: EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 24,
                          offset: Offset(0, 12),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.red[400],
                          ),
                        ),
                        SizedBox(height: 24),
                        Text(
                          'Oops! Something went wrong',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 12),
                        Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                            height: 1.4,
                          ),
                        ),
                        SizedBox(height: 28),
                        ElevatedButton(
                          onPressed: () {
                            if (detailedConsultation['id'] != null) {
                              _fetchDetailedConsultation(
                                  detailedConsultation['id']);
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstants.black,
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(
                                horizontal: 32, vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.refresh, size: 18),
                              SizedBox(width: 8),
                              Text(
                                'Try Again',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : CustomScrollView(
                  slivers: [
                    // App Bar with Image Carousel
                    SliverAppBar(
                      automaticallyImplyLeading: false,
                      expandedHeight: 280,
                      //   pinned: true,
                      backgroundColor: Colors.white,
                      elevation: 0,
                      leading: null,
                      flexibleSpace: FlexibleSpaceBar(
                        background: Stack(
                          children: [
                            // Image Carousel
                            CarouselSlider(
                              carouselController: null,
                              options: CarouselOptions(
                                viewportFraction: 1.0,
                                enlargeCenterPage: false,
                                autoPlay: carouselItems.length > 1,
                                autoPlayInterval: Duration(seconds: 5),
                                onPageChanged: (index, reason) {
                                  setState(() {
                                    _currentImageIndex = index;
                                  });
                                },
                              ),
                              items: carouselItems.map((item) {
                                return Builder(
                                  builder: (BuildContext context) {
                                    if (item['type'] == 'image') {
                                      String url = item['url'];
                                      return url.startsWith('http')
                                          ? Image.network(
                                              url,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  width: double.infinity,
                                                  decoration: BoxDecoration(
                                                    gradient: LinearGradient(
                                                      colors: [
                                                        Colors.grey[200]!,
                                                        Colors.grey[300]!
                                                      ],
                                                      begin: Alignment.topLeft,
                                                      end:
                                                          Alignment.bottomRight,
                                                    ),
                                                  ),
                                                  child: Icon(
                                                    Icons.image,
                                                    size: 60,
                                                    color: Colors.grey[500],
                                                  ),
                                                );
                                              },
                                            )
                                          : Image.asset(
                                              url,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                            );
                                    } else {
                                      // YouTube video thumbnail with play button overlay
                                      String videoId = '';
                                      String url = item['url'];

                                      // Extract video ID from YouTube URL
                                      if (url.contains('youtu.be')) {
                                        videoId = url
                                            .split('/')
                                            .last
                                            .split('?')
                                            .first;
                                      } else if (url.contains('youtube.com')) {
                                        videoId = Uri.parse(url)
                                                .queryParameters['v'] ??
                                            '';
                                      }

                                      // YouTube thumbnail URL
                                      String thumbnailUrl =
                                          'https://img.youtube.com/vi/$videoId/0.jpg';

                                      return Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          // YouTube thumbnail
                                          Image.network(
                                            thumbnailUrl,
                                            fit: BoxFit.cover,
                                            width: double.infinity,
                                            errorBuilder:
                                                (context, error, stackTrace) {
                                              return Container(
                                                width: double.infinity,
                                                decoration: BoxDecoration(
                                                  gradient: LinearGradient(
                                                    colors: [
                                                      Colors.grey[200]!,
                                                      Colors.grey[300]!
                                                    ],
                                                    begin: Alignment.topLeft,
                                                    end: Alignment.bottomRight,
                                                  ),
                                                ),
                                                child: Icon(
                                                  Icons.play_circle_outline,
                                                  size: 80,
                                                  color: Colors.grey[500],
                                                ),
                                              );
                                            },
                                          ),
                                          // Play button overlay
                                          InkWell(
                                            onTap: () {
                                              _launchYoutubeVideo(item['url']);
                                            },
                                            child: Container(
                                              width: 55,
                                              height: 55,
                                              decoration: BoxDecoration(
                                                color: Colors.red,
                                                shape: BoxShape.circle,
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.3),
                                                    blurRadius: 12,
                                                    offset: Offset(0, 4),
                                                  ),
                                                ],
                                              ),
                                              child: Icon(
                                                Icons.play_arrow,
                                                color: Colors.white,
                                                size: 40,
                                              ),
                                            ),
                                          ),
                                        ],
                                      );
                                    }
                                  },
                                );
                              }).toList(),
                            ),

                            // Page indicator
                            if (carouselItems.length > 1)
                              Positioned(
                                bottom: 20,
                                left: 0,
                                right: 0,
                                child: Center(
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withOpacity(0.6),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: AnimatedSmoothIndicator(
                                      activeIndex: _currentImageIndex,
                                      count: carouselItems.length,
                                      effect: ExpandingDotsEffect(
                                        dotHeight: 6,
                                        dotWidth: 6,
                                        activeDotColor: Colors.white,
                                        dotColor: Colors.white.withOpacity(0.5),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                    // Content
                    SliverToBoxAdapter(
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title
                            Text(
                              detailedConsultation['title'] ?? 'No Title',
                              style: GoogleFonts.poppins(
                                fontSize: 26,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 10),

                            // Date info
                            if (displayDate.isNotEmpty) ...[
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.calendar_today,
                                      size: 14,
                                      color: Colors.grey[600],
                                    ),
                                    SizedBox(width: 6),
                                    Text(
                                      displayDate,
                                      style: GoogleFonts.poppins(
                                        color: Colors.grey[600],
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                            const SizedBox(height: 24),

                            // Description
                            Container(
                              padding: EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.grey[200]!,
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        'Description',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 16),
                                  Text(
                                    detailedConsultation['description'] ??
                                        detailedConsultation['content'] ??
                                        'No description available',
                                    style: GoogleFonts.poppins(
                                      fontSize: 15,
                                      color: Colors.grey[700],
                                      height: 1.6,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }

  String _formatDate(String? apiDate) {
    if (apiDate == null) return '';

    try {
      final date = DateTime.parse(apiDate);
      return '${_getMonth(date.month)} ${date.day}, ${date.year}';
    } catch (e) {
      return apiDate;
    }
  }

  String _getMonth(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  Future<void> _launchYoutubeVideo(String? url) async {
    if (url == null) return;

    try {
      final Uri uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      print('Error launching YouTube video: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(
        content: Text('Could not open video: $e'),
        backgroundColor: Colors.red[400],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ));
    }
  }
}
