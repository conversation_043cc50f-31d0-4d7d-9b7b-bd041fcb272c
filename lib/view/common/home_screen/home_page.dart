import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;
import 'package:job/uttilits/image_const.dart';
import 'package:job/view/common/home_screen/pages/consultation_screen.dart';
import 'package:job/view/common/home_screen/pages/products_screen.dart';
import 'package:job/view/common/home_screen/pages/training_screen.dart';
import 'package:job/view/common/home_screen/pages/whats_new_screen.dart';
import 'package:job/view/common/welcome_screen/welcome_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController _searchController = TextEditingController();

  String selectedCategory = 'What\'s New';
  bool isLoggedIn = false;
  String userType = ''; // 'jobseeker' or 'salonowner'

  // Helper method to safely check platform
  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isAndroid => !kIsWeb && Platform.isAndroid;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();

    // Set status bar to match iOS style when on iOS
    if (!kIsWeb && isIOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
          statusBarBrightness:
              Brightness.light, // iOS: controls status bar text color
        ),
      );
    }
  }

  // Method to check login status and user type
  void _checkLoginStatus() async {
    // First check shared preferences for login status
    try {
      final prefs = await SharedPreferences.getInstance();
      final bool? isLoggedInPref = prefs.getBool('is_logged_in');
      final String? storedUserType = prefs.getString('user_type');

      if (isLoggedInPref == true && storedUserType != null) {
        setState(() {
          isLoggedIn = true;
          userType = storedUserType.toLowerCase();
        });
        print('User type from prefs: $userType');
        return; // Exit early if we found login status in prefs
      }
    } catch (e) {
      print('Error checking login status: $e');
    }

    // Fallback to checking parent widgets
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = this.context;
      bool isInJobseeker = false;
      bool isInSalonowner = false;

      // Walk up the widget tree to find parent widgets
      context.visitAncestorElements((element) {
        final widgetStr = element.widget.toString();
        if (widgetStr.contains('Jobseeker')) {
          isInJobseeker = true;
          return false; // Stop traversing
        }
        if (widgetStr.contains('Salonowner')) {
          isInSalonowner = true;
          return false; // Stop traversing
        }
        return true; // Continue traversing
      });

      setState(() {
        isLoggedIn = isInJobseeker || isInSalonowner;
        if (isInJobseeker) userType = 'jobseeker';
        if (isInSalonowner) userType = 'salonowner';
        if (!isInJobseeker && !isInSalonowner) userType = '';
        print('User type from widget tree: $userType');
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.05;

    // Platform-adaptive behavior with web fallback
    if (kIsWeb) {
      return _buildAndroidLayout(size, padding); // Use Android layout for web
    } else {
      return isIOS
          ? _buildIOSLayout(size, padding)
          : _buildAndroidLayout(size, padding);
    }
  }

  // iOS-specific layout implementation
  Widget _buildIOSLayout(Size size, double padding) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.systemBackground.withOpacity(0.8),
        border: null,
        middle: Text(
          'Mountvoq',
          style: TextStyle(
            fontFamily: '.SF Pro Display',
            fontSize: 18,
            fontWeight: FontWeight.w600,
            letterSpacing: -0.5,
            color: CupertinoColors.label,
          ),
        ),
        // Only show sign out button for guest users (when not in jobseeker or salonowner)
        trailing: userType.isEmpty
            ? CupertinoButton(
                padding: EdgeInsets.zero,
                child: Icon(
                  CupertinoIcons.square_arrow_right,
                  color: CupertinoColors.systemBlue,
                ),
                onPressed: () => _handleSignOut(context),
              )
            : null,
      ),
      child: Column(
        children: [
          // Grid with better spacing and consistent sizing
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(padding),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.1, // Better proportions for cards
                children: [
                  _buildIOSGlassyCard(
                    'Training Hub',
                    CupertinoIcons.play_circle_fill,
                    [CupertinoColors.systemOrange, CupertinoColors.systemBlue],
                    () => _navigateWithIOSTransition('training'),
                  ),
                  _buildIOSGlassyCard(
                    'Consultation',
                    CupertinoIcons.chat_bubble_2_fill,
                    [CupertinoColors.systemBlue, CupertinoColors.activeBlue],
                    () => _navigateWithIOSTransition('consultation'),
                  ),
                  _buildIOSGlassyCard(
                    'Products',
                    CupertinoIcons.bag_fill,
                    [CupertinoColors.systemPink, CupertinoColors.systemRed],
                    () => _navigateWithIOSTransition('products'),
                  ),
                  _buildIOSGlassyCard(
                    'What\'s Trending',
                    CupertinoIcons.chart_bar_alt_fill,
                    [CupertinoColors.systemBlue, CupertinoColors.systemYellow],
                    () => _navigateWithIOSTransition('trending'),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // iOS-style glassy card with frosted effect
  Widget _buildIOSGlassyCard(
    String title,
    IconData icon,
    List<Color> colors,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16), // iOS-standard corner radius
        child: BackdropFilter(
          // Frosted glass effect
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              // Translucent background with subtle gradient
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  CupertinoColors.systemBackground.withOpacity(0.7),
                  CupertinoColors.systemBackground.withOpacity(0.5),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              // iOS-style very subtle border
              border: Border.all(
                color: CupertinoColors.systemGrey5.withOpacity(0.5),
                width: 0.5,
              ),
              // iOS-style subtle shadow
              boxShadow: [
                BoxShadow(
                  color: CupertinoColors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon container with gradient
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: colors,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(
                      14,
                    ), // iOS-standard corner radius
                    // iOS-style subtle shadow
                    boxShadow: [
                      BoxShadow(
                        color: colors[0].withOpacity(0.3),
                        blurRadius: 8,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Icon(icon, color: CupertinoColors.white, size: 30),
                ),
                SizedBox(height: 12), // iOS 8pt + 4pt spacing
                // Title with iOS system font
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: '.SF Pro Text', // iOS system font
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: CupertinoColors.label,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToScreen(String screenType) {
    switch (screenType) {
      case 'salon':
        // Navigate to salon management screen
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('My Salon feature coming soon!')),
        );
        break;
      case 'professionals':
        // Navigate to professionals directory
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Professionals directory coming soon!')),
        );
        break;
      case 'training':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => TrainingScreen()),
        );
        break;
      case 'consultation':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ConsultationScreen()),
        );
        break;
      case 'products':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => ProductsScreen()),
        );
        break;
      case 'trending':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => WhatsNewScreen()),
        );
        break;
      case 'analytics':
        // Navigate to analytics screen
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Business Analytics coming soon!')),
        );
        break;
    }
  }

  // iOS-style navigation with proper transitions
  void _navigateWithIOSTransition(String screenType) {
    Widget destinationScreen;
    String title;

    switch (screenType) {
      case 'training':
        destinationScreen = TrainingScreen();
        title = 'Training Hub';
        break;
      case 'consultation':
        destinationScreen = ConsultationScreen();
        title = 'Consultation';
        break;
      case 'products':
        destinationScreen = ProductsScreen();
        title = 'Products';
        break;
      case 'trending':
        destinationScreen = WhatsNewScreen();
        title = 'What\'s Trending';
        break;
      default:
        // Show iOS-style alert for unavailable features
        showCupertinoDialog(
          context: context,
          builder: (context) => CupertinoAlertDialog(
            title: Text('Coming Soon'),
            content: Text('This feature will be available soon.'),
            actions: [
              CupertinoDialogAction(
                child: Text('OK'),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
        );
        return;
    }

    // Use CupertinoPageRoute for iOS-style horizontal slide transition
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => destinationScreen,
        title: title, // Used for iOS navigation bar title
      ),
    );
  }

  // Original Android layout (keeping for cross-platform compatibility)
  Widget _buildAndroidLayout(Size size, double padding) {
    return SafeArea(
      bottom: false,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            // Full-screen background image
            Positioned.fill(
              child: Image.asset(ImageConstants.wall, fit: BoxFit.cover),
            ),

            // Gradient overlay for better text visibility

            // Main content
            Column(
              children: [
                // Header with back button and sign out button for guest users
                if (userType.isEmpty)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Sign out button
                        ElevatedButton.icon(
                          onPressed: () => _handleSignOut(context),
                          icon: const Icon(Icons.logout, color: Colors.white),
                          label: const Text(
                            'Sign Out',
                            style: TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black.withOpacity(0.7),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                // Logo and grid remain the same
                Padding(
                  padding: const EdgeInsets.only(top: 46.0),
                  child: Container(
                    width: 220,
                    height: 120,
                    child: Image.asset(
                      ImageConstants.splash,
                      width: 220,
                      height: 120,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),

                // Grid of menu items
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(padding),
                    child: GridView.count(
                      childAspectRatio: 0.9,
                      padding: EdgeInsets.all(10),
                      crossAxisCount: 2,
                      crossAxisSpacing: 5,
                      mainAxisSpacing: 10,
                      children: [
                        _buildAndroidMenuCard(
                          'Training Hub',
                          Icons.play_arrow,
                          [Colors.blue, Colors.purple],
                          () => _navigateToScreen('training'),
                        ),
                        _buildAndroidMenuCard(
                          'Consultation',
                          Icons.chat_bubble,
                          [Colors.blue, Colors.pink],
                          () => _navigateToScreen('consultation'),
                        ),
                        _buildAndroidMenuCard(
                          'Products',
                          Icons.shopping_bag,
                          [Colors.blue, Colors.red],
                          () => _navigateToScreen('products'),
                        ),
                        _buildAndroidMenuCard(
                          'What\'s Trending',
                          Icons.trending_up,
                          [Colors.blue, Colors.orange],
                          () => _navigateToScreen('trending'),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAndroidMenuCard(
    String title,
    IconData icon,
    List<Color> colors,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: colors,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: colors[0].withOpacity(0.4),
                  blurRadius: 8,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
          SizedBox(height: 12),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // Handle sign out
  void _handleSignOut(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return isIOS
            ? CupertinoAlertDialog(
                title: Text('Sign Out'),
                content: Text('Are you sure you want to sign out?'),
                actions: [
                  CupertinoDialogAction(
                    child: Text('Cancel'),
                    onPressed: () => Navigator.pop(dialogContext),
                  ),
                  CupertinoDialogAction(
                    isDestructiveAction: true,
                    child: Text('Sign Out'),
                    onPressed: () async {
                      Navigator.pop(dialogContext);

                      // Clear user session data
                      try {
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_logged_in', false);
                        await prefs.remove('user_type');
                        await prefs.remove('token');
                        await prefs.remove('user_id');
                        await prefs.remove('email');
                      } catch (e) {
                        print('Error clearing user data: $e');
                      }

                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const WelcomeScreen(),
                        ),
                      );
                    },
                  ),
                ],
              )
            : AlertDialog(
                title: Text('Sign Out'),
                content: Text('Are you sure you want to sign out?'),
                actions: [
                  TextButton(
                    child: Text('Cancel'),
                    onPressed: () => Navigator.pop(dialogContext),
                  ),
                  TextButton(
                    child: Text(
                      'Sign Out',
                      style: TextStyle(color: Colors.red),
                    ),
                    onPressed: () async {
                      Navigator.pop(dialogContext);

                      // Clear user session data
                      try {
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_logged_in', false);
                        await prefs.remove('user_type');
                        await prefs.remove('token');
                        await prefs.remove('user_id');
                        await prefs.remove('email');
                      } catch (e) {
                        print('Error clearing user data: $e');
                      }

                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const WelcomeScreen(),
                        ),
                      );
                    },
                  ),
                ],
              );
      },
    );
  }
}
