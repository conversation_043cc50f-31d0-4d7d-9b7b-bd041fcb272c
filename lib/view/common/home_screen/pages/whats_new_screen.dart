import 'package:flutter/material.dart';
import 'package:job/services/api_service.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/uttilits/image_const.dart';
import 'package:job/view/common/home_screen/details_page/whats_new_details_screen.dart';

class WhatsNewScreen extends StatefulWidget {
  const WhatsNewScreen({Key? key}) : super(key: key);

  @override
  State<WhatsNewScreen> createState() => _WhatsNewScreenState();
}

class _WhatsNewScreenState extends State<WhatsNewScreen> {
  List<Map<String, dynamic>> whatsNewPosts = [
    {
      'title': 'Latest Hair Styling Trends 2023',
      'category': 'What\'s Trending',
      'image': ImageConstants.image1,
      'date': 'May 15, 2023',
      'content':
          'Discover the hottest hair styling trends of 2023. From sleek bobs to textured layers, we cover everything you need to know to stay ahead in the world of hair fashion.',
    },
    {
      'title': 'New Salon Equipment Technology',
      'category': 'What\'s New',
      'image': ImageConstants.image2,
      'date': 'May 12, 2023',
      'content':
          'Explore the latest innovations in salon equipment technology that are revolutionizing the beauty industry. Learn how these tools can enhance your salon experience.',
    },
    {
      'title': 'Sustainable Beauty Practices',
      'category': 'What\'s New',
      'image': ImageConstants.image3,
      'date': 'May 8, 2023',
      'content':
          'Join the movement towards eco-friendly beauty. Discover sustainable practices that salons are adopting to reduce environmental impact while delivering exceptional service.',
    },
  ];

  bool isLoading = false;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _fetchTrendingPosts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'What\'s Trending',
          style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black87),
      ),
      body: RefreshIndicator(
        onRefresh: _fetchTrendingPosts,
        child: isLoading
            ? Center(child: CircularProgressIndicator())
            : errorMessage.isNotEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Something went wrong',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(errorMessage),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _fetchTrendingPosts,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.black,
                      ),
                      child: Text('Try Again'),
                    ),
                  ],
                ),
              )
            : ListView(
                padding: EdgeInsets.all(16),
                children: [
                  // Featured post
                  if (whatsNewPosts.isNotEmpty)
                    _buildFeaturedPost(whatsNewPosts[0]),
                  SizedBox(height: 24),

                  // Section title
                  Text(
                    'Latest Updates',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 16),

                  // List of posts
                  ...whatsNewPosts
                      .skip(whatsNewPosts.length > 1 ? 1 : 0)
                      .map((post) => _buildPostCard(post))
                      .toList(),
                ],
              ),
      ),
    );
  }

  Widget _buildFeaturedPost(Map<String, dynamic> post) {
    // Get image from API or use local fallback
    String? imageUrl;

    // First check for images from API
    if (post['images'] != null && (post['images'] as List).isNotEmpty) {
      imageUrl = (post['images'] as List).first['image'];
    }
    // Then check for YouTube thumbnail if available
    else if (post['youtube_video_url'] != null &&
        post['youtube_video_url'].toString().isNotEmpty) {
      String videoId = '';
      String url = post['youtube_video_url'];

      // Extract video ID from YouTube URL
      if (url.contains('youtu.be')) {
        videoId = url.split('/').last.split('?').first;
      } else if (url.contains('youtube.com')) {
        videoId = Uri.parse(url).queryParameters['v'] ?? '';
      }

      if (videoId.isNotEmpty) {
        imageUrl = 'https://img.youtube.com/vi/$videoId/0.jpg';
      }
    }
    // Finally use local image if available
    else if (post['image'] != null) {
      imageUrl = post['image'];
    }

    // Format date from API or use the one from local data
    String displayDate = post['created_at'] != null
        ? _formatDate(post['created_at'])
        : post['date'] ?? 'Date not available';

    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WhatsNewDetailsScreen(blogPost: post),
          ),
        );
      },
      child: Container(
        height: 240,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Image
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: imageUrl != null && imageUrl.startsWith('http')
                  ? Image.network(
                      imageUrl,
                      height: 240,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          ImageConstants.image1,
                          height: 240,
                          width: double.infinity,
                          fit: BoxFit.cover,
                        );
                      },
                    )
                  : Image.asset(
                      imageUrl ?? ImageConstants.image1,
                      height: 240,
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
            ),

            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
                ),
              ),
            ),

            // Content
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FEATURED',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      post['title'] ?? 'No Title',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 22,
                      ),
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          displayDate,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.8),
                            fontSize: 14,
                          ),
                        ),
                        SizedBox(width: 16),
                        Icon(
                          Icons.arrow_forward,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Read More',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostCard(Map<String, dynamic> post) {
    // Get image from API or use local fallback
    String? imageUrl;

    // First check for images from API
    if (post['images'] != null && (post['images'] as List).isNotEmpty) {
      imageUrl = (post['images'] as List).first['image'];
    }
    // Then check for YouTube thumbnail if available
    else if (post['youtube_video_url'] != null &&
        post['youtube_video_url'].toString().isNotEmpty) {
      String videoId = '';
      String url = post['youtube_video_url'];

      // Extract video ID from YouTube URL
      if (url.contains('youtu.be')) {
        videoId = url.split('/').last.split('?').first;
      } else if (url.contains('youtube.com')) {
        videoId = Uri.parse(url).queryParameters['v'] ?? '';
      }

      if (videoId.isNotEmpty) {
        imageUrl = 'https://img.youtube.com/vi/$videoId/0.jpg';
      }
    }
    // Finally use local image if available
    else if (post['image'] != null) {
      imageUrl = post['image'];
    }

    // Format date from API or use the one from local data
    String displayDate = post['created_at'] != null
        ? _formatDate(post['created_at'])
        : post['date'] ?? 'Date not available';

    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WhatsNewDetailsScreen(blogPost: post),
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.15),
              spreadRadius: 1,
              blurRadius: 8,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            // Image
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
              child: imageUrl != null && imageUrl.startsWith('http')
                  ? Image.network(
                      imageUrl,
                      width: 120,
                      height: 120,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          ImageConstants.image1,
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                        );
                      },
                    )
                  : Image.asset(
                      imageUrl ?? ImageConstants.image1,
                      width: 120,
                      height: 120,
                      fit: BoxFit.cover,
                    ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayDate,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      post['title'] ?? 'No Title',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Text(
                          'Read More',
                          style: TextStyle(
                            color: ColorConstants.PRIMARYCOLOR,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward,
                          size: 14,
                          color: ColorConstants.PRIMARYCOLOR,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Format date from API
  String _formatDate(String apiDate) {
    try {
      final date = DateTime.parse(apiDate);
      return '${_getMonth(date.month)} ${date.day}, ${date.year}';
    } catch (e) {
      return apiDate;
    }
  }

  // Get month name
  String _getMonth(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month - 1];
  }

  Future<void> _fetchTrendingPosts() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final posts = await ApiService.fetchTrendingPosts();

      setState(() {
        whatsNewPosts = posts;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load trending posts: $e';
        isLoading = false;
      });
    }
  }
}
