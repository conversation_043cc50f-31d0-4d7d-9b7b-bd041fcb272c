import 'package:flutter/material.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/view/common/loginregister/saloonowner/salonowner_register.dart';
import 'package:job/view/common/loginregister/saloonowner/saloonowner_forgot_password.dart';
import 'package:job/view/common/welcome_screen/welcome_screen.dart';
import 'package:job/services/api_service.dart';
import 'package:job/view/salonowner2/shome_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class SaloonownerLogin extends StatefulWidget {
  const SaloonownerLogin({super.key});

  @override
  State<SaloonownerLogin> createState() => _SaloonownerLoginState();
}

class _SaloonownerLoginState extends State<SaloonownerLogin> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscureText = true;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          // Navigate to welcome screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const WelcomeScreen()),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
            onPressed: () {
              // Navigate to welcome screen on back button press
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const WelcomeScreen()),
              );
            },
          ),
        ),
        body: Container(
          height: double.infinity,
          decoration: BoxDecoration(
            color: ColorConstants.WHITE,
            // image: DecorationImage(
            //   image: AssetImage(ImageConstants.WelcomeScreen),
            //   fit: BoxFit.cover,
            //   colorFilter: ColorFilter.mode(
            //     Colors.black.withOpacity(0.5),
            //     BlendMode.darken,
            //   ),
            // ),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: _formKey,
                child: Padding(
                  padding: const EdgeInsets.only(top: 70.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              spreadRadius: 2,
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Welcome Back!',
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: ColorConstants.black,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Text(
                              "Login to your account to explore",
                              style: TextStyle(
                                color: ColorConstants.black,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              "your salon and manage your business",
                              style: TextStyle(
                                color: ColorConstants.black,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 30),
                            TextFormField(
                              controller: _emailController,
                              style: const TextStyle(color: Colors.black),
                              cursorColor: Colors.black,
                              decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.black,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.black,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.red,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                focusedErrorBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.red,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                labelText: 'Email',
                                labelStyle: const TextStyle(
                                  color: Colors.black,
                                ),
                                prefixIcon: const Icon(
                                  Icons.email,
                                  color: Colors.black,
                                ),
                                hintStyle: const TextStyle(
                                  color: Colors.black54,
                                ),
                                errorStyle: const TextStyle(color: Colors.red),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your email';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 20),
                            TextFormField(
                              controller: _passwordController,
                              style: const TextStyle(color: Colors.black),
                              cursorColor: Colors.black,
                              obscureText: _obscureText,
                              decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.black,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.black,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                errorBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.red,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                focusedErrorBorder: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: Colors.red,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                labelText: 'Password',
                                labelStyle: const TextStyle(
                                  color: Colors.black,
                                ),
                                prefixIcon: const Icon(
                                  Icons.lock,
                                  color: Colors.black,
                                ),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscureText
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                    color: Colors.black,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscureText = !_obscureText;
                                    });
                                  },
                                ),
                                hintStyle: const TextStyle(
                                  color: Colors.black54,
                                ),
                                errorStyle: const TextStyle(color: Colors.red),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your password';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 20),
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          const SaloonownerForgotPassword(),
                                    ),
                                  );
                                },
                                child: const Text(
                                  'Forgot Password?',
                                  style: TextStyle(color: ColorConstants.black),
                                ),
                              ),
                            ),
                            const SizedBox(height: 10),
                            SizedBox(
                              width: double.infinity,
                              height: 55,
                              child: ElevatedButton(
                                onPressed: _isLoading
                                    ? null
                                    : () async {
                                        if (_formKey.currentState!.validate()) {
                                          setState(() {
                                            _isLoading = true;
                                          });

                                          try {
                                            final email =
                                                _emailController.text.trim();
                                            final password =
                                                _passwordController.text.trim();

                                            // For demo credentials, use the hardcoded login
                                            if (email == '<EMAIL>' &&
                                                password == 'Password@123') {
                                              // Save demo user data to shared preferences
                                              final prefs =
                                                  await SharedPreferences
                                                      .getInstance();
                                              await prefs.setString(
                                                'token',
                                                'demo_token',
                                              );
                                              await prefs.setString(
                                                'user_type',
                                                'admin',
                                              );
                                              await prefs.setBool(
                                                'is_logged_in',
                                                true,
                                              );
                                              await prefs.setString(
                                                'user_data',
                                                jsonEncode({
                                                  'email': email,
                                                  'user_id': 999,
                                                  'user_type': 'ADMIN',
                                                }),
                                              );

                                              // Navigate to Salon Owner dashboard
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                SnackBar(
                                                  content: Row(
                                                    children: [
                                                      const Icon(
                                                        Icons
                                                            .check_circle_outline,
                                                        color: Colors.white,
                                                      ),
                                                      const SizedBox(width: 12),
                                                      const Expanded(
                                                        child: Text(
                                                          'Logged in as Salon Owner',
                                                          style: TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 14,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  behavior:
                                                      SnackBarBehavior.floating,
                                                  backgroundColor:
                                                      Colors.green.shade700,
                                                  elevation: 6,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                      10,
                                                    ),
                                                  ),
                                                  duration: const Duration(
                                                    seconds: 3,
                                                  ),
                                                  margin: const EdgeInsets.only(
                                                    top: 50,
                                                    left: 10,
                                                    right: 10,
                                                  ),
                                                  action: SnackBarAction(
                                                    label: 'DISMISS',
                                                    textColor: Colors.white,
                                                    onPressed: () {
                                                      ScaffoldMessenger.of(
                                                        context,
                                                      ).hideCurrentSnackBar();
                                                    },
                                                  ),
                                                ),
                                              );

                                              Navigator.pushAndRemoveUntil(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      ShomePage(),
                                                ),
                                                (route) => false,
                                              );
                                            } else {
                                              // Use the API for real login
                                              try {
                                                final response =
                                                    await ApiService.login(
                                                  email: email,
                                                  password: password,
                                                );

                                                // Check if the user type is ADMIN
                                                if (response['user_type'] ==
                                                    'ADMIN') {
                                                  // Save token and user data
                                                  final prefs =
                                                      await SharedPreferences
                                                          .getInstance();
                                                  await prefs.setString(
                                                    'token',
                                                    response['token'],
                                                  );
                                                  await prefs.setString(
                                                    'user_type',
                                                    'admin',
                                                  );
                                                  await prefs.setBool(
                                                    'is_logged_in',
                                                    true,
                                                  );
                                                  await prefs.setString(
                                                    'user_data',
                                                    jsonEncode({
                                                      'email':
                                                          response['email'],
                                                      'user_id':
                                                          response['user_id'],
                                                      'user_type':
                                                          response['user_type'],
                                                    }),
                                                  );

                                                  Navigator.pushAndRemoveUntil(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          ShomePage(),
                                                    ),
                                                    (route) => false,
                                                  );
                                                } else {
                                                  // Show unauthorized user type error
                                                  _showAnimatedErrorSnackBar(
                                                    'This login is only for Salon Owners. Please use the appropriate login page.',
                                                  );
                                                }
                                              } catch (e) {
                                                String errorMessage =
                                                    e.toString().replaceAll(
                                                          'Exception: ',
                                                          '',
                                                        );

                                                // Check for specific error patterns
                                                if (errorMessage.contains(
                                                      'non_field_errors',
                                                    ) ||
                                                    errorMessage.contains(
                                                      'Unable to log in with provided credentials',
                                                    ) ||
                                                    errorMessage.contains(
                                                      'No active account',
                                                    )) {
                                                  _showAnimatedErrorSnackBar(
                                                    'Invalid email or password. Please try again.',
                                                  );
                                                } else if (errorMessage
                                                        .contains(
                                                      'SocketException',
                                                    ) ||
                                                    errorMessage.contains(
                                                      'Network is unreachable',
                                                    )) {
                                                  _showAnimatedErrorSnackBar(
                                                    'Network error. Please check your internet connection.',
                                                    isWarning: true,
                                                  );
                                                } else {
                                                  _showAnimatedErrorSnackBar(
                                                    "Your account is pending approval from Super Admin or your membership has expired. Please contact support for assistance.",
                                                  );
                                                }
                                              }
                                            }
                                          } catch (e) {
                                            // Show generic error
                                            _showAnimatedErrorSnackBar(
                                              'An unexpected error occurred. Please try again.',
                                            );
                                          } finally {
                                            setState(() {
                                              _isLoading = false;
                                            });
                                          }
                                        }
                                      },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ColorConstants.black,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(28),
                                  ),
                                  disabledBackgroundColor: Colors.grey,
                                ),
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2.0,
                                        ),
                                      )
                                    : const Text(
                                        'Login',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: ColorConstants.WHITE,
                                        ),
                                      ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  'Don\'t have an account?',
                                  style: TextStyle(color: ColorConstants.black),
                                ),
                                TextButton(
                                  onPressed: () {
                                    Navigator.pushReplacement(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            const SalonownerRegister(),
                                      ),
                                    );
                                  },
                                  child: const Text(
                                    'Register',
                                    style: TextStyle(
                                      color: ColorConstants.black,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _showAnimatedErrorSnackBar(String message, {bool isWarning = false}) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();

    final snackBar = SnackBar(
      content: Row(
        children: [
          Icon(
            isWarning ? Icons.warning_amber_rounded : Icons.error_outline,
            color: Colors.white,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      behavior: SnackBarBehavior.floating,
      backgroundColor: isWarning ? Colors.orange.shade800 : Colors.red.shade800,
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.only(
        top: 50,
        left: 10,
        right: 10,
      ), // Position at top
      action: SnackBarAction(
        label: 'DISMISS',
        textColor: Colors.white,
        onPressed: () {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        },
      ),
    );

    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}
