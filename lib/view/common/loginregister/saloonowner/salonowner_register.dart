import 'package:flutter/material.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/view/common/loginregister/saloonowner/saloonowner_login.dart';
import 'package:job/services/api_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

// import 'package:job_portal/const/color_const.dart';
// import 'package:job_portal/const/image_const.dart';
// import 'package:job_portal/view/login_screen/login_screen.dart';

class SalonownerRegister extends StatefulWidget {
  const SalonownerRegister({super.key});

  @override
  State<SalonownerRegister> createState() => _SalonownerRegisterState();
}

class _SalonownerRegisterState extends State<SalonownerRegister> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _salonNameController = TextEditingController();
  final _salonAddressController = TextEditingController();
  final _pinCodeController = TextEditingController();
  final _googleMapLinkController = TextEditingController();
  final _salonTypeController = TextEditingController();
  String _selectedSalonType = 'UNISEX'; // Default value
  final List<String> _salonTypeOptions = ['MALE', 'FEMALE', 'UNISEX'];
  bool _obscureText = true;
  bool _obscureConfirmText = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _salonNameController.dispose();
    _salonAddressController.dispose();
    _pinCodeController.dispose();
    _googleMapLinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        if (!didPop) {
          // Navigate to salon owner login screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const SaloonownerLogin()),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
            onPressed: () {
              // Navigate to salon owner login screen on back button press
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const SaloonownerLogin(),
                ),
              );
            },
          ),
          title: const Text(
            'Salon Registration',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
        ),
        body: Container(
          height: double.infinity,
          decoration: BoxDecoration(color: ColorConstants.WHITE),
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            spreadRadius: 2,
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Create a salon owner account to post jobs and hire talent",
                            style: TextStyle(
                              color: ColorConstants.black,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 30),

                          // Owner's Name
                          TextFormField(
                            controller: _nameController,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Owner Full Name',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.person,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your name';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Salon Name
                          TextFormField(
                            controller: _salonNameController,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Salon Name',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.business,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your salon name';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Salon Address
                          TextFormField(
                            controller: _salonAddressController,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Salon Address',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.location_on,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your salon address';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Pin Code
                          TextFormField(
                            controller: _pinCodeController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Pin Code',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.pin_drop,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your pin code';
                              }
                              if (value.length != 6) {
                                return 'Pin code must be 6 digits';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Google Map Link
                          TextFormField(
                            controller: _googleMapLinkController,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Google Map Link',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.map,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your Google Map link';
                              }

                              // Accept various Google Maps link formats
                              final validFormats = [
                                'maps.google.com',
                                'goo.gl/maps',
                                'maps.app.goo.gl',
                                'g.co/kgs',
                                'google.com/maps',
                                'maps.app',
                              ];

                              bool isValidLink = false;
                              for (final format in validFormats) {
                                if (value.contains(format)) {
                                  isValidLink = true;
                                  break;
                                }
                              }

                              if (!isValidLink) {
                                return 'Please enter a valid Google Maps link';
                              }

                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Salon Type
                          DropdownButtonFormField<String>(
                            value: _selectedSalonType,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Salon Type',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.spa,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            items: _salonTypeOptions.map((String type) {
                              String displayText = type == 'MALE'
                                  ? 'Male Salon'
                                  : type == 'FEMALE'
                                      ? 'Female Salon'
                                      : 'Unisex Salon';
                              return DropdownMenuItem<String>(
                                value: type,
                                child: Text(displayText),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _selectedSalonType = newValue;
                                });
                              }
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select your salon type';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Email
                          TextFormField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Email',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.email,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your email';
                              }
                              if (!RegExp(
                                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                              ).hasMatch(value)) {
                                return 'Please enter a valid email';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Phone Number
                          TextFormField(
                            controller: _phoneController,
                            keyboardType: TextInputType.phone,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Phone Number',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.phone,
                                color: ColorConstants.black,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your phone number';
                              }
                              if (!RegExp(
                                r'^\+?[0-9]{10,12}$',
                              ).hasMatch(value)) {
                                return 'Please enter a valid phone number';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Password
                          TextFormField(
                            controller: _passwordController,
                            obscureText: _obscureText,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Password',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.lock,
                                color: ColorConstants.black,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureText
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: ColorConstants.black,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureText = !_obscureText;
                                  });
                                },
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your password';
                              }
                              if (value.length < 6) {
                                return 'Password must be at least 6 characters';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20),

                          // Confirm Password
                          TextFormField(
                            controller: _confirmPasswordController,
                            obscureText: _obscureConfirmText,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ColorConstants.black,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              labelText: 'Confirm Password',
                              labelStyle: const TextStyle(
                                color: ColorConstants.black,
                              ),
                              prefixIcon: const Icon(
                                Icons.lock_outline,
                                color: ColorConstants.black,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscureConfirmText
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: ColorConstants.black,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscureConfirmText = !_obscureConfirmText;
                                  });
                                },
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please confirm your password';
                              }
                              if (value != _passwordController.text) {
                                return 'Passwords do not match';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 30),

                          // Register Button
                          SizedBox(
                            width: double.infinity,
                            height: 55,
                            child: ElevatedButton(
                              onPressed: _isLoading
                                  ? null
                                  : () async {
                                      if (_formKey.currentState!.validate()) {
                                        setState(() {
                                          _isLoading = true;
                                        });

                                        try {
                                          // Get selected plan
                                          // final selectedPlan =
                                          //     _subscriptionPlans.firstWhere(
                                          //   (plan) =>
                                          //       plan['isSelected'] == true,
                                          // );

                                          // Call API service
                                          final response = await ApiService
                                              .registerSalonOwner(
                                            email: _emailController.text.trim(),
                                            password: _passwordController.text,
                                            ownerName:
                                                _nameController.text.trim(),
                                            salonName: _salonNameController.text
                                                .trim(),
                                            salonAddress:
                                                _salonAddressController.text
                                                    .trim(),
                                            pinCode:
                                                _pinCodeController.text.trim(),
                                            googleMapLink:
                                                _googleMapLinkController.text
                                                    .trim(),
                                            phoneNumber:
                                                _phoneController.text.trim(),
                                            salonType: _selectedSalonType,
                                          );

                                          // Save token and user data to shared preferences
                                          final prefs = await SharedPreferences
                                              .getInstance();
                                          await prefs.setString(
                                            'token',
                                            response['token'],
                                          );
                                          await prefs.setString(
                                            'user_data',
                                            jsonEncode(response['user']),
                                          );
                                          await prefs.setString(
                                            'user_type',
                                            'ADMIN',
                                          );

                                          // Show success message
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Row(
                                                children: [
                                                  const Icon(
                                                    Icons.check_circle_outline,
                                                    color: Colors.white,
                                                  ),
                                                  const SizedBox(width: 12),
                                                  Expanded(
                                                    child: Text(
                                                      'Registration successful! You have subscribed .',
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              backgroundColor:
                                                  Colors.green.shade700,
                                              elevation: 6,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              duration: const Duration(
                                                seconds: 3,
                                              ),
                                            ),
                                          );

                                          // Show registration success dialog
                                          _showRegistrationSuccessDialog(
                                            context,
                                          );
                                        } catch (e) {
                                          // Show error message
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Row(
                                                children: [
                                                  const Icon(
                                                    Icons.error_outline,
                                                    color: Colors.white,
                                                  ),
                                                  const SizedBox(width: 12),
                                                  Expanded(
                                                    child: Text(
                                                      'Registration failed: ${e.toString().replaceAll('Exception: ', '')}',
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              behavior:
                                                  SnackBarBehavior.floating,
                                              backgroundColor:
                                                  Colors.red.shade800,
                                              elevation: 6,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              duration: const Duration(
                                                seconds: 4,
                                              ),
                                              action: SnackBarAction(
                                                label: 'DISMISS',
                                                textColor: Colors.white,
                                                onPressed: () {
                                                  ScaffoldMessenger.of(
                                                    context,
                                                  ).hideCurrentSnackBar();
                                                },
                                              ),
                                            ),
                                          );
                                          setState(() {
                                            _isLoading = false;
                                          });
                                        }
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorConstants.black,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(28),
                                  side: BorderSide(color: ColorConstants.WHITE),
                                ),
                                disabledBackgroundColor: Colors.grey,
                              ),
                              child: _isLoading
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2.0,
                                      ),
                                    )
                                  : const Text(
                                      'Register & Subscribe',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: ColorConstants.WHITE,
                                      ),
                                    ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                'Already have an account?',
                                style: TextStyle(color: ColorConstants.black),
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          const SaloonownerLogin(),
                                    ),
                                  );
                                },
                                child: const Text(
                                  'Login',
                                  style: TextStyle(
                                    color: ColorConstants.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

void _showRegistrationSuccessDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 40,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Registration Successful!',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info, color: Colors.blue),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Your account has been created successfully. Please login to continue.',
                      style: TextStyle(fontSize: 15),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Your account requires approval from a Super Admin before you can access all features.',
              style: TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to login screen
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder: (context) => const SaloonownerLogin(),
                ),
                (route) => false,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: const Text(
              'Proceed to Login',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ],
      );
    },
  );
}
