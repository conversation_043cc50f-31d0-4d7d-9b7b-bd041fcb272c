// main.dart
import 'package:flutter/material.dart';
import 'package:job/uttilits/image_const.dart';
import 'package:job/view/common/welcome_screen/welcome_screen.dart';
import 'package:job/view/jobseeker/jhome_page.dart';

import 'package:job/view/salonowner2/shome_page.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
  }

  // Check if user is already logged in
  void _checkLoginStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bool isLoggedIn = prefs.getBool('is_logged_in') ?? false;
      final String userType = prefs.getString('user_type') ?? '';

      // Delay for splash screen visibility
      await Future.delayed(const Duration(seconds: 2));

      if (isLoggedIn && userType.isNotEmpty) {
        // Navigate based on user type
        if (userType.toLowerCase() == 'jobseeker') {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const JhomePage()),
          );
        } else if (userType.toLowerCase() == 'admin' ||
            userType.toLowerCase() == 'salonowner') {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const ShomePage()),
          );
        } else {
          // Fallback to welcome screen if user type is unknown
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const WelcomeScreen()),
          );
        }
      } else {
        // Not logged in, go to welcome screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const WelcomeScreen()),
        );
      }
    } catch (e) {
      print('Error checking login status: $e');
      // On error, go to welcome screen
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const WelcomeScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Image.asset(
          ImageConstants.splash,
          width: 200,
          height: 200,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
