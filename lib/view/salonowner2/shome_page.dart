import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:google_fonts/google_fonts.dart';
import 'dart:io' show Platform;
import 'package:job/uttilits/image_const.dart';
import 'package:job/view/common/home_screen/pages/consultation_screen.dart';
import 'package:job/view/common/home_screen/pages/products_screen.dart';
import 'package:job/view/common/home_screen/pages/training_screen.dart';
import 'package:job/view/common/home_screen/pages/whats_new_screen.dart';
import 'package:job/view/salonowner2/screens/salon_details_screen.dart';
import 'package:job/view/salonowner2/screens/employer_page.dart';
import 'package:job/view/salonowner2/screens/help_center_screen.dart';
import 'package:job/view/salonowner2/screens/business_income_screen.dart';
import 'package:job/view/salonowner2/screens/salon_expenses_screen.dart';
import 'package:job/view/salonowner2/screens/find_prefessionals.dart';
import 'package:job/view/salonowner2/screens/ourvacancy_page.dart';
import 'package:job/view/salonowner2/screens/recruitment_hub.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';
import 'package:job/services/api_service.dart'; // Added import for ApiService

// Class to store menu item data
class MenuItemData {
  final String title;
  final IconData icon;
  final List<Color> colors;
  final String screenType;

  MenuItemData({
    required this.title,
    required this.icon,
    required this.colors,
    required this.screenType,
  });
}

class ShomePage extends StatefulWidget {
  const ShomePage({super.key});

  @override
  State<ShomePage> createState() => _ShomePageState();
}

class _ShomePageState extends State<ShomePage> {
  final TextEditingController _searchController = TextEditingController();

  // List to store menu items that can be reordered
  List<MenuItemData> menuItems = [];

  String selectedCategory = 'What\'s New';
  bool isLoggedIn = false;
  String userType = ''; // 'jobseeker' or 'salonowner'
  String? _profilePictureUrl; // Add this for profile picture

  // Helper method to safely check platform
  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isAndroid => !kIsWeb && Platform.isAndroid;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();
    _initializeMenuItems();
    _loadProfilePicture(); // Add this line

    // Set status bar to match iOS style when on iOS
    if (!kIsWeb && isIOS) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
          statusBarBrightness:
              Brightness.light, // iOS: controls status bar text color
        ),
      );
    }
  }

  // Add this method to load profile picture
  Future<void> _loadProfilePicture() async {
    try {
      final profileData = await ApiService.fetchSalonProfile();
      setState(() {
        _profilePictureUrl = profileData['profile_picture'];
      });
    } catch (e) {
      print('Error loading profile picture: $e');
    }
  }

  void _initializeMenuItems() {
    menuItems = [
      MenuItemData(
        title: 'Track My Business',
        icon: Icons.business_center_outlined,
        colors: [Color(0xff3171C9), Color(0xff3171C9)],
        screenType: 'income',
      ),
      MenuItemData(
        title: 'Salon Crew',
        icon: Icons.content_cut,
        colors: [Color(0xff2BA85C), Color(0xff2BA85C)],
        screenType: 'employers',
      ),
      MenuItemData(
        title: 'Post Job Vacancies',
        icon: Icons.work_outline_rounded,
        colors: [Color(0xffF03D88), Color(0xffF03D88)],
        screenType: 'our_vacancies',
      ),

      MenuItemData(
        title: 'Recruitment Hub',
        icon: Icons.receipt_rounded,
        colors: [Color(0xff7AC03C), Color(0xff3171C9)],
        screenType: 'recruitment_hub',
      ),
      MenuItemData(
        title: 'Search Professionals',
        icon: Icons.find_in_page,
        colors: [Color(0xffF6AC32), Color(0xffC42F62)],
        screenType: 'find_professionals',
      ),
      MenuItemData(
        title: 'Salon Expenses',
        icon: Icons.attach_money_rounded,
        colors: [Color(0xffFA4A06), Color(0xff3171C9)],
        screenType: 'expenses',
      ),

      MenuItemData(
        title: 'Training Hub',
        icon: Icons.play_arrow,
        colors: [Color(0xffF4B102), Color(0xffF4B102)],
        screenType: 'training',
      ),
      MenuItemData(
        title: 'Consultation',
        icon: Icons.chat_bubble,
        colors: [Colors.pink, Color(0xff3171C9)],
        screenType: 'consultation',
      ),
      MenuItemData(
        title: 'Products',
        icon: Icons.shopping_bag,
        colors: [Color(0xffE65B00), Color(0xffE65B00)],
        screenType: 'products',
      ),
      MenuItemData(
        title: 'What\'s Trending',
        icon: Icons.trending_up,
        colors: [Color(0xff3171C9), Color(0xff5E469B)],
        screenType: 'trending',
      ),

      // MenuItemData(
      //   title: 'Training Hub',
      //   icon: Icons.play_arrow,
      //   colors: [Color(0xffF4B102), Color(0xffF4B102)],
      //   screenType: 'training',
      // ),
      // MenuItemData(
      //   title: 'Training Hub',
      //   icon: Icons.play_arrow,
      //   colors: [Color(0xffF03D88), Color(0xffF03D88)],
      //   screenType: 'training',
      // ),
      // MenuItemData(
      //   title: 'Training Hub',
      //   icon: Icons.play_arrow,
      //   colors: [Color(0xff4123BF), Color(0xff4123BF)],
      //   screenType: 'training',
      // ),
      MenuItemData(
        title: 'Help Center',
        icon: Icons.help,
        colors: [Colors.purple, Colors.red],
        screenType: 'help_center',
      ),
    ];
  }

  // Method to check login status and user type
  void _checkLoginStatus() async {
    // First check shared preferences for login status
    try {
      final prefs = await SharedPreferences.getInstance();
      final bool? isLoggedInPref = prefs.getBool('is_logged_in');
      final String? storedUserType = prefs.getString('user_type');

      if (isLoggedInPref == true && storedUserType != null) {
        setState(() {
          isLoggedIn = true;
          userType = storedUserType.toLowerCase();
        });
        print('User type from prefs: $userType');
        return; // Exit early if we found login status in prefs
      }
    } catch (e) {
      print('Error checking login status: $e');
    }

    // Fallback to checking parent widgets
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = this.context;
      bool isInJobseeker = false;
      bool isInSalonowner = false;

      // Walk up the widget tree to find parent widgets
      context.visitAncestorElements((element) {
        final widgetStr = element.widget.toString();
        if (widgetStr.contains('Jobseeker')) {
          isInJobseeker = true;
          return false; // Stop traversing
        }
        if (widgetStr.contains('Salonowner')) {
          isInSalonowner = true;
          return false; // Stop traversing
        }
        return true; // Continue traversing
      });

      setState(() {
        isLoggedIn = isInJobseeker || isInSalonowner;
        if (isInJobseeker) userType = 'jobseeker';
        if (isInSalonowner) userType = 'salonowner';
        if (!isInJobseeker && !isInSalonowner) userType = '';
        print('User type from widget tree: $userType');
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final size = MediaQuery.of(context).size;
        final padding = size.width * 0.04;

        // Platform-adaptive behavior with web fallback
        if (kIsWeb) {
          return _buildAndroidLayout(
            size,
            padding,
          ); // Use Android layout for web
        } else {
          return _buildAndroidLayout(size, padding);
        }
      },
    );
  }

  // Original Android layout (keeping for cross-platform compatibility)
  Widget _buildAndroidLayout(Size size, double padding) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Color(0xFF333333),
        centerTitle: true,
        actionsPadding: EdgeInsets.only(right: 16),
        toolbarHeight: 80,
        title: Container(
          width: 256,
          height: 90,
          child: Image.asset(
            ImageConstants.splash,
            width: 286,
            height: 150,
            fit: BoxFit.contain,
          ),
        ),
        actions: [
          InkWell(
            onTap: () {
              // Navigate to profile page
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const salonprofile_page(),
                ),
              );
            },
            child: CircleAvatar(
              radius: 20,
              backgroundImage: _profilePictureUrl != null
                  ? NetworkImage(_profilePictureUrl!)
                  : AssetImage(ImageConstants.image3) as ImageProvider,
              onBackgroundImageError: (exception, stackTrace) {
                print('Error loading profile picture: $exception');
              },
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // Full-screen background image
          Positioned.fill(
            child: Image.asset(ImageConstants.wall, fit: BoxFit.cover),
          ),

          // Gradient overlay for better text visibility
          Positioned.fill(child: Container(decoration: BoxDecoration())),

          // Main content
          Column(
            children: [
              // Grid of menu items
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(padding),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 66),
                    child: Theme(
                      data: Theme.of(context).copyWith(
                        // Make the drag feedback background transparent
                        canvasColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        scaffoldBackgroundColor: Colors.transparent,
                      ),
                      child: ReorderableGridView.count(
                        crossAxisCount: 3,
                        crossAxisSpacing: 14,
                        mainAxisSpacing: 10,
                        childAspectRatio: 0.8,
                        dragWidgetBuilder: (index, child) {
                          // Custom drag widget with transparent background
                          final item = menuItems[index];
                          return Material(
                            color: Colors.transparent,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: item.colors,
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    borderRadius: BorderRadius.circular(14),
                                  ),
                                  child: Icon(
                                    item.icon,
                                    color: Colors.white,
                                    size: 30,
                                  ),
                                ),
                                SizedBox(height: 12),
                                Text(
                                  item.title,
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                        children: List.generate(menuItems.length, (index) {
                          final item = menuItems[index];
                          return _buildDraggableMenuCard(
                            key: ValueKey(index),
                            title: item.title,
                            icon: item.icon,
                            colors: item.colors,
                            onTap: () => _handleMenuItemTap(item.screenType),
                          );
                        }),
                        onReorder: (oldIndex, newIndex) {
                          setState(() {
                            if (oldIndex < newIndex) {
                              newIndex -= 1;
                            }
                            final item = menuItems.removeAt(oldIndex);
                            menuItems.insert(newIndex, item);
                          });
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDraggableMenuCard({
    required Key key,
    required String title,
    required IconData icon,
    required List<Color> colors,
    required VoidCallback onTap,
  }) {
    return InkWell(
      key: key,
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: colors,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(14),
            ),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
          SizedBox(height: 12),
          Text(
            title,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuItemTap(String screenType) {
    switch (screenType) {
      case 'training':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const TrainingScreen()),
        );
        break;
      case 'consultation':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ConsultationScreen()),
        );
        break;
      case 'products':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ProductsScreen()),
        );
        break;
      case 'trending':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const WhatsNewScreen()),
        );
        break;
      case 'income':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const BusinessIncomeScreen()),
        );
        break;
      case 'expenses':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SalonExpensesScreen()),
        );

        break;
      case 'employers':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EmployerPage()),
        );
        break;
      case 'our_vacancies':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const OurvacancyPage()),
        );
        break;
      case 'recruitment_hub':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const RecruitmentHub()),
        );
        break;
      case 'find_professionals':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const FindPrefessionals()),
        );
        break;
      case 'help_center':
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const HelpCenterScreen()),
        );
        break;

      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('$screenType feature coming soon!')),
        );
    }
  }

  // Handle sign out
}
