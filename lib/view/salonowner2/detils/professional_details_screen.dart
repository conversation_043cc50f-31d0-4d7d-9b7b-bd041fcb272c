import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io' show Platform;
import 'package:android_intent_plus/android_intent.dart' as android_intent;
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:job/uttilits/api_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProfessionalDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> professional;

  const ProfessionalDetailsScreen({super.key, required this.professional});

  @override
  Widget build(BuildContext context) {
    final String profileImageUrl = professional['profile_picture'] != null
        ? professional['profile_picture']
        : 'https://img.icons8.com/ios-filled/50/user.png';

    final List<dynamic> workPictures = professional['work_pictures_data'] ?? [];

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      body: CustomScrollView(
        slivers: [
          // Minimal header with profile
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            elevation: 0,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.transparent,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new_outlined,
                  color: Colors.black87),
              onPressed: () => Navigator.pop(context),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                color: Colors.white,
                child: SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),
                      // Profile image with subtle shadow
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.06),
                              spreadRadius: 0,
                              blurRadius: 20,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 45,
                          backgroundColor: Colors.grey.shade200,
                          backgroundImage: NetworkImage(profileImageUrl),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Name with clean typography
                      Text(
                        professional['name'] ?? 'Professional',
                        style: GoogleFonts.poppins(
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                          letterSpacing: -0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Profession with muted color
                      Text(
                        professional['expertise_areas_data'] != null &&
                                professional['expertise_areas_data'].isNotEmpty
                            ? professional['expertise_areas_data'][0]['name'] ??
                                ''
                            : '',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Content with natural spacing
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Contact information
                  _buildSection(
                    title: 'Contact',
                    child: Column(
                      children: [
                        if (professional['contact_no'] != null)
                          _buildContactItem(
                            icon: Icons.phone_outlined,
                            label: professional['contact_no'],
                            onTap: () =>
                                _makePhoneCall(professional['contact_no']),
                          ),
                        if (professional['email'] != null)
                          _buildContactItem(
                            icon: Icons.email_outlined,
                            label: professional['email'],
                          ),
                        if (professional['place'] != null)
                          _buildContactItem(
                            icon: Icons.location_on_outlined,
                            label: professional['place'],
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Professional details
                  _buildSection(
                    title: 'Experience',
                    child: Column(
                      children: [
                        if (professional['years_of_experience'] != null)
                          _buildDetailItem(
                            'Years of Experience',
                            '${professional['years_of_experience']} years',
                          ),
                        if (professional['current_salary'] != null)
                          _buildDetailItem(
                            'Current Salary',
                            '₹${professional['current_salary']}',
                          ),
                        if (professional['work_status'] != null)
                          _buildDetailItem(
                            'Work Status',
                            professional['work_status'],
                          ),
                      ],
                    ),
                  ),

                  // About section
                  if (professional['about'] != null &&
                      professional['about'].toString().isNotEmpty) ...[
                    const SizedBox(height: 32),
                    _buildSection(
                      title: 'About',
                      child: Text(
                        professional['about'],
                        style: TextStyle(
                          fontSize: 16,
                          height: 1.6,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],

                  // Resume section
                  if (professional['resume'] != null) ...[
                    const SizedBox(height: 32),
                    _buildSection(
                      title: 'Resume',
                      child: _buildResumeCard(context),
                    ),
                  ],

                  // Work gallery
                  if (workPictures.isNotEmpty) ...[
                    const SizedBox(height: 18),
                    _buildSection(
                      title: 'Work Gallery',
                      child: _buildWorkGallery(workPictures),
                    ),
                  ],

                  const SizedBox(height: 10), // Space for bottom bar
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(context),
    );
  }

  Widget _buildSection({required String title, required Widget child}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 16,
                    color: onTap != null
                        ? Colors.blue.shade700
                        : Colors.grey.shade800,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResumeCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: InkWell(
        onTap: () => _downloadResume(context, professional['resume']),
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.description_outlined,
                color: Colors.grey.shade700,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Resume.pdf',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    'Tap to download',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.download_outlined,
              color: Colors.grey.shade600,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkGallery(List<dynamic> workPictures) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: workPictures.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () => _showFullScreenImage(
            context,
            workPictures[index]['image'],
            index,
            workPictures,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              color: Colors.grey.shade100,
              child: Image.network(
                workPictures[index]['image'],
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey.shade50,
                    child: Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.grey.shade400,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () =>
                    _makePhoneCall(professional['contact_no'] ?? ''),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.phone_outlined,
                        size: 18, color: Colors.grey.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'Call',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: () => _showInvitationDialog(context),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.black87,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.send_outlined, size: 18),
                    const SizedBox(width: 8),
                    const Text(
                      'Invite',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Keep all the existing methods for functionality
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);

    if (Platform.isAndroid) {
      final intent = android_intent.AndroidIntent(
        action: 'android.intent.action.DIAL',
        data: launchUri.toString(),
      );
      await intent.launch();
    } else {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      } else {
        throw 'Could not launch $launchUri';
      }
    }
  }

  void _showInvitationDialog(BuildContext context) {
    final TextEditingController messageController = TextEditingController(
      text:
          "We would like to invite you for an interview at our salon. Please contact us to schedule a convenient time.",
    );

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            bool isLoading = false;

            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: const Text(
                'Send Invitation',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Send invitation to ${professional['name'] ?? 'Professional'}',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: messageController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      labelText: 'Message',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.black87),
                      ),
                      hintText: 'Enter your invitation message',
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      // ignore: dead_code
                      ? null
                      : () async {
                          setState(() {
                            isLoading = true;
                          });

                          try {
                            await _sendInvitation(
                              context,
                              professional['id'],
                              messageController.text,
                            );

                            if (context.mounted) {
                              Navigator.pop(context);
                              _showSuccessDialog(context);
                            }
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content:
                                      Text('Failed to send invitation: $e'),
                                  backgroundColor: Colors.red.shade600,
                                ),
                              );
                            }
                          } finally {
                            setState(() {
                              isLoading = false;
                            });
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black87,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text('Send Invitation'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _sendInvitation(
    BuildContext context,
    int jobseekerId,
    String message,
  ) async {
    final url = Uri.parse('${ApiConstants.baseUrl}/salon-invitations/create/');
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Token $token',
      },
      body: jsonEncode({'jobseeker_id': jobseekerId, 'message': message}),
    );

    if (response.statusCode == 400 && response.body.contains('already')) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text(
              'You have already sent an invitation to this jobseeker'),
          backgroundColor: Colors.red.shade600,
        ),
      );
      return;
    }

    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception('Failed to send invitation: ${response.body}');
    }
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 24),
              SizedBox(width: 12),
              Text('Invitation Sent'),
            ],
          ),
          content: Text(
            'Your invitation has been sent to ${professional['name'] ?? 'the professional'}. They will be notified and can respond to your invitation.',
            style: const TextStyle(fontSize: 16, height: 1.5),
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black87,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _downloadResume(BuildContext context, String resumeUrl) async {
    try {
      final uri = Uri.parse(resumeUrl);

      if (Platform.isAndroid) {
        try {
          final intent = android_intent.AndroidIntent(
            action: 'android.intent.action.VIEW',
            data: uri.toString(),
            type: 'application/pdf',
          );
          await intent.launch();
          return;
        } catch (e) {
          print('Android intent failed: $e');
        }
      }

      if (await canLaunchUrl(uri)) {
        final launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        if (!launched) {
          throw 'Could not launch $resumeUrl';
        }
      } else {
        throw 'Could not launch $resumeUrl';
      }
    } catch (e) {
      print('Error downloading resume: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Could not open resume. Please try again later.'),
          backgroundColor: Colors.red.shade600,
        ),
      );
    }
  }

  void _showFullScreenImage(
    BuildContext context,
    String imageUrl,
    int initialIndex,
    List<dynamic> images,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              'Work Sample ${initialIndex + 1}/${images.length}',
              style: const TextStyle(color: Colors.white),
            ),
          ),
          body: PageView.builder(
            itemCount: images.length,
            controller: PageController(initialPage: initialIndex),
            itemBuilder: (context, index) {
              return Center(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: Image.network(
                    images[index]['image'],
                    fit: BoxFit.contain,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
