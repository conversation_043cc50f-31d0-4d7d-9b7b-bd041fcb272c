import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/view/salonowner2/edit/edit_vacancy_screen.dart';

class VacancyDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> vacancy;

  const VacancyDetailsScreen({Key? key, required this.vacancy})
      : super(key: key);

  // Helper function to convert gender code to display text
  String _formatGenderDisplay(String? genderCode) {
    switch (genderCode?.toLowerCase()) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'anyone':
      case '':
      case null:
        return 'Anyone';
      default:
        return genderCode ?? 'Anyone';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Format posted date
    final postedDate = DateTime.parse(
      vacancy['posted_on'] ?? DateTime.now().toString(),
    );
    final formattedDate =
        '${postedDate.day}/${postedDate.month}/${postedDate.year}';

    // Get responsibilities and benefits as lists
    final List<dynamic> responsibilities =
        vacancy['key_responsibilities'] ?? [];
    final List<dynamic> benefits = vacancy['benefits'] ?? [];
    final List<dynamic> languages = vacancy['languages'] ?? [];

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new_outlined, size: 22),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: Text(
          'Vacancy Details',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined, size: 22),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EditVacancyScreen(vacancy: vacancy),
                ),
              );

              if (result != null && result['action'] == 'update') {
                Navigator.pop(context, {
                  'action': 'update',
                  'data': result['data'],
                });
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete_outline, size: 22),
            onPressed: () => _showDeleteConfirmation(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 16, 24, 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Salon Name
                  if (vacancy['salon_name'] != null) ...[
                    Text(
                      vacancy['salon_name'],
                      style: GoogleFonts.inter(
                        fontSize: 15,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],

                  // Job Title
                  Text(
                    vacancy['title'] ?? 'Job Title',
                    style: GoogleFonts.inter(
                      fontSize: 26,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Location and Job Type
                  Row(
                    children: [
                      Icon(Icons.location_on_outlined,
                          size: 18, color: Colors.grey[600]),
                      const SizedBox(width: 6),
                      Text(
                        vacancy['location'] ?? 'Location',
                        style: GoogleFonts.inter(
                          color: Colors.grey[700],
                          fontSize: 15,
                        ),
                      ),
                      const SizedBox(width: 24),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Posted Date
                  Row(
                    children: [
                      Icon(Icons.calendar_today_outlined,
                          size: 16, color: Colors.grey[500]),
                      const SizedBox(width: 6),
                      Text(
                        'Posted on $formattedDate',
                        style: GoogleFonts.inter(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content Sections
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Job Summary
                  _buildSection(
                    'Job Summary',
                    vacancy['job_summary'] ?? 'No summary available',
                  ),
                  const SizedBox(height: 32),

                  // Specialization
                  if (vacancy['specialization_name'] != null) ...[
                    _buildSection(
                      'Specialization',
                      vacancy['specialization_name'],
                    ),
                    const SizedBox(height: 32),
                  ],

                  // Key Responsibilities
                  if (responsibilities.isNotEmpty) ...[
                    _buildListSection('Key Responsibilities', responsibilities),
                    const SizedBox(height: 32),
                  ],

                  // Benefits
                  if (benefits.isNotEmpty) ...[
                    _buildListSection('Benefits', benefits),
                    const SizedBox(height: 32),
                  ],

                  // Job Requirements
                  _buildRequirementsSection(languages),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          content,
          style: GoogleFonts.inter(
            fontSize: 15,
            height: 1.6,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildListSection(String title, List<dynamic> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        ...items.map((item) => _buildListItem(item)),
      ],
    );
  }

  Widget _buildListItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.inter(
                fontSize: 15,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirementsSection(List<dynamic> languages) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Gender
        _buildRequirementItem(
          'Gender',
          _formatGenderDisplay(vacancy['gender']),
        ),

        // Languages
        if (languages.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildRequirementItem(
            'Languages',
            languages.join(', '),
          ),
        ],
      ],
    );
  }

  Widget _buildRequirementItem(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: GoogleFonts.inter(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: GoogleFonts.inter(
              fontSize: 15,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(
          'Delete Vacancy',
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this vacancy? This action cannot be undone.',
          style: GoogleFonts.inter(fontSize: 15),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: Text(
              'Cancel',
              style: GoogleFonts.inter(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(dialogContext);
              final mainContext = context;

              final loadingOverlay = OverlayEntry(
                builder: (context) => Container(
                  color: Colors.black.withOpacity(0.3),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              );

              Overlay.of(mainContext).insert(loadingOverlay);

              try {
                final jobId = vacancy['id'];
                if (jobId == null) {
                  throw Exception('Job ID is missing');
                }

                final int jobIdInt =
                    jobId is int ? jobId : int.parse(jobId.toString());
                await ApiService.deleteJob(jobIdInt);

                loadingOverlay.remove();

                if (mainContext.mounted) {
                  Navigator.pop(
                      mainContext, {'action': 'delete', 'id': jobIdInt});
                }
              } catch (e) {
                loadingOverlay.remove();

                if (mainContext.mounted) {
                  ScaffoldMessenger.of(mainContext).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete vacancy: $e'),
                      backgroundColor: Colors.red[700],
                    ),
                  );
                }
              }
            },
            child: Text(
              'Delete',
              style: GoogleFonts.inter(
                color: Colors.red[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
