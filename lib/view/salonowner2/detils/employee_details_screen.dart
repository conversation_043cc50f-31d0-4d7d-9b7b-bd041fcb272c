import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io' show Platform;
import 'package:android_intent_plus/android_intent.dart' as android_intent;
import 'package:intl/intl.dart';

class EmployeeDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> employee;

  const EmployeeDetailsScreen({super.key, required this.employee});

  @override
  Widget build(BuildContext context) {
    // Get applicant details if available
    final Map<String, dynamic>? applicantDetails =
        employee.containsKey('original_data') &&
            employee['original_data'].containsKey('applicant_details')
        ? employee['original_data']['applicant_details']
        : null;

    // Format experience value to handle decimal years
    String formattedExperience = '';
    if (employee['years_of_experience'] != null) {
      final experience = employee['years_of_experience'];
      if (experience is num) {
        final years = experience.floor();
        final months = ((experience - years) * 12).round();

        if (years > 0 && months > 0) {
          formattedExperience = '$years years, $months months';
        } else if (years > 0) {
          formattedExperience = '$years years';
        } else if (months > 0) {
          formattedExperience = '$months months';
        } else {
          formattedExperience = 'Less than a month';
        }
      } else {
        formattedExperience = experience.toString();
      }
    } else if (applicantDetails != null &&
        applicantDetails['years_of_experience'] != null) {
      final experience = applicantDetails['years_of_experience'];
      if (experience is num) {
        final years = experience.floor();
        final months = ((experience - years) * 12).round();

        if (years > 0 && months > 0) {
          formattedExperience = '$years years, $months months';
        } else if (years > 0) {
          formattedExperience = '$years years';
        } else if (months > 0) {
          formattedExperience = '$months months';
        } else {
          formattedExperience = 'Less than a month';
        }
      } else {
        formattedExperience = experience.toString();
      }
    }

    // Format start date
    String formattedStartDate = 'Not specified';
    if (employee['start_date'] != null && employee['start_date'].isNotEmpty) {
      try {
        final date = DateTime.parse(employee['start_date']);
        formattedStartDate = DateFormat('MMM d, yyyy').format(date);
      } catch (e) {
        formattedStartDate = employee['start_date'];
      }
    } else if (employee['original_data'] != null &&
        employee['original_data']['applied_on'] != null) {
      try {
        final date = DateTime.parse(employee['original_data']['applied_on']);
        formattedStartDate = DateFormat('MMM d, yyyy').format(date);
      } catch (e) {
        formattedStartDate = 'Not specified';
      }
    }

    // Get profile image
    String profileImage =
        employee['image'] ??
        (applicantDetails != null
            ? applicantDetails['profile_picture']
            : 'https://via.placeholder.com/250');

    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: CustomScrollView(
        slivers: [
          // Custom App Bar with employee image
          SliverAppBar(
            expandedHeight: 250,
            pinned: true,
            backgroundColor: Colors.black,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Background image with overlay
                  Image.network(
                    profileImage,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.person,
                          size: 100,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
                  Container(color: Colors.black.withOpacity(0.5)),

                  // Employee info
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleAvatar(
                            radius: 50,
                            backgroundImage: NetworkImage(profileImage),
                            onBackgroundImageError: (exception, stackTrace) {
                              debugPrint('Error loading image: $exception');
                            },
                          ),
                          const SizedBox(height: 16),
                          Text(
                            employee['name'] ??
                                (applicantDetails != null
                                    ? applicantDetails['name']
                                    : 'Unknown'),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            employee['position'] ??
                                employee['job_title'] ??
                                (applicantDetails != null &&
                                        applicantDetails['expertise_areas'] !=
                                            null &&
                                        applicantDetails['expertise_areas']
                                            .isNotEmpty
                                    ? applicantDetails['expertise_areas'][0]['name']
                                    : 'Unknown Position'),
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Employee details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoCard(
                    title: 'Personal Information',
                    content: Column(
                      children: [
                        _buildDetailRow(
                          icon: Icons.phone,
                          title: 'Phone',
                          value: employee['contact_no'] != null
                              ? '+91 ${employee['contact_no']}'
                              : (applicantDetails != null &&
                                        applicantDetails['contact_no'] != null
                                    ? '+91 ${applicantDetails['contact_no']}'
                                    : 'Not specified'),
                        ),
                        _buildDetailRow(
                          icon: Icons.email,
                          title: 'Email',
                          value:
                              employee['email'] ??
                              (applicantDetails != null
                                  ? applicantDetails['email']
                                  : 'Not specified'),
                        ),
                        _buildDetailRow(
                          icon: Icons.location_on,
                          title: 'Address',
                          value:
                              employee['place'] ??
                              (applicantDetails != null
                                  ? applicantDetails['place']
                                  : 'Not specified'),
                        ),
                        _buildDetailRow(
                          icon: Icons.person,
                          title: 'Gender',
                          value: employee['gender'] == 'M'
                              ? 'Male'
                              : (employee['gender'] == 'F'
                                    ? 'Female'
                                    : (applicantDetails != null &&
                                              applicantDetails['gender'] == 'M'
                                          ? 'Male'
                                          : (applicantDetails != null &&
                                                    applicantDetails['gender'] ==
                                                        'F'
                                                ? 'Female'
                                                : 'Not specified'))),
                        ),
                        if (applicantDetails != null &&
                            applicantDetails['age'] != null)
                          _buildDetailRow(
                            icon: Icons.cake,
                            title: 'Age',
                            value: '${applicantDetails['age']} years',
                          ),
                        if (applicantDetails != null &&
                            applicantDetails['date_of_birth'] != null)
                          _buildDetailRow(
                            icon: Icons.calendar_today,
                            title: 'Date of Birth',
                            value: _formatDate(
                              applicantDetails['date_of_birth'],
                            ),
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  _buildInfoCard(
                    title: 'Employment Details',
                    content: Column(
                      children: [
                        _buildDetailRow(
                          icon: Icons.work,
                          title: 'Position',
                          value:
                              employee['job_title'] ??
                              employee['position'] ??
                              ((applicantDetails != null &&
                                      applicantDetails['expertise_areas'] !=
                                          null &&
                                      applicantDetails['expertise_areas']
                                          .isNotEmpty)
                                  ? applicantDetails['expertise_areas'][0]['name']
                                  : 'Not specified'),
                        ),
                        _buildDetailRow(
                          icon: Icons.date_range,
                          title: 'Joining Date',
                          value: formattedStartDate,
                        ),
                        _buildDetailRow(
                          icon: Icons.attach_money,
                          title: 'Salary',
                          value: employee['current_salary'] != null
                              ? '₹${employee['current_salary']}/month'
                              : 'Not specified',
                        ),
                        _buildDetailRow(
                          icon: Icons.work_history,
                          title: 'Experience',
                          value: formattedExperience.isNotEmpty
                              ? formattedExperience
                              : 'Not specified',
                        ),
                        _buildDetailRow(
                          icon: Icons.business_center,
                          title: 'Work Status',
                          value: _formatWorkStatus(employee['work_status']),
                        ),
                      ],
                    ),
                  ),

                  // Show additional information if available from API
                  if (applicantDetails != null) ...[
                    const SizedBox(height: 16),

                    // About section if available
                    if (applicantDetails['about'] != null &&
                        applicantDetails['about'].isNotEmpty)
                      _buildInfoCard(
                        title: 'About',
                        content: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            applicantDetails['about'],
                            style: const TextStyle(fontSize: 14, height: 1.5),
                          ),
                        ),
                      ),

                    // Social links if available
                    if (applicantDetails['social_link'] != null &&
                        applicantDetails['social_link'].isNotEmpty) ...[
                      const SizedBox(height: 16),
                      _buildInfoCard(
                        title: 'Social Media',
                        content: _buildDetailRow(
                          icon: Icons.link,
                          title: 'Social Profile',
                          value: applicantDetails['social_link'],
                          isLink: true,
                          onTap: () =>
                              _launchUrl(applicantDetails['social_link']),
                        ),
                      ),
                    ],

                    // Resume if available
                    if (applicantDetails['resume'] != null &&
                        applicantDetails['resume'].isNotEmpty) ...[
                      const SizedBox(height: 16),
                      _buildInfoCard(
                        title: 'Resume',
                        content: _buildDetailRow(
                          icon: Icons.description,
                          title: 'View Resume',
                          value: 'Tap to open resume',
                          isLink: true,
                          onTap: () => _launchUrl(applicantDetails['resume']),
                        ),
                      ),
                    ],

                    // Work pictures if available
                    if (applicantDetails['work_pictures'] != null &&
                        applicantDetails['work_pictures'].isNotEmpty) ...[
                      const SizedBox(height: 16),
                      _buildInfoCard(
                        title: 'Portfolio',
                        content: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 200,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount:
                                    applicantDetails['work_pictures'].length,
                                itemBuilder: (context, index) {
                                  final workPicture =
                                      applicantDetails['work_pictures'][index];
                                  return Container(
                                    width: 200,
                                    margin: const EdgeInsets.only(right: 16),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      image: DecorationImage(
                                        image: NetworkImage(
                                          workPicture['image'],
                                        ),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            final contactNo =
                employee['contact_no'] ??
                (applicantDetails != null
                    ? applicantDetails['contact_no']
                    : '');
            _makePhoneCall(contactNo, context);
          },
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
            backgroundColor: Colors.black,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('Call'),
        ),
      ),
    );
  }

  Widget _buildInfoCard({required String title, required Widget content}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String title,
    required String value,
    bool isLink = false,
    VoidCallback? onTap,
  }) {
    final textWidget = Text(
      value,
      style: TextStyle(
        fontSize: 16,
        color: isLink ? Colors.blue : null,
        decoration: isLink ? TextDecoration.underline : null,
      ),
    );

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[700]),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const SizedBox(height: 4),
                isLink && onTap != null
                    ? GestureDetector(onTap: onTap, child: textWidget)
                    : textWidget,
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatWorkStatus(String? status) {
    if (status == null) return 'Not specified';

    switch (status) {
      case 'WORKING':
        return 'Currently Working';
      case 'NOT_WORKING':
        return 'Not Working';
      case 'ON_LEAVE':
        return 'On Leave';
      default:
        return status;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('MMM d, yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  Future<void> _makePhoneCall(String phoneNumber, BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        // Use Android Intent for Android devices
        final intent = android_intent.AndroidIntent(
          action: 'android.intent.action.DIAL',
          data: 'tel:$phoneNumber',
        );
        await intent.launch();
      } else if (Platform.isIOS) {
        // Use URL launcher for iOS devices
        final url = Uri.parse('tel:$phoneNumber');
        if (await canLaunchUrl(url)) {
          await launchUrl(url);
        } else {
          throw 'Could not launch $url';
        }
      } else {
        // For other platforms
        throw 'Platform not supported';
      }
    } catch (e) {
      debugPrint('Error making phone call: $e');
      // Show error to user
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not make call: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
