import 'package:flutter/material.dart';
import 'package:job/services/api_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:job/uttilits/api_constants.dart';

class ApplicationDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> application;

  const ApplicationDetailsScreen({Key? key, required this.application})
      : super(key: key);

  @override
  State<ApplicationDetailsScreen> createState() =>
      _ApplicationDetailsScreenState();
}

class _ApplicationDetailsScreenState extends State<ApplicationDetailsScreen> {
  late String _status;
  late Color _statusColor;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _status = widget.application['status'];
    _statusColor = widget.application['statusColor'];
  }

  // Method to update application status in the API

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'ACCEPTED':
        return Colors.green;
      case 'REJECTED':
        return Colors.red;
      case 'HIRED':
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Extract applicant details for API applications
    final applicantDetails = widget.application['applicant_details'] ?? {};
    final bool isApiApplication = widget.application.containsKey(
      'original_data',
    );

    return Scaffold(
      appBar: AppBar(
        title: Text('Application Details'),
        actions: [
          // Status dropdown
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: _isUpdating
                ? Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.black,
                        strokeWidth: 2,
                      ),
                    ),
                  )
                : DropdownButton<String>(
                    value: _status,
                    icon: const Icon(
                      Icons.arrow_drop_down,
                      color: Colors.black,
                    ),
                    elevation: 16,
                    style: const TextStyle(color: Colors.black),
                    underline: Container(height: 2, color: Colors.white),
                    dropdownColor: Colors.white,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        _showUpdateStatusDialog(context, newValue);
                      }
                    },
                    items: <String>['Pending', 'Accepted', 'Rejected', 'Hired']
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Applicant header
            Container(
              padding: const EdgeInsets.all(20),
              color: Colors.white,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: NetworkImage(widget.application['image']),
                    backgroundColor: Colors.grey[200],
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.application['name'],
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          widget.application['position'],
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: _statusColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: _statusColor.withOpacity(0.5),
                            ),
                          ),
                          child: Text(
                            _status,
                            style: TextStyle(
                              color: _statusColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Applicant details
            Container(
              padding: const EdgeInsets.all(20),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Applicant Details',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // Experience
                  _buildDetailRow(
                    icon: Icons.work,
                    title: 'Experience',
                    value: widget.application['experience'],
                  ),

                  // Contact info (from API if available)
                  _buildDetailRow(
                    icon: Icons.phone,
                    title: 'Contact',
                    value: isApiApplication
                        ? applicantDetails['contact_no'] ?? 'Not provided'
                        : '+91 9876543210', // Fallback for non-API applications
                  ),

                  // Email (from API if available)
                  _buildDetailRow(
                    icon: Icons.email,
                    title: 'Email',
                    value: isApiApplication
                        ? applicantDetails['email'] ?? 'Not provided'
                        : '<EMAIL>', // Fallback for non-API applications
                  ),

                  // Location (from API if available)
                  _buildDetailRow(
                    icon: Icons.location_on,
                    title: 'Location',
                    value: isApiApplication
                        ? applicantDetails['place'] ?? 'Not provided'
                        : 'Mumbai, India', // Fallback for non-API applications
                  ),

                  // Age (from API if available)
                  if (isApiApplication && applicantDetails['age'] != null)
                    _buildDetailRow(
                      icon: Icons.cake,
                      title: 'Age',
                      value: '${applicantDetails['age']} years',
                    ),

                  // Gender (from API if available)
                  if (isApiApplication && applicantDetails['gender'] != null)
                    _buildDetailRow(
                      icon: Icons.person,
                      title: 'Gender',
                      value:
                          applicantDetails['gender'] == 'M' ? 'Male' : 'Female',
                    ),

                  // Social link (from API if available)
                  if (isApiApplication &&
                      applicantDetails['social_link'] != null)
                    _buildDetailRow(
                      icon: Icons.link,
                      title: 'Social Link',
                      value: applicantDetails['social_link'],
                      isLink: true,
                    ),

                  // Resume (from API if available)
                  if (isApiApplication && applicantDetails['resume'] != null)
                    _buildDetailRow(
                      icon: Icons.description,
                      title: 'Resume',
                      value: 'View Resume',
                      isLink: true,
                      onTap: () {
                        // Open resume in browser or PDF viewer
                        launchUrl(Uri.parse(applicantDetails['resume']));
                      },
                    ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // About section (from API if available)
            if (isApiApplication && applicantDetails['about'] != null)
              Container(
                padding: const EdgeInsets.all(20),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'About',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      applicantDetails['about'],
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[800],
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 16),

            // Expertise areas (from API if available)
            if (isApiApplication && applicantDetails['expertise_areas'] != null)
              Container(
                padding: const EdgeInsets.all(20),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Expertise Areas',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: (applicantDetails['expertise_areas'] as List)
                          .map(
                            (expertise) => Chip(
                              label: Text(expertise['name']),
                              backgroundColor: Colors.grey[200],
                            ),
                          )
                          .toList(),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 16),

            // Work pictures (from API if available)
            if (isApiApplication &&
                applicantDetails['work_pictures'] != null &&
                (applicantDetails['work_pictures'] as List).isNotEmpty)
              Container(
                padding: const EdgeInsets.all(20),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Work Portfolio',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount:
                            (applicantDetails['work_pictures'] as List).length,
                        itemBuilder: (context, index) {
                          final workPicture = (applicantDetails['work_pictures']
                              as List)[index];
                          return Container(
                            width: 200,
                            margin: const EdgeInsets.only(right: 16),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              image: DecorationImage(
                                image: NetworkImage(workPicture['image']),
                                fit: BoxFit.cover,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 24),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Call or message the applicant
                  if (isApiApplication &&
                      applicantDetails['contact_no'] != null) {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) => Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ListTile(
                            leading: Icon(Icons.phone),
                            title: Text('Call'),
                            onTap: () {
                              Navigator.pop(context);
                              launchUrl(
                                Uri.parse(
                                  'tel:${applicantDetails['contact_no']}',
                                ),
                              );
                            },
                          ),
                          ListTile(
                            leading: Icon(Icons.message),
                            title: Text('Message'),
                            onTap: () {
                              Navigator.pop(context);
                              launchUrl(
                                Uri.parse(
                                  'sms:${applicantDetails['contact_no']}',
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  } else {
                    // Fallback for non-API applications
                    launchUrl(Uri.parse('tel:+919876543210'));
                  }
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: const BorderSide(color: Colors.black),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Call',
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _status == 'Accepted'
                  ? ElevatedButton(
                      onPressed: () {
                        _showJoiningRequestDialog(context);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Send Joining Request'),
                    )
                  : ElevatedButton(
                      onPressed: () {
                        // Update status dialog
                        _showUpdateStatusDialog(context, _status);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Status'),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String title,
    required String value,
    bool isLink = false,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Colors.grey[700], size: 20),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const SizedBox(height: 4),
                isLink
                    ? GestureDetector(
                        onTap: onTap ??
                            () {
                              if (value.startsWith('http')) {
                                launchUrl(Uri.parse(value));
                              }
                            },
                        child: Text(
                          value,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      )
                    : Text(
                        value,
                        style: TextStyle(fontSize: 16, color: Colors.black87),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Remove the _buildTimelineItem method as it's no longer needed

  // Removed duplicate _getStatusColor method to resolve the conflict.

  void _showUpdateStatusDialog(BuildContext context, String newStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Status'),
        content:
            Text('Are you sure you want to change the status to "$newStatus"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog

              // Show loading indicator
              setState(() {
                _isUpdating = true;
              });

              try {
                // Convert UI status to API status format
                final apiStatus = newStatus.toUpperCase();

                // Get application ID from original data
                final applicationId = widget.application['id'];

                // Call API to update status
                await ApiService.updateApplicationStatus(
                  applicationId: applicationId,
                  status: apiStatus,
                );

                setState(() {
                  _status = newStatus;
                  _statusColor = _getStatusColor(newStatus);
                  _isUpdating = false;
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Application status updated to $newStatus'),
                    backgroundColor: Colors.green,
                  ),
                );

                // Return updated status to previous screen
                Navigator.pop(context, {
                  'status': newStatus,
                  'statusColor': _getStatusColor(newStatus),
                });
              } catch (e) {
                setState(() {
                  _isUpdating = false;
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to update status: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: Text('Confirm'),
          ),
        ],
      ),
    );
  }

  Future<void> _showJoiningRequestDialog(BuildContext context) async {
    final jobTitleController = TextEditingController();
    final salaryController = TextEditingController();
    final messageController = TextEditingController(
      text:
          'We would like to offer you a position at our salon based on your profile.',
    );

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Joining Request'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Send joining request to ${widget.application['name']}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: jobTitleController,
                decoration: const InputDecoration(
                  labelText: 'Job Title',
                  border: OutlineInputBorder(),
                  hintText: 'e.g. Hair Stylist',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: salaryController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Salary (₹)',
                  border: OutlineInputBorder(),
                  hintText: 'e.g. 20000',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: messageController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Message',
                  border: OutlineInputBorder(),
                  hintText: 'Enter a message for the applicant',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (jobTitleController.text.isEmpty ||
                  salaryController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please fill all required fields'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context);
              _sendJoiningRequest(
                jobTitleController.text,
                salaryController.text,
                messageController.text,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: const Text('Send Request'),
          ),
        ],
      ),
    );
  }

  Future<void> _sendJoiningRequest(
    String jobTitle,
    String salary,
    String message,
  ) async {
    if (!widget.application.containsKey('original_data')) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot send joining request for demo applications'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // Get the token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Get jobseeker ID from application data - check multiple possible locations
      int? jobseekerId;

      // Try to get from original_data directly
      if (widget.application['original_data']['jobseeker'] != null) {
        jobseekerId = widget.application['original_data']['jobseeker'];
      }
      // Try to get from applicant_details
      else if (widget.application['applicant_details'] != null &&
          widget.application['applicant_details']['id'] != null) {
        jobseekerId = widget.application['applicant_details']['id'];
      }
      // Try to get from original_data.applicant
      else if (widget.application['original_data']['applicant'] != null) {
        jobseekerId = widget.application['original_data']['applicant'];
      }

      // If still not found, print the structure to help debug
      if (jobseekerId == null) {
        print('Application structure: ${jsonEncode(widget.application)}');
        throw Exception('Jobseeker ID not found in application data');
      }

      final url = Uri.parse('${ApiConstants.baseUrl}/joining-requests/create/');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'jobseeker_id': jobseekerId,
          'job_title': jobTitle,
          'salary': double.parse(salary),
          'message': message,
        }),
      );

      // Close loading indicator
      if (context.mounted) Navigator.pop(context);

      if (response.statusCode == 201) {
        // Success
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Joining request sent successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // Error
        throw Exception('Failed to send joining request: ${response.body}');
      }
    } catch (e) {
      // Close loading indicator if still showing
      if (context.mounted) Navigator.pop(context);

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending joining request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
