import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:job/uttilits/api_constants.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:job/utils/string_extensions.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class ViaRequestDetailsScreen extends StatefulWidget {
  final Map<String, dynamic> request;

  const ViaRequestDetailsScreen({Key? key, required this.request})
      : super(key: key);

  @override
  State<ViaRequestDetailsScreen> createState() =>
      _ViaRequestDetailsScreenState();
}

class _ViaRequestDetailsScreenState extends State<ViaRequestDetailsScreen> {
  bool _isUpdating = false;
  late String _status;
  late Color _statusColor;

  @override
  void initState() {
    super.initState();
    _status = widget.request['status'];
    _statusColor = widget.request['statusColor'];
  }

  @override
  Widget build(BuildContext context) {
    final applicantDetails = widget.request['applicant_details'] ??
        widget.request['jobseeker_details'] ??
        {};
    final formattedDate = widget.request['applied_on'] != null
        ? DateFormat('MMM d, yyyy')
            .format(DateTime.parse(widget.request['applied_on']))
        : widget.request['created_at'] != null
            ? DateFormat('MMM d, yyyy')
                .format(DateTime.parse(widget.request['created_at']))
            : 'Unknown date';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Request Details'),
        actions: [
          // Status dropdown for all requests
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: _isUpdating
                ? Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    ),
                  )
                : DropdownButton<String>(
                    value: _status,
                    icon: const Icon(
                      Icons.arrow_drop_down,
                      color: Colors.black,
                    ),
                    elevation: 16,
                    style: const TextStyle(color: Colors.black),
                    underline: Container(height: 2, color: Colors.transparent),
                    dropdownColor: Colors.white,
                    onChanged: (String? newValue) {
                      if (newValue != null && newValue != _status) {
                        if (newValue == 'Accepted') {
                          _showConfirmationDialog('ACCEPTED');
                        } else if (newValue == 'Rejected') {
                          _showConfirmationDialog('REJECTED');
                        }
                      }
                    },
                    items: <String>['Pending', 'Accepted', 'Rejected']
                        .map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(value),
                      );
                    }).toList(),
                  ),
          ),
        ],
        elevation: 0,
      ),
      body: _isUpdating
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Applicant header
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          radius: 40,
                          backgroundImage:
                              NetworkImage(widget.request['image']),
                          backgroundColor: Colors.grey[200],
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.request['name'],
                                style: GoogleFonts.inter(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.request['position'],
                                style: GoogleFonts.inter(
                                  fontSize: 16,
                                  color: Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: _statusColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                      color: _statusColor.withOpacity(0.5)),
                                ),
                                child: Text(
                                  _status,
                                  style: GoogleFonts.inter(
                                    color: _statusColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Request details
                    _buildDetailSection(
                      title: 'Request Details',
                      details: [
                        DetailItem(label: 'Applied On', value: formattedDate),
                        DetailItem(
                            label: 'Experience',
                            value: widget.request['experience'] ??
                                '${applicantDetails['years_of_experience'] ?? 0} years'),
                        if (widget.request['salary'] != null)
                          DetailItem(
                              label: 'Requested Salary',
                              value: '₹${widget.request['salary']}'),
                        if (applicantDetails['current_salary'] != null)
                          DetailItem(
                              label: 'Current Salary',
                              value: '₹${applicantDetails['current_salary']}'),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Applicant Details
                    _buildDetailSection(
                      title: 'Applicant Details',
                      details: [
                        DetailItem(
                            label: 'Email',
                            value: applicantDetails['email'] ?? 'Not provided'),
                        DetailItem(
                            label: 'Contact',
                            value: applicantDetails['contact_no'] ??
                                'Not provided'),
                        DetailItem(
                            label: 'Gender',
                            value:
                                applicantDetails['gender'] ?? 'Not provided'),
                        DetailItem(
                            label: 'Location',
                            value: applicantDetails['place'] ?? 'Not provided'),
                        DetailItem(
                            label: 'Age',
                            value: applicantDetails['age']?.toString() ??
                                'Not provided'),
                        DetailItem(
                            label: 'Work Status',
                            value: applicantDetails['work_status'] ??
                                'Not provided'),
                        if (applicantDetails['social_link'] != null &&
                            applicantDetails['social_link'].isNotEmpty)
                          DetailItem(
                            label: 'Social Link',
                            value: applicantDetails['social_link'],
                            isLink: true,
                            onTap: () => _launchSocialLink(
                                applicantDetails['social_link']),
                          ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Expertise Areas
                    if (applicantDetails['expertise_areas'] != null &&
                        (applicantDetails['expertise_areas'] as List)
                            .isNotEmpty) ...[
                      Text(
                        'Expertise Areas',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: (applicantDetails['expertise_areas'] as List)
                            .map<Widget>((expertise) => Chip(
                                  label: Text(expertise),
                                  backgroundColor: Colors.blue.withOpacity(0.1),
                                ))
                            .toList(),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // About
                    if (applicantDetails['about'] != null &&
                        applicantDetails['about'].toString().isNotEmpty) ...[
                      Text(
                        'About',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Text(
                          applicantDetails['about'],
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: Colors.grey[800],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Message
                    if (widget.request['message'] != null) ...[
                      Text(
                        'Message',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Text(
                          widget.request['message'],
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: Colors.grey[800],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Work Pictures
                    if (applicantDetails['work_pictures'] != null &&
                        (applicantDetails['work_pictures'] as List)
                            .isNotEmpty) ...[
                      Text(
                        'Work Portfolio',
                        style: GoogleFonts.inter(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 120,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: (applicantDetails['work_pictures'] as List)
                              .length,
                          itemBuilder: (context, index) {
                            final imageUrl =
                                applicantDetails['work_pictures'][index];
                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: GestureDetector(
                                onTap: () {
                                  // Show full-screen image view
                                  showDialog(
                                    context: context,
                                    builder: (context) => Dialog(
                                      child: Image.network(imageUrl),
                                    ),
                                  );
                                },
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    imageUrl,
                                    width: 120,
                                    height: 120,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Resume
                    if (applicantDetails['resume'] != null) ...[
                      ElevatedButton.icon(
                        onPressed: () {
                          launchUrl(Uri.parse(applicantDetails['resume']));
                        },
                        icon: const Icon(Icons.description),
                        label: const Text('View Resume'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Action buttons
                  ])),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Call or message the applicant
                  // Check if this is an API request by looking for original_data
                  final bool isApiApplication =
                      widget.request.containsKey('original_data');
                  if (isApiApplication &&
                      applicantDetails['contact_no'] != null) {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) => Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ListTile(
                            leading: Icon(Icons.phone),
                            title: Text('Call'),
                            onTap: () {
                              Navigator.pop(context);
                              launchUrl(
                                Uri.parse(
                                  'tel:${applicantDetails['contact_no']}',
                                ),
                              );
                            },
                          ),
                          ListTile(
                            leading: Icon(Icons.message),
                            title: Text('Message'),
                            onTap: () {
                              Navigator.pop(context);
                              launchUrl(
                                Uri.parse(
                                  'sms:${applicantDetails['contact_no']}',
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  } else {
                    // Fallback for non-API applications
                    launchUrl(Uri.parse('tel:+919876543210'));
                  }
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: const BorderSide(color: Colors.black),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Call',
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _status == 'Accepted'
                  ? ElevatedButton(
                      onPressed: () {
                        _showJoiningRequestDialog(context);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Send Joining Request'),
                    )
                  : ElevatedButton(
                      onPressed: () {
                        _showUpdateStatusDialog(context, _status);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.black,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Update Status'),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailSection({
    required String title,
    required List<DetailItem> details,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: details.map((detail) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 120,
                      child: Text(
                        detail.label,
                        style: GoogleFonts.inter(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Expanded(
                      child: detail.isLink
                          ? GestureDetector(
                              onTap: detail.onTap,
                              child: Text(
                                detail.value,
                                style: GoogleFonts.inter(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.blue,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            )
                          : Text(
                              detail.value,
                              style: GoogleFonts.inter(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Future<void> _updateRequestStatus(String newStatus) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      final requestId = widget.request['id'];
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating request status for ID: $requestId to $newStatus');

      // Create a simple request body with only the accept field
      final requestBody = {
        'accept': newStatus == 'ACCEPTED' ? true : false,
      };

      // Use the correct API endpoint for responding to salon requests
      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/salon-requests/$requestId/respond/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        // Parse the response to get updated data
        final responseData = jsonDecode(response.body);

        // If the response contains jobseeker_id, store it for later use
        if (responseData.containsKey('jobseeker_id')) {
          setState(() {
            widget.request['jobseeker_id'] = responseData['jobseeker_id'];
          });
        }

        setState(() {
          _status = _mapApiStatusToUiStatus(newStatus);
          _statusColor = _getStatusColor(newStatus);
          _isUpdating = false;
        });

        // Return updated status to previous screen
        Navigator.pop(context, {
          'status': _status,
          'statusColor': _statusColor,
        });
      } else {
        throw Exception('Failed to update status: ${response.body}');
      }
    } catch (e) {
      print('Error updating request status: $e');
      setState(() {
        _isUpdating = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update status: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper method to map API status to UI status
  String _mapApiStatusToUiStatus(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'ACCEPTED':
        return 'Accepted';
      case 'REJECTED':
        return 'Rejected';
      default:
        return 'Pending';
    }
  }

  // Helper method to get status color
  Color _getStatusColor(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'ACCEPTED':
        return Colors.green;
      case 'REJECTED':
        return Colors.red;
      default:
        return Colors.orange;
    }
  }

  Future<void> _showConfirmationDialog(String newStatus) async {
    final action = newStatus == 'ACCEPTED' ? 'accept' : 'reject';
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Confirm ${action.capitalize()}'),
          content: Text('Are you sure you want to $action this request?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              child: Text(action.capitalize()),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (result == true) {
      await _updateRequestStatus(newStatus);
    }
  }

  Future<void> _showJoiningRequestDialog(BuildContext context) async {
    final jobTitleController = TextEditingController();
    final salaryController = TextEditingController();
    final messageController = TextEditingController(
      text:
          'We would like to offer you a position at our salon based on your profile.',
    );

    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: const Text('Send Joining Request'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Send joining request to ${widget.request['name']}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: jobTitleController,
                decoration: const InputDecoration(
                  labelText: 'Job Title',
                  border: OutlineInputBorder(),
                  hintText: 'e.g. Senior Hair Stylist',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: salaryController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Salary (₹)',
                  border: OutlineInputBorder(),
                  hintText: 'e.g. 25000',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: messageController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Message',
                  border: OutlineInputBorder(),
                  hintText: 'Enter your message to the applicant',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (jobTitleController.text.isEmpty ||
                  salaryController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please fill all required fields'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.of(dialogContext).pop();
              _sendJoiningRequest(
                jobTitleController.text,
                salaryController.text,
                messageController.text,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: const Text('Send Request'),
          ),
        ],
      ),
    );
  }

  Future<void> _sendJoiningRequest(
    String jobTitle,
    String salary,
    String message,
  ) async {
    try {
      setState(() {
        _isUpdating = true;
      });

      // Get jobseeker ID from request data
      int? jobseekerId;

      // Debug the request structure
      print('Request data structure: ${widget.request}');

      // Try different ways to extract jobseeker_id
      if (widget.request.containsKey('jobseeker_id')) {
        jobseekerId = widget.request['jobseeker_id'];
      } else if (widget.request.containsKey('id')) {
        // Some APIs return the jobseeker's ID directly as 'id'
        jobseekerId = widget.request['id'];
      } else if (widget.request.containsKey('salon_id')) {
        // For salon requests, the jobseeker ID might be in a different field
        // Try to get it from the request structure
        if (widget.request.containsKey('jobseeker')) {
          jobseekerId = widget.request['jobseeker'];
        }
      }

      // If still null, try to extract from nested structures
      if (jobseekerId == null) {
        final applicantDetails = widget.request['applicant_details'] ??
            widget.request['jobseeker_details'] ??
            {};

        if (applicantDetails.containsKey('id')) {
          jobseekerId = applicantDetails['id'];
        } else if (applicantDetails.containsKey('user_id')) {
          jobseekerId = applicantDetails['user_id'];
        }
      }

      // If still null, try to use a hardcoded ID for testing
      if (jobseekerId == null) {
        // Show dialog to manually enter jobseeker ID
        final result = await _showJobseekerIdInputDialog(context);
        if (result == null) {
          setState(() {
            _isUpdating = false;
          });
          return;
        }
        jobseekerId = int.parse(result);
      }

      // Parse salary to double
      double salaryValue = double.parse(salary);

      // Get token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Prepare request body - only include serializable data
      final requestBody = {
        'jobseeker_id': jobseekerId,
        'job_title': jobTitle,
        'salary': salaryValue,
        'message': message,
      };

      print('Sending joining request with body: $requestBody');

      // Send joining request
      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/joining-requests/create/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 201) {
        setState(() {
          _isUpdating = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Joining request sent successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Return to previous screen
        Navigator.pop(context);
      } else {
        throw Exception('Failed to send joining request: ${response.body}');
      }
    } catch (e) {
      setState(() {
        _isUpdating = false;
      });

      print('Error sending joining request: $e');

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: ${e.toString().replaceAll('Exception: ', '')}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add a dialog to manually input jobseeker ID if automatic extraction fails
  Future<String?> _showJobseekerIdInputDialog(BuildContext context) async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Enter Jobseeker ID'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Could not automatically determine the jobseeker ID. Please enter it manually:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Jobseeker ID',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a valid ID'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }
              Navigator.pop(context, controller.text);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  Future<void> _launchSocialLink(String url) async {
    // Ensure the URL has a scheme
    String launchUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      launchUrl = 'https://$url';
    }

    try {
      final Uri uri = Uri.parse(launchUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl;
      } else {
        throw 'Could not launch $launchUrl';
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open link: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to get token

  void _showUpdateStatusDialog(BuildContext context, String currentStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select new status:'),
            const SizedBox(height: 16),
            ...['Pending', 'Accepted', 'Rejected']
                .map(
                  (status) => RadioListTile<String>(
                    title: Text(status),
                    value: status,
                    groupValue: currentStatus,
                    onChanged: (value) {
                      Navigator.pop(context);
                      if (value != null && value != currentStatus) {
                        _showConfirmationDialog(value.toUpperCase());
                      }
                    },
                  ),
                )
                .toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class DetailItem {
  final String label;
  final String value;
  final bool isLink;
  final VoidCallback? onTap;

  DetailItem({
    required this.label,
    required this.value,
    this.isLink = false,
    this.onTap,
  });
}
