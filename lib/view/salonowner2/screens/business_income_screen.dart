import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:job/uttilits/color_const.dart';
import 'package:job/services/api_service.dart';

class BusinessIncomeScreen extends StatefulWidget {
  const BusinessIncomeScreen({super.key});

  @override
  State<BusinessIncomeScreen> createState() => _BusinessIncomeScreenState();
}

class _BusinessIncomeScreenState extends State<BusinessIncomeScreen> {
  // List to store transactions from API
  final List<Map<String, dynamic>> transactions = [];
  String? _errorMessage;

  // Map of service names to their IDs - will be populated from API
  final Map<String, int> serviceIds = {};

  @override
  void initState() {
    super.initState();
    _fetchTransactions();
    _fetchServicesFromAPI();
  }

  // Fetch transactions from API
  Future<void> _fetchTransactions() async {
    setState(() {
      _errorMessage = null;
    });

    try {
      final visits = await ApiService.fetchCustomerVisits();
      setState(() {
        transactions.clear();
        for (var visit in visits) {
          transactions.add({
            'id': visit['id'],
            'date': DateTime.parse(visit['date']),
            'customerName': visit['customer_name'],
            'customerPhone': visit['phone_number'],
            'category': visit['service_names'].join(', '),
            'amount': int.parse(
              visit['amount'].split('.')[0],
            ), // Convert to int
            'paymentMethod': visit['payment_method'],
          });
        }
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load transactions: $e';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $_errorMessage'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Fetch services from API and update the service IDs mapping
  Future<void> _fetchServicesFromAPI() async {
    try {
      final servicesData = await ApiService.fetchServices();
      setState(() {
        _services.clear();
        serviceIds.clear(); // Clear existing mapping

        for (var service in servicesData) {
          final serviceName = service['servicetype'] ?? '';
          final serviceId = service['id'];

          if (serviceName.isNotEmpty && serviceId != null) {
            _services.add(serviceName);
            serviceIds[serviceName] =
                serviceId; // Map service name to its actual ID
          }
        }
      });

      print('Loaded ${_services.length} services: $_services');
      print('Service IDs mapping: $serviceIds');
    } catch (e) {
      print('Error fetching services: $e');
      // Don't add any default services if API fails
    }
  }

  // Controllers for the add transaction dialog
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _customerNameController = TextEditingController();
  final TextEditingController _newServiceController = TextEditingController();
  final TextEditingController _customerPhoneController =
      TextEditingController();
// Remove default service
  DateTime _selectedDate = DateTime.now();
  String _selectedPaymentMethod = 'Cash'; // Add payment method selection

  // List to store selected services for multiple selection
  final List<String> _selectedServices = [];

  // List of services that can be expanded - will be populated from API
  final List<String> _services = [];

  // List of payment methods
  final List<String> _paymentMethods = [
    'Cash',
    'Credit Card',
    'Debit Card',
    'UPI',
    'Net Banking',
    'Digital Wallet',
  ];

  @override
  void dispose() {
    _amountController.dispose();
    _customerNameController.dispose();
    _newServiceController.dispose();
    _customerPhoneController.dispose();
    super.dispose();
  }

  // Calculate total income for the last 6 months
  int get _totalIncome {
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
    return transactions
        .where((t) => (t['date'] as DateTime).isAfter(sixMonthsAgo))
        .fold(0, (sum, item) => sum + (item['amount'] as int));
  }

  // Calculate this month's income
  int get _thisMonthIncome {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    return transactions
        .where((t) => (t['date'] as DateTime).isAfter(startOfMonth))
        .fold(0, (sum, item) => sum + (item['amount'] as int));
  }

  // Calculate last month's income
  int get _lastMonthIncome {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 1);
    return transactions.where((t) {
      final date = t['date'] as DateTime;
      return date.isAfter(startOfLastMonth.subtract(const Duration(days: 1))) &&
          date.isBefore(endOfLastMonth);
    }).fold(0, (sum, item) => sum + (item['amount'] as int));
  }

  // Calculate today's income
  int get _todayIncome {
    final now = DateTime.now();
    final todayYear = now.year;
    final todayMonth = now.month;
    final todayDay = now.day;

    return transactions.where((t) {
      final date = t['date'] as DateTime;
      return date.year == todayYear &&
          date.month == todayMonth &&
          date.day == todayDay;
    }).fold(0, (sum, item) => sum + (item['amount'] as int));
  }

  // Calculate growth percentage

  void _showAddTransactionDialog() {
    // Reset controllers
    _amountController.clear();
    _customerNameController.clear();
    _newServiceController.clear();
    _customerPhoneController.clear();
    _selectedDate = DateTime.now();
    _selectedServices.clear(); // Clear selected services

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'Add Transaction',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Customer name field
                TextField(
                  controller: _customerNameController,
                  decoration: InputDecoration(
                    labelText: 'Customer Name',
                    hintText: 'Enter customer name',
                    prefixIcon: const Icon(Icons.person),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Customer phone number field
                TextField(
                  controller: _customerPhoneController,
                  decoration: InputDecoration(
                    labelText: 'Phone Number',
                    hintText: 'Enter customer phone number',
                    prefixIcon: const Icon(Icons.phone),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                ),
                const SizedBox(height: 16),

                // Multiple Service Selection
                Text(
                  'Select Services',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),

                // Services checkboxes
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade400),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: _services.map((service) {
                      return CheckboxListTile(
                        title: Text(service),
                        value: _selectedServices.contains(service),
                        onChanged: (bool? value) {
                          setDialogState(() {
                            if (value == true) {
                              _selectedServices.add(service);
                            } else {
                              _selectedServices.remove(service);
                            }
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16),
                      );
                    }).toList(),
                  ),
                ),
                const SizedBox(height: 16),

                // Add new service section
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _newServiceController,
                        decoration: InputDecoration(
                          labelText: 'Add New Service',
                          hintText: 'Enter new service name',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.add_circle),
                      color: ColorConstants.black,
                      onPressed: () async {
                        if (_newServiceController.text.isNotEmpty) {
                          try {
                            // Add service via API

                            // Update local services list
                            final newServiceName = _newServiceController.text;
                            setDialogState(() {
                              if (!_services.contains(newServiceName)) {
                                _services.add(newServiceName);
                              }
                              _selectedServices.add(newServiceName);
                              _newServiceController.clear();
                            });

                            // Refresh services from API to get the new service ID
                            await _fetchServicesFromAPI();

                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Service "$newServiceName" added successfully'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } catch (e) {
                            // Show error message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Error adding service: ${e.toString()}'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Amount field
                TextField(
                  controller: _amountController,
                  decoration: InputDecoration(
                    labelText: 'Amount (₹)',
                    hintText: 'Enter amount',
                    prefixIcon: const Icon(Icons.currency_rupee),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                ),

                const SizedBox(height: 16),

                // Date picker
                InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _selectedDate,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now(),
                    );
                    if (picked != null && picked != _selectedDate) {
                      setState(() {
                        _selectedDate = DateTime(
                          picked.year,
                          picked.month,
                          picked.day,
                          _selectedDate.hour,
                          _selectedDate.minute,
                        );
                      });
                    }
                  },
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: 'Transaction Date',
                      prefixIcon: const Icon(Icons.calendar_today),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Payment method dropdown
                Text(
                  'Payment Method',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade400),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _selectedPaymentMethod,
                            isExpanded: true,
                            icon: const Icon(Icons.arrow_drop_down),
                            hint: Text('Select Payment Method'),
                            items: _paymentMethods.map((String method) {
                              return DropdownMenuItem<String>(
                                value: method,
                                child: Text(method),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setDialogState(() {
                                  // Use setDialogState for dropdown updates
                                  _selectedPaymentMethod = newValue;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.black),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Add transaction with all fields
                _addTransactionWithService(
                  _selectedServices.join(', '),
                  ' ', // Empty description
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.black,
                foregroundColor: Colors.black,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              child: Text(
                'Add Transaction',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addTransactionWithService(
    String service,
    String description,
  ) async {
    // Validate inputs
    if (_customerNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter customer name')),
      );
      return;
    }

    if (_selectedServices.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one service')),
      );
      return;
    }

    final amount = int.tryParse(_amountController.text);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid amount')),
      );
      return;
    }

    try {
      // Format date as YYYY-MM-DD
      final formattedDate = '${_selectedDate.year}-'
          '${_selectedDate.month.toString().padLeft(2, '0')}-'
          '${_selectedDate.day.toString().padLeft(2, '0')}';

      // Get service IDs for all selected services
      final selectedServiceIds = _selectedServices
          .map((serviceName) {
            final serviceId = serviceIds[serviceName];
            if (serviceId == null) {
              print('Warning: Service ID not found for "$serviceName"');
              print('Available services: $serviceIds');
              // Don't filter out services without IDs, let the API handle it
              return 0; // Use 0 to indicate no ID found
            }
            return serviceId;
          })
          .toList()
          .cast<int>();

      // Filter out services with no ID (0) but keep valid ones
      final validServiceIds =
          selectedServiceIds.where((id) => id != 0).toList();

      if (validServiceIds.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Error: No valid services found. Please check service mapping.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      print('Selected services: $_selectedServices');
      print('Service IDs to send: $validServiceIds');
      print(
          'Services without IDs: ${_selectedServices.where((service) => !serviceIds.containsKey(service)).toList()}');

      // Add transaction to API
      final response = await ApiService.addCustomerVisit(
        date: formattedDate,
        customerName: _customerNameController.text,
        phoneNumber: _customerPhoneController.text,
        services: validServiceIds, // Pass array of valid service IDs
        amount: amount.toString(),
        paymentMethod: _selectedPaymentMethod,
      );

      // Add the new transaction to the local list
      setState(() {
        transactions.add({
          'id': response['id'],
          'date': _selectedDate,
          'customerName': _customerNameController.text,
          'customerPhone': _customerPhoneController.text,
          'category': _selectedServices
              .join(', '), // Keep all selected services in display
          'amount': amount,
          'paymentMethod': _selectedPaymentMethod,
        });
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Added ₹$amount transaction for ${_customerNameController.text}',
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error adding transaction: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Edit a transaction
  void _editTransaction(Map<String, dynamic> transaction) async {
    // Set up controllers with existing data
    _customerNameController.text = transaction['customerName'];
    _customerPhoneController.text = transaction['customerPhone'] ?? '';
    _amountController.text = transaction['amount'].toString();
    _selectedDate = transaction['date'];
    _selectedPaymentMethod =
        transaction['paymentMethod'] ?? 'Cash'; // Set payment method

    print(
        'Edit transaction - Payment method from transaction: ${transaction['paymentMethod']}');
    print(
        'Edit transaction - Selected payment method: $_selectedPaymentMethod');

    // Extract service names and initialize selected services
    String serviceName = transaction['category'];
    _selectedServices.clear();

    // Split the category string to get individual services
    if (serviceName.contains(',')) {
      _selectedServices.addAll(serviceName.split(',').map((s) => s.trim()));
    } else {
      _selectedServices.add(serviceName);
    }

    // Show bottom sheet for editing
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        // Use StatefulBuilder to update the dialog state
        builder: (context, setDialogState) => Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom +
                10, // Increased to 90px padding
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Edit Transaction',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 20),
                  TextField(
                    controller: _customerNameController,
                    decoration: InputDecoration(
                      labelText: 'Customer Name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: _customerPhoneController,
                    decoration: InputDecoration(
                      labelText: 'Phone Number',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  SizedBox(height: 12),
                  TextField(
                    controller: _amountController,
                    decoration: InputDecoration(
                      labelText: 'Amount',
                      border: OutlineInputBorder(),
                      prefixText: '₹',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  SizedBox(height: 12),

                  // Service selection with checkboxes
                  Text(
                    'Select Services',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8),

                  // Services checkboxes
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: _services.map((service) {
                        return CheckboxListTile(
                          title: Text(service),
                          value: _selectedServices.contains(service),
                          onChanged: (bool? value) {
                            setDialogState(() {
                              // Use setDialogState for checkbox updates
                              if (value == true) {
                                _selectedServices.add(service);
                              } else {
                                _selectedServices.remove(service);
                              }
                            });
                          },
                          controlAffinity: ListTileControlAffinity.leading,
                          contentPadding: EdgeInsets.symmetric(horizontal: 16),
                        );
                      }).toList(),
                    ),
                  ),
                  SizedBox(height: 12),
                  // Payment method dropdown
                  Text(
                    'Payment Method',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<String>(
                              value: _selectedPaymentMethod,
                              isExpanded: true,
                              icon: const Icon(Icons.arrow_drop_down),
                              hint: Text('Select Payment Method'),
                              items: _paymentMethods.map((String method) {
                                return DropdownMenuItem<String>(
                                  value: method,
                                  child: Text(method),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                if (newValue != null) {
                                  setDialogState(() {
                                    // Use setDialogState for dropdown updates
                                    _selectedPaymentMethod = newValue;
                                  });
                                }
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12),
                  // Date picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (picked != null && picked != _selectedDate) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Transaction Date',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                      ),
                    ),
                  ),
                  SizedBox(height: 12),

                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('Cancel'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[700],
                        ),
                      ),
                      SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            // Format date as YYYY-MM-DD

                            // Get selected services
                            final selectedServiceNames =
                                _selectedServices.isNotEmpty
                                    ? _selectedServices
                                    : [serviceName];

                            // Convert service names to IDs
                            final selectedServiceIds = selectedServiceNames
                                .map((serviceName) {
                                  final serviceId = serviceIds[serviceName];
                                  if (serviceId == null) {
                                    print(
                                        'Warning: Service ID not found for "$serviceName"');
                                    print('Available services: $serviceIds');
                                    // Don't filter out services without IDs, let the API handle it
                                    return 0; // Use 0 to indicate no ID found
                                  }
                                  return serviceId;
                                })
                                .toList()
                                .cast<int>();

                            // Filter out services with no ID (0) but keep valid ones
                            final validServiceIds = selectedServiceIds
                                .where((id) => id != 0)
                                .toList();

                            if (validServiceIds.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      'Error: No valid services found. Please check service mapping.'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            print(
                                'Selected services for edit: $selectedServiceNames');
                            print('Service IDs for edit: $validServiceIds');
                            print(
                                'Services without IDs: ${selectedServiceNames.where((service) => !serviceIds.containsKey(service)).toList()}');

                            // Update transaction via API

                            // Update local data
                            setState(() {
                              int index = transactions.indexWhere(
                                (t) => t['id'] == transaction['id'],
                              );
                              if (index != -1) {
                                transactions[index] = {
                                  ...transaction,
                                  'customerName': _customerNameController.text,
                                  'customerPhone':
                                      _customerPhoneController.text,
                                  'amount': int.parse(_amountController.text),
                                  'category': selectedServiceNames.join(', '),
                                  'date': _selectedDate,
                                  'paymentMethod': _selectedPaymentMethod,
                                };
                              }
                            });

                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Transaction updated successfully'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error updating transaction: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        },
                        child: Text('Save Changes'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Delete a transaction
  void _deleteTransaction(Map<String, dynamic> transaction) async {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Transaction',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Are you sure you want to delete this transaction? This action cannot be undone.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[700]),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // Delete transaction via API
                await ApiService.deleteCustomerVisit(transaction['id']);

                // Remove from local list
                setState(() {
                  transactions.removeWhere((t) => t['id'] == transaction['id']);
                });

                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Transaction deleted successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Error deleting transaction: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text('Delete'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Track My Business',
          style: GoogleFonts.poppins(
              color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: ColorConstants.black,
        foregroundColor: Colors.white,
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTransactionDialog,
        backgroundColor: ColorConstants.black,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Income summary card
            _buildSummaryCard(),

            // Recent Orders header
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Orders',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      _fetchTransactions();
                      setState(() {});
                    },
                    child: Icon(Icons.refresh, size: 22, color: Colors.black),
                  ),
                ],
              ),
            ),

            // Transactions table
            transactions.isEmpty
                ? _buildEmptyState()
                : _buildTransactionsTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long, size: 70, color: Colors.grey[400]),
          SizedBox(height: 16),
          Text(
            'No transactions yet',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Add your first customer transaction',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddTransactionDialog,
            icon: Icon(Icons.add),
            label: Text('Add Transaction'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorConstants.WHITE,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Total Income (Last 6 Months)',
            style: GoogleFonts.poppins(color: Colors.black, fontSize: 16),
          ),
          const SizedBox(height: 8),
          Text(
            '₹ $_totalIncome',
            style: GoogleFonts.poppins(
              color: Colors.black,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatItem('Today', '₹ $_todayIncome'),
              _buildStatItem('This Month', '₹ $_thisMonthIncome'),
              _buildStatItem('Last Month', '₹ $_lastMonthIncome'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            color: Colors.black.withOpacity(0.7),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsTable() {
    // Sort transactions by date (newest first)
    final sortedTransactions = List<Map<String, dynamic>>.from(transactions)
      ..sort(
          (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        headingRowColor: MaterialStateProperty.all(Colors.grey[200]),
        dataRowMinHeight: 60,
        dataRowMaxHeight: 80,
        columnSpacing: 20,
        columns: [
          DataColumn(
            label: Text(
              'SI No',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
          DataColumn(
            label: Text(
              'Date',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
          DataColumn(
            label: Text(
              'Customer',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
          DataColumn(
            label: Text(
              'Service',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
          DataColumn(
            label: Text(
              'Amount',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
          DataColumn(
            label: Text(
              'Payment Method',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
          DataColumn(
            label: Text(
              'Actions',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
          ),
        ],
        rows: sortedTransactions.map((transaction) {
          final index = sortedTransactions.indexOf(transaction) + 1;
          return DataRow(
            cells: [
              DataCell(Text(index.toString(), style: GoogleFonts.poppins())),
              DataCell(
                Text(
                  DateFormat('dd MMM yyyy').format(transaction['date']),
                  style: GoogleFonts.poppins(),
                ),
              ),
              DataCell(
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction['customerName'],
                      style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                    ),
                    if (transaction['customerPhone'] != null &&
                        transaction['customerPhone'].isNotEmpty)
                      Text(
                        transaction['customerPhone'],
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
              DataCell(
                Text(transaction['category'], style: GoogleFonts.poppins()),
              ),
              DataCell(
                Text(
                  '₹${transaction['amount']}',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                ),
              ),
              DataCell(
                Text(
                  transaction['paymentMethod'] ?? 'Cash',
                  style: GoogleFonts.poppins(),
                ),
              ),
              DataCell(
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(Icons.edit, size: 18),
                      onPressed: () => _editTransaction(transaction),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                    SizedBox(width: 8),
                    IconButton(
                      icon: Icon(Icons.delete, size: 18, color: Colors.red),
                      onPressed: () => _deleteTransaction(transaction),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  // Helper method to get month name

  // Helper method to generate random order code
}
