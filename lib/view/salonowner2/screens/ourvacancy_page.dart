import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/view/salonowner2/edit/add_new_vacancy_screen.dart';
import 'package:job/view/salonowner2/detils/vacancy_details_screen.dart';
import 'package:flutter/cupertino.dart';

class OurvacancyPage extends StatefulWidget {
  const OurvacancyPage({
    super.key,
  });

  @override
  State<OurvacancyPage> createState() => _OurvacancyPageState();
}

class _OurvacancyPageState extends State<OurvacancyPage> {
  List<Map<String, dynamic>> _jobs = [];
  List<Map<String, dynamic>> _professionals =
      []; // New variable for API professionals
  bool _isLoading = false;
  // New loading state for professionals
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _professionalSearchController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchJobs();
    _fetchApplications();
    _fetchProfessionals(); // Fetch professionals when the page loads
  }

  Future<void> _fetchJobs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final jobsData = await ApiService.getJobs();
      setState(() {
        _jobs = jobsData;
        _isLoading = false;
      });
    } catch (e) {
      print('Error fetching jobs: $e');
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load jobs: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch applications
  Future<void> _fetchApplications() async {
    setState(() {});

    try {
      setState(() {});
    } catch (e) {
      print('Error fetching applications: $e');
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load applications: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch professionals
  Future<void> _fetchProfessionals() async {
    setState(() {});

    try {
      final professionalsData = await ApiService.fetchOpenJobseekers();
      setState(() {
        _professionals = professionalsData;
      });
    } catch (e) {
      print('Error fetching professionals: $e');
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load professionals: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Replace the hardcoded jobsList with a getter that formats API data
  List<Map<String, dynamic>> get filteredJobs {
    List<Map<String, dynamic>> results = List.from(_jobs);

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      results = results.where((job) {
        final title = job['title']?.toString().toLowerCase() ?? '';
        final location = job['location']?.toString().toLowerCase() ?? '';
        final jobType = job['job_type']?.toString().toLowerCase() ?? '';
        final experience = job['experience']?.toString().toLowerCase() ?? '';

        return title.contains(query) ||
            location.contains(query) ||
            jobType.contains(query) ||
            experience.contains(query);
      }).toList();
    }

    return results;
  }

  // Update the filtered professionals getter to use API data
  List<Map<String, dynamic>> get filteredProfessionals {
    if (_professionals.isEmpty) {
      return [];
    }

    List<Map<String, dynamic>> results = List.from(_professionals);

    // Apply search filter
    if (_professionalSearchController.text.isNotEmpty) {
      final query = _professionalSearchController.text.toLowerCase();
      results = results.where((professional) {
        final name = professional['name']?.toString().toLowerCase() ?? '';
        final expertise = professional['expertise_areas_data'] != null &&
                professional['expertise_areas_data'].isNotEmpty
            ? professional['expertise_areas_data'][0]['name']
                    ?.toString()
                    .toLowerCase() ??
                ''
            : '';

        return name.contains(query) || expertise.contains(query);
      }).toList();
    }

    return results;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _professionalSearchController.dispose();
    super.dispose();
  }

  final List<String> categories = [
    'All Jobs',
    'Design',
    'Development',
    'Marketing',
    'Sales',
    'Finance',
  ];

  // Helper function to convert gender code to display text
  String _formatGenderDisplay(String? genderCode) {
    switch (genderCode?.toLowerCase()) {
      case 'male':
        return 'Male';
      case 'female':
        return 'Female';
      case 'anyone':
      case '':
      case null:
        return 'Anyone';
      default:
        return genderCode ?? 'Anyone';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AddNewVacancyScreen()),
          );
        },
        backgroundColor: Colors.black,
        elevation: 8,
        child: const Icon(Icons.add, color: Colors.white, size: 24),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern Header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 0,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      //   color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.arrow_back_ios_new, size: 26),
                      onPressed: () => Navigator.pop(context),
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Our Vacancies',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          'Manage your job postings',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Modern Search Bar
            Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 0,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search vacancies...',
                  hintStyle: TextStyle(color: Colors.grey[400]),
                  prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                  border: InputBorder.none,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                ),
                onChanged: (value) {
                  setState(() {});
                },
              ),
            ),

            // Content
            Expanded(child: _buildVacanciesTab()),
          ],
        ),
      ),
    );
  }

  Widget _buildVacanciesTab() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    spreadRadius: 0,
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading vacancies...',
              style: GoogleFonts.inter(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (filteredJobs.isEmpty) {
      return Center(
        child: Container(
          margin: const EdgeInsets.all(40),
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                spreadRadius: 0,
                blurRadius: 20,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.work_outline,
                  size: 48,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'No vacancies yet',
                style: GoogleFonts.inter(
                  fontSize: 20,
                  fontWeight: FontWeight.w700,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first job posting to attract talented professionals',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddNewVacancyScreen(),
                    ),
                  );

                  if (result != null && result['action'] == 'add') {
                    _fetchJobs();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.add, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Add New Vacancy',
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _fetchJobs,
      color: Colors.black,
      child: ListView.builder(
        padding: const EdgeInsets.only(top: 8, bottom: 20),
        itemCount: filteredJobs.length,
        itemBuilder: (context, index) {
          final job = filteredJobs[index];

          // Format the posted date
          String postedTimeText = 'Posted recently';
          if (job['posted_on'] != null) {
            try {
              final postedDate = DateTime.parse(job['posted_on']);
              final difference = DateTime.now().difference(postedDate);

              if (difference.inDays > 0) {
                postedTimeText =
                    'Posted ${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
              } else if (difference.inHours > 0) {
                postedTimeText =
                    'Posted ${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
              } else {
                postedTimeText =
                    'Posted ${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
              }
            } catch (e) {
              print('Error parsing date: $e');
            }
          }

          return _buildVacancyCard(
            title: job['title'] ?? 'No Title',
            location: job['location'] ?? 'No Location',
            description: job['job_summary'] ?? 'No Description',
            postedTime: postedTimeText,
            gender: _formatGenderDisplay(job['gender']),
            onTap: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => VacancyDetailsScreen(vacancy: job),
                ),
              );

              if (result != null) {
                if (result['action'] == 'delete') {
                  _fetchJobs();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Vacancy has been deleted'),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else if (result['action'] == 'update') {
                  _fetchJobs();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Vacancy has been updated'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
          );
        },
      ),
    );
  }

  Widget _buildVacancyCard({
    required String title,
    required String location,
    required String description,
    required String postedTime,
    required VoidCallback onTap,
    String gender = 'Anyone',
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16, left: 20, right: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and status
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: GoogleFonts.inter(
                              fontWeight: FontWeight.w700,
                              fontSize: 18,
                              color: Colors.black87,
                              height: 1.3,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.location_on_outlined,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 6),
                              Text(
                                location,
                                style: GoogleFonts.inter(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Status indicator
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.green[200]!),
                      ),
                      child: Text(
                        'Active',
                        style: GoogleFonts.inter(
                          color: Colors.green[700],
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Description
                Text(
                  description,
                  style: GoogleFonts.inter(
                    color: Colors.grey[700],
                    fontSize: 14,
                    height: 1.5,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 20),

                // Bottom row with gender and time
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Gender info
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.person_outline,
                            size: 14,
                            color: Colors.blue[600],
                          ),
                          const SizedBox(width: 6),
                          Text(
                            gender,
                            style: GoogleFonts.inter(
                              color: Colors.blue[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Posted time
                    Text(
                      postedTime,
                      style: GoogleFonts.inter(
                        color: Colors.grey[500],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Store applications in state so they can be modified

  // State variables for expanded sections

  // Helper method to map API status to UI status

  // Helper method to get status color
}
