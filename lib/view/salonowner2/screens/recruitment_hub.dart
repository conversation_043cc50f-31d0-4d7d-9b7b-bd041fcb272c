import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/uttilits/api_constants.dart';
import 'package:job/view/salonowner2/detils/application_details_screen.dart';
import 'package:job/view/salonowner2/detils/via_request_details_screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class RecruitmentHub extends StatefulWidget {
  const RecruitmentHub({super.key});

  @override
  State<RecruitmentHub> createState() => _RecruitmentHubState();
}

class _RecruitmentHubState extends State<RecruitmentHub> {
  List<Map<String, dynamic>> _jobs = [];
  List<Map<String, dynamic>> _applications = [];
  List<Map<String, dynamic>> _professionals = [];
  List<Map<String, dynamic>> _joiningRequests = [];
  bool _isLoadingApplications = false;
  bool _isLoadingJoiningRequests = false;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _professionalSearchController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchJobs();
    _fetchApplications();
    _fetchProfessionals();
    _fetchJoiningRequests();
  }

  Future<void> _fetchJobs() async {
    setState(() {});

    try {
      final jobsData = await ApiService.getJobs();
      setState(() {
        _jobs = jobsData;
      });
    } catch (e) {
      print('Error fetching jobs: $e');
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load jobs: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch applications
  Future<void> _fetchApplications() async {
    setState(() {
      _isLoadingApplications = true;
    });

    try {
      final applicationsData = await ApiService.getApplications();
      setState(() {
        _applications = applicationsData;
        _isLoadingApplications = false;
      });
    } catch (e) {
      print('Error fetching applications: $e');
      setState(() {
        _isLoadingApplications = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load applications: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch professionals
  Future<void> _fetchProfessionals() async {
    setState(() {});

    try {
      final professionalsData = await ApiService.fetchOpenJobseekers();
      setState(() {
        _professionals = professionalsData;
      });
    } catch (e) {
      print('Error fetching professionals: $e');
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load professionals: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch joining requests
  Future<void> _fetchJoiningRequests() async {
    setState(() {
      _isLoadingJoiningRequests = true;
    });

    try {
      // Use the salon-requests-list endpoint
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse(ApiConstants.salonRequestsList),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        setState(() {
          _joiningRequests =
              data.map((item) => item as Map<String, dynamic>).toList();
          _isLoadingJoiningRequests = false;
        });
      } else {
        throw Exception(
            'Failed to load joining requests: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching joining requests: $e');
      setState(() {
        _isLoadingJoiningRequests = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load joining requests: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to refresh all data
  Future<void> _refreshAllData() async {
    await Future.wait([
      _fetchApplications(),
      _fetchJoiningRequests(),
      _fetchJobs(),
      _fetchProfessionals(),
    ]);
  }

  // Replace the hardcoded jobsList with a getter that formats API data
  List<Map<String, dynamic>> get filteredJobs {
    List<Map<String, dynamic>> results = List.from(_jobs);

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      results = results.where((job) {
        final title = job['title']?.toString().toLowerCase() ?? '';
        final location = job['location']?.toString().toLowerCase() ?? '';
        final jobType = job['job_type']?.toString().toLowerCase() ?? '';
        final experience = job['experience']?.toString().toLowerCase() ?? '';

        return title.contains(query) ||
            location.contains(query) ||
            jobType.contains(query) ||
            experience.contains(query);
      }).toList();
    }

    return results;
  }

  // Update the filtered professionals getter to use API data
  List<Map<String, dynamic>> get filteredProfessionals {
    if (_professionals.isEmpty) {
      return [];
    }

    List<Map<String, dynamic>> results = List.from(_professionals);

    // Apply search filter
    if (_professionalSearchController.text.isNotEmpty) {
      final query = _professionalSearchController.text.toLowerCase();
      results = results.where((professional) {
        final name = professional['name']?.toString().toLowerCase() ?? '';
        final expertise = professional['expertise_areas_data'] != null &&
                professional['expertise_areas_data'].isNotEmpty
            ? professional['expertise_areas_data'][0]['name']
                    ?.toString()
                    .toLowerCase() ??
                ''
            : '';

        return name.contains(query) || expertise.contains(query);
      }).toList();
    }

    return results;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _professionalSearchController.dispose();
    super.dispose();
  }

  final List<String> categories = [
    'All Jobs',
    'Design',
    'Development',
    'Marketing',
    'Sales',
    'Finance',
  ];

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.04;

    return SafeArea(
      bottom: false,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header without tabs
            Container(
              padding: EdgeInsets.symmetric(
                //  horizontal: padding,
                vertical: padding * 0.7,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back_ios_new),
                    onPressed: () => Navigator.pop(context),
                  ),
                  SizedBox(width: 28),
                  Text(
                    'Recruitment Hub',
                    style: GoogleFonts.poppins(
                      fontSize: size.width * 0.06,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // Content - directly show applications
            Expanded(child: _buildApplicationsTab()),
          ],
        ),
      ),
    );
  }

  // Store applications in state so they can be modified

  // State variables for expanded sections
  bool _isPortalExpanded = false;
  bool _isRequestExpanded = false;

  Widget _buildApplicationsTab() {
    if (_isLoadingApplications || _isLoadingJoiningRequests) {
      return Center(child: CircularProgressIndicator());
    }

    // Group applications by source
    // For API data, we'll consider all as "Job Portal" for now
    final portalApplications = _applications.where((app) {
      // Filter out rejected applications
      return (app['status'] ?? 'PENDING').toUpperCase() != 'REJECTED';
    }).map((app) {
      final applicantDetails = app['applicant_details'] ?? {};

      return {
        'id': app['id'],
        'job_id': app['job'],
        'name': applicantDetails['name'] ?? 'Unknown',
        'position': app['job_title'] ?? 'Unknown Position',
        'experience': '${applicantDetails['years_of_experience'] ?? 0} years',
        'status': _mapApiStatusToUiStatus(app['status'] ?? 'PENDING'),
        'statusColor': _getStatusColor(app['status'] ?? 'PENDING'),
        'image': applicantDetails['profile_picture'] ??
            'https://img.icons8.com/ios-filled/50/user.png',
        'source': 'Job Portal',
        'applied_on': app['applied_on'],
        'applicant_details': applicantDetails,
        'original_data': app, // Keep the original data for reference
      };
    }).toList();

    // Format joining requests for the "Via Request" section
    final requestApplications = _joiningRequests.map((request) {
      final jobseekerDetails = request['jobseeker_details'] ?? {};

      // Format experience properly
      String experienceText = 'Not specified';
      if (jobseekerDetails['years_of_experience'] != null) {
        final experience = jobseekerDetails['years_of_experience'];
        if (experience is num) {
          final years = experience.floor();
          final months = ((experience - years) * 12).round();

          if (years > 0 && months > 0) {
            experienceText = '$years years, $months months';
          } else if (years > 0) {
            experienceText = '$years years';
          } else if (months > 0) {
            experienceText = '$months months';
          } else {
            experienceText = 'Less than a month';
          }
        } else {
          experienceText = '$experience years';
        }
      }

      return {
        'id': request['id'],
        'name': request['jobseeker_name'] ?? 'Unknown',
        'position': request['job_title'] ?? 'Unknown Position',
        'experience': experienceText,
        'status': _mapApiStatusToUiStatus(request['status'] ?? 'PENDING'),
        'statusColor': _getStatusColor(request['status'] ?? 'PENDING'),
        'image': jobseekerDetails['profile_picture'] ??
            'https://img.icons8.com/ios-filled/50/user.png',
        'source': 'Request',
        'applied_on': request['created_at'],
        'applicant_details': jobseekerDetails,
        'original_data': request, // Keep the original data for reference
        'message': request['message'],
        'salary': request['salary'],
      };
    }).toList();

    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        // iOS-style refresh control
        CupertinoSliverRefreshControl(
          onRefresh: _refreshAllData,
          builder: (
            context,
            refreshState,
            pulledExtent,
            refreshTriggerPullDistance,
            refreshIndicatorExtent,
          ) {
            return Center(
              child: CupertinoActivityIndicator(
                radius: 14,
                color: CupertinoColors.systemGrey,
              ),
            );
          },
        ),

        // Content
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              // Applications via Job Portal
              _buildApplicationSection(
                title: 'Via Job Portal',
                applications: portalApplications,
                isExpanded: _isPortalExpanded,
                onSeeMorePressed: () {
                  setState(() {
                    _isPortalExpanded = !_isPortalExpanded;
                  });
                },
                icon: Icons.work_outline,
                color: Colors.blue,
              ),

              const SizedBox(height: 24),

              // Applications via Invite
              const SizedBox(height: 24),

              // Applications via Request
              _buildApplicationSection(
                title: 'Via Request',
                applications: requestApplications,
                isExpanded: _isRequestExpanded,
                onSeeMorePressed: () {
                  setState(() {
                    _isRequestExpanded = !_isRequestExpanded;
                  });
                },
                icon: Icons.contact_mail_outlined,
                color: Colors.purple,
              ),
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildApplicationSection({
    required String title,
    required List<Map<String, dynamic>> applications,
    required bool isExpanded,
    required VoidCallback onSeeMorePressed,
    required IconData icon,
    required Color color,
  }) {
    // Determine if we need a "See More" button
    final hasMoreThan5 = applications.length > 5;
    final displayedApplications =
        isExpanded ? applications : applications.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 8),
            Text(
              '$title (${applications.length})',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // No applications message
        if (applications.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Center(
              child: Column(
                children: [
                  Icon(Icons.person_search, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'No applications yet',
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Applications list
        if (applications.isNotEmpty)
          ...displayedApplications
              .map((application) => _buildApplicationCard(application))
              .toList(),

        // See more button
        if (hasMoreThan5)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: InkWell(
              onTap: onSeeMorePressed,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Center(
                  child: Text(
                    isExpanded
                        ? 'Show Less'
                        : 'See All ${applications.length} Applications',
                    style: GoogleFonts.inter(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildApplicationCard(Map<String, dynamic> application) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () async {
            // Make sure the application ID is an integer
            if (application['id'] is String) {
              application['id'] = int.tryParse(application['id']) ?? 0;
            }

            // Navigate to appropriate details screen based on source
            if (application['source'] == 'Request') {
              // Navigate to request details screen
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      ViaRequestDetailsScreen(request: application),
                ),
              );

              // Update status if returned from details screen
              if (result != null && result is Map<String, dynamic>) {
                setState(() {
                  application['status'] = result['status'];
                  application['statusColor'] = result['statusColor'];
                });

                // Refresh all data when returning from details screen
                _refreshAllData();
              }
            } else {
              // Navigate to application details screen
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      ApplicationDetailsScreen(application: application),
                ),
              );

              // Update status if returned from details screen
              if (result != null && result is Map<String, dynamic>) {
                setState(() {
                  application['status'] = result['status'];
                  application['statusColor'] = result['statusColor'];
                });

                // Refresh all data when returning from details screen
                _refreshAllData();
              }
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CircleAvatar(
                      radius: 25,
                      backgroundImage: NetworkImage(application['image']),
                      backgroundColor: Colors.grey[200],
                      onBackgroundImageError: (exception, stackTrace) {
                        // Fallback if image fails to load
                        print('Error loading image: $exception');
                      },
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            application['name'],
                            style: GoogleFonts.inter(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${application['position']} • ${application['experience']}',
                            style: GoogleFonts.inter(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildStatusChip(
                      application['status'],
                      application['statusColor'],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        status,
        style: GoogleFonts.inter(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Helper method to map API status to UI status
  String _mapApiStatusToUiStatus(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return 'Pending';
      case 'ACCEPTED':
        return 'Accepted';
      case 'REJECTED':
        return 'Rejected';
      case 'HIRED':
        return 'Hired';
      default:
        return 'Pending';
    }
  }

  // Helper method to get status color
  Color _getStatusColor(String apiStatus) {
    switch (apiStatus.toUpperCase()) {
      case 'PENDING':
        return Colors.orange;
      case 'ACCEPTED':
        return Colors.green;
      case 'REJECTED':
        return Colors.red;
      case 'HIRED':
        return Colors.blue;
      default:
        return Colors.orange;
    }
  }
}
