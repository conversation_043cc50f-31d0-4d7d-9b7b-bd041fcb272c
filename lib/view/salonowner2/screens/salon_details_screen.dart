import 'package:flutter/material.dart';
import 'package:job/view/common/welcome_screen/welcome_screen.dart';
import 'package:job/view/salonowner2/edit/saloon_edit_profile.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:job/services/api_service.dart';
import 'package:android_intent_plus/android_intent.dart' as android_intent;
import 'dart:io' show Platform;

class salonprofile_page extends StatefulWidget {
  const salonprofile_page({super.key});

  @override
  State<salonprofile_page> createState() => _salonprofile_pageState();
}

class _salonprofile_pageState extends State<salonprofile_page> {
  bool isLoading = true;
  Map<String, dynamic> salonProfile = {};
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _fetchSalonProfile();
  }

  Future<void> _fetchSalonProfile() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      // Fetch salon profile data from API
      final response = await ApiService.fetchSalonProfile();

      setState(() {
        salonProfile = response;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Failed to load salon profile: $e';
        isLoading = false;
      });
      print('Error fetching salon profile: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit_outlined, color: Colors.black),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SaloonEditProfile(),
                ),
              );

              if (result == true) {
                _fetchSalonProfile();
              }
            },
          ),
        ],
      ),
      body: isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.black),
            )
          : errorMessage.isNotEmpty
              ? Center(
                  child: Text(
                    errorMessage,
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      // Profile Section
                      Container(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          children: [
                            // Profile Image
                            Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                    color: Colors.grey.shade200, width: 3),
                              ),
                              child: salonProfile['profile_picture'] != null
                                  ? ClipOval(
                                      child: Image.network(
                                        salonProfile['profile_picture'],
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            color: Colors.grey.shade100,
                                            child: const Icon(
                                              Icons.business,
                                              size: 40,
                                              color: Colors.grey,
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  : Container(
                                      color: Colors.grey.shade100,
                                      child: const Icon(
                                        Icons.business,
                                        size: 40,
                                        color: Colors.grey,
                                      ),
                                    ),
                            ),
                            const SizedBox(height: 16),

                            // Salon Name
                            Text(
                              salonProfile['salon_name'] ?? 'Salon Name',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Location
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.location_on,
                                    size: 16, color: Colors.grey.shade600),
                                const SizedBox(width: 4),
                                Text(
                                  salonProfile['location'] ?? 'Location',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Status Badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: salonProfile['is_approved'] == true
                                    ? Colors.green.shade50
                                    : Colors.orange.shade50,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                salonProfile['is_approved'] == true
                                    ? 'Approved'
                                    : 'Pending',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: salonProfile['is_approved'] == true
                                      ? Colors.green.shade700
                                      : Colors.orange.shade700,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Action Buttons
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Row(
                          children: [
                            Expanded(
                              child: _buildActionButton(
                                icon: Icons.phone,
                                label: 'Call',
                                onTap: () => _launchPhone(
                                    salonProfile['contact_no'] ?? ''),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildActionButton(
                                icon: Icons.map,
                                label: 'Directions',
                                onTap: () => _launchMaps(
                                    salonProfile['google_map_link'] ?? ''),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Information Sections
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInfoSection(
                              title: 'Salon Information',
                              items: [
                                _buildInfoItem(
                                    'Salon Name',
                                    salonProfile['salon_name'] ??
                                        'Not available'),
                                _buildInfoItem(
                                    'Location',
                                    salonProfile['location'] ??
                                        'Not available'),
                                _buildInfoItem(
                                    'Pin Code',
                                    salonProfile['pin_code'] ??
                                        'Not available'),
                                _buildInfoItem(
                                    'Unique ID',
                                    salonProfile['unique_id'] ??
                                        'Not available'),
                                _buildInfoItem(
                                    'Salon Type',
                                    _getSalonTypeDisplay(
                                        salonProfile['salon_type'])),
                              ],
                            ),
                            const SizedBox(height: 24),
                            if (salonProfile['about'] != null &&
                                salonProfile['about'].toString().isNotEmpty)
                              _buildInfoSection(
                                title: 'About',
                                items: [
                                  _buildInfoItem(
                                      'Description', salonProfile['about'],
                                      isDescription: true),
                                ],
                              ),
                            if (salonProfile['about'] != null &&
                                salonProfile['about'].toString().isNotEmpty)
                              const SizedBox(height: 24),
                            // Gallery Section
                            _buildInfoSection(
                              title: 'Gallery',
                              items: [
                                _buildGalleryItem(
                                    salonProfile['salon_images'] ?? []),
                              ],
                            ),
                            const SizedBox(height: 24),
                            _buildInfoSection(
                              title: 'Owner Information',
                              items: [
                                _buildInfoItem(
                                    'Owner\'s Name',
                                    salonProfile['owner_name'] ??
                                        'Not available'),
                                _buildInfoItem(
                                    'Contact Number',
                                    salonProfile['contact_no'] ??
                                        'Not available',
                                    isLink: true),
                                _buildInfoItem('Email',
                                    salonProfile['email'] ?? 'Not available'),
                              ],
                            ),
                            const SizedBox(height: 24),
                            _buildInfoSection(
                              title: 'Membership',
                              items: [
                                _buildInfoItem(
                                    'Status',
                                    salonProfile['membership_active'] == true
                                        ? 'Active'
                                        : 'Inactive'),
                                _buildInfoItem(
                                    'Start Date',
                                    salonProfile['membership_start_date'] ??
                                        'Not available'),
                                _buildInfoItem(
                                    'End Date',
                                    salonProfile['membership_end_date'] ??
                                        'Not available'),
                              ],
                            ),
                            const SizedBox(height: 10),
                            _buildMenuItem(
                              context,
                              icon: Icons.logout,
                              title: 'Logout',
                              subtitle: 'Sign out from your account',
                              iconColor: Colors.red,
                              onTap: () => _handleLogout(context),
                            ),
                            const SizedBox(height: 30),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  String _getSalonTypeDisplay(String? salonType) {
    switch (salonType?.toUpperCase()) {
      case 'MALE':
        return 'Male Salon';
      case 'FEMALE':
        return 'Female Salon';
      case 'UNISEX':
        return 'Unisex Salon';
      default:
        return salonType ?? 'Not specified';
    }
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.black, size: 24),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value,
      {bool isLink = false, bool isDescription = false}) {
    if (isDescription) {
      return Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black87,
            height: 1.5,
          ),
        ),
      );
    }

    return InkWell(
      onTap: isLink
          ? () {
              if (label == 'Contact Number') {
                _launchPhone(value);
              }
            }
          : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isLink ? Colors.blue : Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            if (isLink)
              Icon(Icons.arrow_forward_ios,
                  size: 16, color: Colors.grey.shade400),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    VoidCallback? onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      leading: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: iconColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: iconColor, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
      ),
      subtitle: Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          subtitle,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
      ),
      trailing: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          shape: BoxShape.circle,
        ),
        child: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
      ),
      onTap: onTap,
    );
  }

  void _handleLogout(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: const Row(
            children: [
              Icon(Icons.logout, color: Colors.red),
              SizedBox(width: 8),
              Text(
                'Logout',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          content: const Text(
            'Are you sure you want to logout from your account?',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder: (context) => const WelcomeScreen(),
                ),
                (route) => false,
              ),
              child: const Text(
                'Logout',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

//   void _handleLogout(BuildContext context) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           backgroundColor: Colors.white,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(20),
//           ),
//           title: const Row(
//             children: [
//               Icon(Icons.logout, color: Colors.red),
//               SizedBox(width: 8),
//               Text(
//                 'Logout',
//                 style: TextStyle(
//                   fontWeight: FontWeight.bold,
//                   color: Colors.red,
//                 ),
//               ),
//             ],
//           ),
//           content: const Text(
//             'Are you sure you want to logout from your account?',
//             style: TextStyle(fontSize: 16),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.pop(context),
//               child: Text(
//                 'Cancel',
//                 style: TextStyle(
//                   color: Colors.grey[600],
//                   fontWeight: FontWeight.w600,
//                 ),
//               ),
//             ),
//             ElevatedButton(
//               onPressed: () async {
//                 // Clear user session data
//                 try {
//                   final prefs = await SharedPreferences.getInstance();
//                   await prefs.setBool('is_logged_in', false);
//                   await prefs.remove('user_type');
//                   await prefs.remove('token');
//                   await prefs.remove('user_data');
//                 } catch (e) {
//                   print('Error clearing user data: $e');
//                 }

  Widget _buildGalleryItem(List images) {
    if (images.isEmpty) {
      return Container(
        height: 120,
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Text(
            'No images available',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.all(16),
        itemCount: images.length,
        itemBuilder: (context, index) {
          final image = images[index];

          return Container(
            width: 120,
            margin: const EdgeInsets.only(right: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                image['image'] ?? '',
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    color: Colors.grey.shade100,
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                        strokeWidth: 2,
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey.shade200,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image_not_supported,
                          color: Colors.grey.shade400,
                          size: 32,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Failed to load',
                          style: TextStyle(
                            color: Colors.grey.shade500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  // Helper methods to launch external actions
  Future<void> _launchPhone(String phoneNumber) async {
    if (phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Phone number not available')),
      );
      return;
    }

    try {
      // Clean the phone number (remove spaces, etc.)
      final cleanedNumber = phoneNumber.replaceAll(RegExp(r'\s+'), '');

      if (Platform.isAndroid) {
        // For Android, use the intent package for better control
        final intent = android_intent.AndroidIntent(
          action: 'android.intent.action.DIAL',
          data: 'tel:$cleanedNumber',
        );
        await intent.launch();
      } else {
        // For iOS and other platforms
        final Uri launchUri = Uri(scheme: 'tel', path: cleanedNumber);
        if (await canLaunchUrl(launchUri)) {
          await launchUrl(launchUri);
        } else {
          throw 'Could not launch $launchUri';
        }
      }
    } catch (e) {
      print('Error launching phone dialer: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not launch phone dialer: $e')),
      );
    }
  }

  Future<void> _launchMaps(String mapUrl) async {
    try {
      print('Attempting to launch map URL: $mapUrl');

      // Ensure the URL is properly formatted
      Uri uri;
      if (mapUrl.isEmpty) {
        throw 'Map URL is empty';
      }

      try {
        uri = Uri.parse(mapUrl);
      } catch (e) {
        throw 'Invalid map URL format: $e';
      }

      // Try different launch methods
      bool launched = false;

      // 1. Try using android_intent_plus for Android
      if (Platform.isAndroid) {
        try {
          final intent = android_intent.AndroidIntent(
            action: 'action_view',
            data: uri.toString(),
            package: 'com.google.android.apps.maps',
          );
          await intent.launch();
          print('Launched map via Android Intent');
          return;
        } catch (e) {
          print('Android Intent for maps failed: $e');
          // Continue to other methods if this fails
        }
      }

      // 2. Try external application mode
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
        print('Launch result (external): $launched');
        if (launched) return;
      }

      // 3. Try platform default as fallback
      if (await canLaunchUrl(uri)) {
        launched = await launchUrl(uri, mode: LaunchMode.platformDefault);
        print('Launch result (platform): $launched');
        if (launched) return;
      }

      // 4. Last resort: try a generic Google Maps URL
      final fallbackUri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=${Uri.encodeComponent(salonProfile['location'] ?? '')}',
      );
      if (await canLaunchUrl(fallbackUri)) {
        launched = await launchUrl(
          fallbackUri,
          mode: LaunchMode.externalApplication,
        );
        print('Launch result (fallback): $launched');
        if (!launched) {
          throw 'Could not launch maps';
        }
      } else {
        throw 'Could not parse map URL';
      }
    } catch (e) {
      print('Error launching maps: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Could not open maps: $e')));
    }
  }
}
