import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:job/view/salonowner2/detils/professional_details_screen.dart';
import 'package:job/view/salonowner2/screens/invitation_history_screen.dart';
import 'package:flutter/cupertino.dart';

class FindPrefessionals extends StatefulWidget {
  const FindPrefessionals({super.key});

  @override
  State<FindPrefessionals> createState() => _FindPrefessionalsState();
}

class _FindPrefessionalsState extends State<FindPrefessionals>
    with WidgetsBindingObserver {
  List<Map<String, dynamic>> _jobs = [];
  List<Map<String, dynamic>> _professionals =
      []; // New variable for API professionals
  bool _isLoadingProfessionals = false; // New loading state for professionals
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _professionalSearchController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchJobs();
    _fetchApplications();
    _fetchProfessionals(); // Fetch professionals when the page loads
  }

  Future<void> _fetchJobs() async {
    setState(() {});

    try {
      final jobsData = await ApiService.getJobs();
      setState(() {
        _jobs = jobsData;
      });
    } catch (e) {
      print('Error fetching jobs: $e');
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load jobs: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch applications
  Future<void> _fetchApplications() async {
    setState(() {});

    try {
      setState(() {});
    } catch (e) {
      print('Error fetching applications: $e');
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load applications: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Add this method to fetch professionals
  Future<void> _fetchProfessionals() async {
    setState(() {
      _isLoadingProfessionals = true;
    });

    try {
      final professionalsData = await ApiService.fetchOpenJobseekers();
      setState(() {
        _professionals = professionalsData;
        _isLoadingProfessionals = false;
      });
    } catch (e) {
      print('Error fetching professionals: $e');
      setState(() {
        _isLoadingProfessionals = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load professionals: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Replace the hardcoded jobsList with a getter that formats API data
  List<Map<String, dynamic>> get filteredJobs {
    List<Map<String, dynamic>> results = List.from(_jobs);

    // Apply search filter
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      results = results.where((job) {
        final title = job['title']?.toString().toLowerCase() ?? '';
        final location = job['location']?.toString().toLowerCase() ?? '';
        final jobType = job['job_type']?.toString().toLowerCase() ?? '';
        final experience = job['experience']?.toString().toLowerCase() ?? '';

        return title.contains(query) ||
            location.contains(query) ||
            jobType.contains(query) ||
            experience.contains(query);
      }).toList();
    }

    return results;
  }

  // Update the filtered professionals getter to use API data
  List<Map<String, dynamic>> get filteredProfessionals {
    if (_professionals.isEmpty) {
      return [];
    }

    List<Map<String, dynamic>> results = List.from(_professionals);

    // Apply search filter
    if (_professionalSearchController.text.isNotEmpty) {
      final query = _professionalSearchController.text.toLowerCase();
      results = results.where((professional) {
        final name = professional['name']?.toString().toLowerCase() ?? '';
        final expertise = professional['expertise_areas_data'] != null &&
                professional['expertise_areas_data'].isNotEmpty
            ? professional['expertise_areas_data'][0]['name']
                    ?.toString()
                    .toLowerCase() ??
                ''
            : '';

        return name.contains(query) || expertise.contains(query);
      }).toList();
    }

    return results;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _professionalSearchController.dispose();
    super.dispose();
  }

  final List<String> categories = [
    'All Jobs',
    'Design',
    'Development',
    'Marketing',
    'Sales',
    'Finance',
  ];

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = size.width * 0.04;

    return SafeArea(
      bottom: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header without tabs
            Container(
              padding: EdgeInsets.symmetric(
                //  horizontal: padding,
                vertical: padding * 0.5,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    icon: Icon(Icons.arrow_back_ios_new),
                    onPressed: () {
                      // Simply pop the current screen to go back
                      Navigator.pop(context);
                    },
                  ),
                  SizedBox(width: 28),
                  Text(
                    'Search Professionals',
                    style: GoogleFonts.poppins(
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            // Search bar for professionals
            Padding(
              padding: EdgeInsets.all(padding),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          //  Hired Employees button
                          ElevatedButton(
                            onPressed: () {
                              //   Navigate to hired employees screen without passing data
                              // It will fetch data from API directly
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const InvitationHistoryScreen(),
                                ),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: Colors.black,
                              side: const BorderSide(
                                color: Colors.black,
                              ),
                              elevation: 2,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 10,
                              ),
                            ),
                            child: Text(
                              'Invitation History',
                              style: GoogleFonts.inter(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // Search field
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: TextField(
                            controller: _professionalSearchController,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Search professionals...',
                              prefixIcon: Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide.none,
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade100,
                              contentPadding:
                                  EdgeInsets.symmetric(vertical: 12),
                            ),
                            onChanged: (value) {
                              setState(() {
                                // This will trigger a rebuild with filtered results
                              });
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content - directly show professionals
            Expanded(child: _buildProfessionalsTab()),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalsTab() {
    if (_isLoadingProfessionals) {
      return Center(child: CircularProgressIndicator());
    }

    if (filteredProfessionals.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(Icons.person_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No professionals found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _fetchProfessionals,
              child: Text('Refresh'),
            ),
            SizedBox(height: 10),
          ],
        ),
      );
    }

    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        // iOS-style refresh control
        CupertinoSliverRefreshControl(
          onRefresh: _fetchProfessionals,
          builder: (
            context,
            refreshState,
            pulledExtent,
            refreshTriggerPullDistance,
            refreshIndicatorExtent,
          ) {
            return Center(
              child: CupertinoActivityIndicator(
                radius: 14,
                color: CupertinoColors.systemGrey,
              ),
            );
          },
        ),

        // Header with invitation history button
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(1, 1, 1, 1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [],
            ),
          ),
        ),

        // Content
        SliverPadding(
          padding: const EdgeInsets.all(12),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final professional = filteredProfessionals[index];
              return _buildProfessionalCard(professional);
            }, childCount: filteredProfessionals.length),
          ),
        ),
      ],
    );
  }

  Widget _buildProfessionalCard(Map<String, dynamic> professional) {
    // Extract expertise areas if available
    final List<String> skills = [];
    if (professional['expertise_areas_data'] != null) {
      for (var expertise in professional['expertise_areas_data']) {
        if (expertise['name'] != null) {
          skills.add(expertise['name']);
        }
      }
    }

    // If no expertise areas, add a placeholder
    if (skills.isEmpty) {
      skills.add('No skills specified');
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 11),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade400,
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(1, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: CircleAvatar(
                    radius: 30,
                    backgroundImage: professional['profile_picture'] != null
                        ? NetworkImage(
                            'baseUrl${professional['profile_picture']}',
                          )
                        : NetworkImage(
                            'https://img.icons8.com/ios-filled/50/user.png',
                          ),
                    backgroundColor: Colors.grey[200],
                    onBackgroundImageError: (exception, stackTrace) {
                      // Fallback icon if image fails to load
                      print('Error loading image: $exception');
                    },
                  ),
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        professional['name'] ?? 'Unknown',
                        style: GoogleFonts.inter(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        professional['expertise_areas_data'] != null &&
                                professional['expertise_areas_data'].isNotEmpty
                            ? professional['expertise_areas_data'][0]['name'] ??
                                'No position specified'
                            : 'No position specified',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.location_on, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            professional['place'] ?? 'Location not specified',
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.work, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            '${professional['years_of_experience'] ?? '0'} years experience',
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: skills.map((skill) {
                return Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      skill,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: Colors.grey[800],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 1),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    // Navigate to professional details page and await result
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ProfessionalDetailsScreen(
                          professional: professional,
                        ),
                      ),
                    );

                    // Refresh the professionals list when returning
                    if (mounted) {
                      _fetchProfessionals();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                      //  side: BorderSide(color: Colors.grey),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        'View Profile',
                        style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.black,
                            fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(width: 4),
                      Icon(Icons.arrow_forward_ios_outlined)
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to map API status to UI status

  // Helper method to get status color
}
