import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:job/services/api_service.dart';
import 'package:flutter/cupertino.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:job/uttilits/api_constants.dart';

class InvitationHistoryScreen extends StatefulWidget {
  const InvitationHistoryScreen({Key? key}) : super(key: key);

  @override
  State<InvitationHistoryScreen> createState() =>
      _InvitationHistoryScreenState();
}

class _InvitationHistoryScreenState extends State<InvitationHistoryScreen> {
  List<Map<String, dynamic>> _invitations = [];
  List<Map<String, dynamic>> _joiningRequests = []; // Track joining requests
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Fetch both invitations and joining requests in parallel
      final invitationsFuture = ApiService.getSalonInvitations();
      final joiningRequestsFuture = _fetchJoiningRequests();

      final results = await Future.wait([
        invitationsFuture,
        joiningRequestsFuture,
      ]);

      final invitationsData = results[0];
      final joiningRequestsData = results[1];

      setState(() {
        _joiningRequests = joiningRequestsData;

        // Filter out invitations where a joining request has already been sent
        _invitations = invitationsData.where((invitation) {
          final jobseekerId = invitation['jobseeker'];
          // Check if there's a joining request for this jobseeker
          return !_joiningRequests.any(
            (request) => request['jobseeker'] == jobseekerId,
          );
        }).toList();

        _isLoading = false;
      });
    } catch (e) {
      print('Error fetching data: $e');
      setState(() {
        _errorMessage = 'Failed to load data: $e';
        _isLoading = false;
      });
    }
  }

  Future<List<Map<String, dynamic>>> _fetchJoiningRequests() async {
    try {
      // Get the token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final url = Uri.parse('${ApiConstants.baseUrl}/joining-requests/salon/');

      final response = await http.get(
        url,
        headers: {'Authorization': 'Token $token'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception('Failed to fetch joining requests: ${response.body}');
      }
    } catch (e) {
      print('Error fetching joining requests: $e');
      return []; // Return empty list on error
    }
  }

  Future<void> _sendJoiningRequest(
    int jobseekerId,
    String jobseekerName,
  ) async {
    // Show dialog to get job title and salary
    final result = await _showJoiningRequestDialog(jobseekerName);

    if (result == null) return; // User cancelled

    try {
      // Get the token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final url = Uri.parse('${ApiConstants.baseUrl}/joining-requests/create/');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'jobseeker_id': jobseekerId,
          'job_title': result['jobTitle'],
          'salary': double.parse(result['salary']!),
          'message':
              'We would like to offer you a position at our salon based on your profile.',
        }),
      );

      if (response.statusCode == 201) {
        // Success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Joining request sent successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh data to update the UI
        await _fetchData();
      } else {
        // Error
        throw Exception('Failed to send joining request: ${response.body}');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _deleteInvitation(int invitationId, String jobseekerName) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Invitation'),
        content: Text(
          'Are you sure you want to delete the invitation sent to $jobseekerName?',
          style: GoogleFonts.inter(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return; // User cancelled

    try {
      // Get the token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final url = Uri.parse(
          '${ApiConstants.baseUrl}/salon-invitations/$invitationId/delete/');

      final response = await http.delete(
        url,
        headers: {'Authorization': 'Token $token'},
      );

      if (response.statusCode == 204 || response.statusCode == 200) {
        // Success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invitation deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh data to update the UI
        await _fetchData();
      } else {
        // Error
        throw Exception('Failed to delete invitation: ${response.body}');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
      );
    }
  }

  Future<Map<String, String>?> _showJoiningRequestDialog(
    String jobseekerName,
  ) async {
    final jobTitleController = TextEditingController();
    final salaryController = TextEditingController();

    return showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Send Joining Request'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Send joining request to $jobseekerName',
              style: GoogleFonts.inter(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: jobTitleController,
              decoration: InputDecoration(
                labelText: 'Expertise Area',
                border: OutlineInputBorder(),
                hintText: 'e.g. Hair Stylist',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: salaryController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Salary (₹)',
                border: OutlineInputBorder(),
                hintText: 'e.g. 20000',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (jobTitleController.text.isEmpty ||
                  salaryController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please fill all fields'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context, {
                'jobTitle': jobTitleController.text,
                'salary': salaryController.text,
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: Text('Send Request'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            'Invitation History',
            style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
          ),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, size: 20),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _errorMessage.isNotEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 48, color: Colors.red),
                        SizedBox(height: 16),
                        Text(
                          'Error',
                          style: GoogleFonts.inter(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          _errorMessage,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.inter(color: Colors.red[700]),
                        ),
                        SizedBox(height: 24),
                        ElevatedButton(
                          onPressed: _fetchData,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            foregroundColor: Colors.white,
                          ),
                          child: Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : _invitations.isEmpty
                    ? _buildEmptyState()
                    : _buildInvitationsList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.mail_outline, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No pending invitations',
            style: GoogleFonts.inter(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'You have no invitations without joining requests',
            style: GoogleFonts.inter(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: _fetchData,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Refresh'),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationsList() {
    return RefreshIndicator(
      onRefresh: _fetchData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _invitations.length,
        itemBuilder: (context, index) {
          final invitation = _invitations[index];
          return _buildInvitationCard(invitation);
        },
      ),
    );
  }

  Widget _buildInvitationCard(Map<String, dynamic> invitation) {
    final jobseekerName = invitation['jobseeker_name'] ?? 'Unknown';
    final jobseekerId = invitation['jobseeker'] ?? 0;
    final invitationId = invitation['id'] ?? 0;
    // final message = invitation['message'] ?? 'No message';
    final date = invitation['created_at'] != null
        ? DateTime.parse(invitation['created_at'])
        : null;
    final formattedDate = date != null
        ? '${date.day}/${date.month}/${date.year}'
        : 'Unknown date';

    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.grey[200],
                  child: Text(
                    jobseekerName.isNotEmpty
                        ? jobseekerName[0].toUpperCase()
                        : '?',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        jobseekerName,
                        style: GoogleFonts.inter(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'Sent on $formattedDate',
                        style: GoogleFonts.inter(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                // Delete button
                IconButton(
                  onPressed: () =>
                      _deleteInvitation(invitationId, jobseekerName),
                  icon: Icon(Icons.delete_outline, color: Colors.red),
                  tooltip: 'Delete invitation',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () =>
                      _sendJoiningRequest(jobseekerId, jobseekerName),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text('Send Joining Request',
                      style: GoogleFonts.poppins(
                        color: Colors.black,
                      )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
