import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:job/services/api_service.dart';

class SalonExpensesScreen extends StatefulWidget {
  const SalonExpensesScreen({super.key});

  @override
  State<SalonExpensesScreen> createState() => _SalonExpensesScreenState();
}

class _SalonExpensesScreenState extends State<SalonExpensesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Data for products purchase
  final List<Map<String, dynamic>> productsPurchase = [];

  // Data for operational expenses
  final List<Map<String, dynamic>> operationalExpenses = [];

  // Common controllers
  DateTime _selectedDate = DateTime.now();

  // Loading state

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update FAB and summary
    });
    _fetchOperationalExpenses();
    _fetchProductPurchases();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Calculate totals for current tab
  double get _totalAmount {
    if (_tabController.index == 0) {
      return productsPurchase.fold(
        0,
        (sum, item) => sum + (item['amount'] as double),
      );
    } else {
      return operationalExpenses.fold(
        0,
        (sum, expense) => sum + (expense['totalAmount'] as double),
      );
    }
  }

  double get _totalPaidAmount {
    if (_tabController.index == 0) {
      return productsPurchase.fold(
        0,
        (sum, item) => sum + (item['paidAmount'] as double),
      );
    } else {
      return operationalExpenses.fold(
        0,
        (sum, expense) => sum + (expense['paidAmount'] as double),
      );
    }
  }

  double get _totalBalance {
    if (_tabController.index == 0) {
      return productsPurchase.fold(
        0,
        (sum, item) => sum + (item['balance'] as double),
      );
    } else {
      return operationalExpenses.fold(
        0,
        (sum, expense) => sum + (expense['balance'] as double),
      );
    }
  }

  void _showAddProductDialog() {
    final categoryController = TextEditingController();
    final itemController = TextEditingController();
    final rateController = TextEditingController();
    final quantityController = TextEditingController();
    final paidAmountController = TextEditingController();
    _selectedDate = DateTime.now();

    final _formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Colors.white,
          title: Text(
            'Add Product Purchase',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2030),
                      );
                      if (picked != null && picked != _selectedDate) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        DateFormat('dd/MM/yyyy').format(_selectedDate),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Category field
                  _buildTextField(
                    controller: categoryController,
                    label: 'Category',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a category';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Item field
                  _buildTextField(
                    controller: itemController,
                    label: 'Item',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter an item';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Rate and Quantity in a row
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: rateController,
                          label: 'Rate (₹)',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            if (double.tryParse(value) == null) {
                              return 'Invalid number';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: quantityController,
                          label: 'Quantity',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Required';
                            }
                            if (int.tryParse(value) == null) {
                              return 'Invalid number';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Paid Amount field
                  _buildTextField(
                    controller: paidAmountController,
                    label: 'Paid Amount (₹)',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter paid amount';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.black54),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  final rate = double.parse(rateController.text);
                  final quantity = int.parse(quantityController.text);
                  final amount = rate * quantity;
                  final paidAmount = double.parse(paidAmountController.text);
                  final balance = amount - paidAmount;

                  // Format date as YYYY-MM-DD
                  final formattedDate = '${_selectedDate.year}-'
                      '${_selectedDate.month.toString().padLeft(2, '0')}-'
                      '${_selectedDate.day.toString().padLeft(2, '0')}';

                  try {
                    await ApiService.addProductPurchase(
                      date: formattedDate,
                      category: categoryController.text,
                      item: itemController.text,
                      rate: rate.toString(),
                      quantity: quantity,
                      amount: amount.toString(),
                      paidAmount: paidAmount.toString(),
                      balance: balance.toString(),
                    );

                    // Refresh the list after adding
                    _fetchProductPurchases();

                    Navigator.pop(context);
                    _showSuccessMessage('Product purchase added successfully');
                  } catch (e) {
                    _showErrorMessage('Failed to add product purchase: $e');
                  }
                }
              },
              style: _getButtonStyle(),
              child: Text(
                'Add Purchase',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddExpenseDialog() {
    final categoryController = TextEditingController();
    final descriptionController = TextEditingController();
    final vendorController = TextEditingController();
    final totalAmountController = TextEditingController();
    final paidAmountController = TextEditingController();
    _selectedDate = DateTime.now();

    final _formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Colors.white,
          title: Text(
            'Add Operational Expense',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Date picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2030),
                      );
                      if (picked != null && picked != _selectedDate) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        DateFormat('dd/MM/yyyy').format(_selectedDate),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Expense Category field
                  _buildTextField(
                    controller: categoryController,
                    label: 'Expense Category',
                    hint: 'e.g., Rent, Utilities, Salary',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a category';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Description field
                  _buildTextField(
                    controller: descriptionController,
                    label: 'Description',
                    hint: 'Enter expense details...',
                    maxLines: 2,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Vendor/Payee field
                  _buildTextField(
                    controller: vendorController,
                    label: 'Vendor/Payee',
                    hint: 'e.g., Property Owner, Electricity Board',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter vendor/payee';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Total Amount field
                  _buildTextField(
                    controller: totalAmountController,
                    label: 'Total Amount (₹)',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter total amount';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Paid Amount field
                  _buildTextField(
                    controller: paidAmountController,
                    label: 'Paid Amount (₹)',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter paid amount';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.black54),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  final totalAmount = double.parse(totalAmountController.text);
                  final paidAmount = double.parse(paidAmountController.text);
                  final balance = totalAmount - paidAmount;

                  // Format date as YYYY-MM-DD
                  final formattedDate = '${_selectedDate.year}-'
                      '${_selectedDate.month.toString().padLeft(2, '0')}-'
                      '${_selectedDate.day.toString().padLeft(2, '0')}';

                  try {
                    await ApiService.addOperationalExpense(
                      date: formattedDate,
                      expenseCategory: categoryController.text,
                      description: descriptionController.text,
                      vendorPayee: vendorController.text,
                      totalAmount: totalAmount.toString(),
                      paidAmount: paidAmount.toString(),
                      balance: balance.toString(),
                    );

                    // Refresh the list after adding
                    _fetchOperationalExpenses();

                    Navigator.pop(context);
                    _showSuccessMessage(
                        'Operational expense added successfully');
                  } catch (e) {
                    _showErrorMessage('Failed to add operational expense: $e');
                  }
                }
              },
              style: _getButtonStyle(),
              child: Text(
                'Add Expense',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      validator: validator,
    );
  }

  ButtonStyle _getButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.black,
      foregroundColor: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 12,
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(color: Colors.black, fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Salon Expenses',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          dividerColor: Colors.transparent,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey,
          indicatorWeight: 3,
          labelStyle: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontWeight: FontWeight.normal,
            fontSize: 15,
          ),
          tabs: const [
            Tab(
              text: 'Products Purchase',
            ),
            Tab(text: 'Operational Expenses'),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          if (_tabController.index == 0) {
            _showAddProductDialog();
          } else {
            _showAddExpenseDialog();
          }
        },
        backgroundColor: Colors.black,
        elevation: 4,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      body: Column(
        children: [
          // Summary card
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey.shade200, width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  _tabController.index == 0
                      ? 'Products Purchase Summary'
                      : 'Operational Expenses Summary',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '₹${_totalAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 20),
                const Divider(color: Colors.black, thickness: 1),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildStatItem(
                      'Total Amount',
                      '₹${_totalAmount.toStringAsFixed(2)}',
                    ),
                    _buildStatItem(
                      'Paid Amount',
                      '₹${_totalPaidAmount.toStringAsFixed(2)}',
                    ),
                    _buildStatItem(
                      'Balance',
                      '₹${_totalBalance.toStringAsFixed(2)}',
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildProductsPurchaseTab(),
                _buildOperationalExpensesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsPurchaseTab() {
    return Column(
      children: [
        // Table header
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Purchase Records',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {}); // Refresh the list
                },
                child: const Icon(Icons.refresh, size: 22, color: Colors.black),
              ),
            ],
          ),
        ),

        // Products table
        Expanded(
          child: productsPurchase.isEmpty
              ? _buildEmptyState('No product purchases yet',
                  'Tap the + button to add a purchase')
              : _buildProductsTable(),
        ),
      ],
    );
  }

  Widget _buildOperationalExpensesTab() {
    return Column(
      children: [
        // Table header
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Expense Records',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {}); // Refresh the list
                },
                child: const Icon(Icons.refresh, size: 22, color: Colors.black),
              ),
            ],
          ),
        ),

        // Expenses table
        Expanded(
          child: operationalExpenses.isEmpty
              ? _buildEmptyState('No operational expenses yet',
                  'Tap the + button to add an expense')
              : _buildOperationalExpensesTable(),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.receipt_long, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.poppins(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: DataTable(
          columns: [
            DataColumn(
              label: Text(
                'Date',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Category',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Item',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Rate',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Quantity',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Amount',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Paid Amount',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Balance',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Actions',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
          ],
          rows: productsPurchase.map((product) {
            final date = product['date'] as DateTime;
            final formattedDate = DateFormat('dd/MM/yyyy').format(date);

            return DataRow(
              cells: [
                DataCell(Text(formattedDate)),
                DataCell(Text(product['category'])),
                DataCell(
                  SizedBox(
                    width: 150,
                    child: Text(
                      product['item'],
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(Text('₹${product['rate'].toStringAsFixed(2)}')),
                DataCell(Text(product['quantity'].toString())),
                DataCell(
                  Text(
                    '₹${product['amount'].toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                  ),
                ),
                DataCell(Text('₹${product['paidAmount'].toStringAsFixed(2)}')),
                DataCell(
                  Text(
                    '₹${product['balance'].toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      color: product['balance'] > 0 ? Colors.red : Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: Colors.blue),
                        onPressed: () => _showEditProductDialog(product),
                        tooltip: 'Edit',
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red),
                        onPressed: () =>
                            _showDeleteProductConfirmation(product),
                        tooltip: 'Delete',
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildOperationalExpensesTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: DataTable(
          columns: [
            DataColumn(
              label: Text(
                'Date',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Expense Category',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Description',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Vendor/Payee',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Total Amount',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Paid Amount',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Balance',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            DataColumn(
              label: Text(
                'Actions',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
          ],
          rows: operationalExpenses.map((expense) {
            final date = expense['date'] as DateTime;
            final formattedDate = DateFormat('dd/MM/yyyy').format(date);

            return DataRow(
              cells: [
                DataCell(Text(formattedDate)),
                DataCell(
                  Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Icon(
                          _getCategoryIcon(expense['expenseCategory']),
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(expense['expenseCategory']),
                    ],
                  ),
                ),
                DataCell(
                  SizedBox(
                    width: 200,
                    child: Text(
                      expense['description'],
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  SizedBox(
                    width: 150,
                    child: Text(
                      expense['vendorPayee'],
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    '₹${expense['totalAmount'].toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                  ),
                ),
                DataCell(Text('₹${expense['paidAmount'].toStringAsFixed(2)}')),
                DataCell(
                  Text(
                    '₹${expense['balance'].toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      color: expense['balance'] > 0 ? Colors.red : Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: Colors.blue),
                        onPressed: () => _showEditExpenseDialog(expense),
                        tooltip: 'Edit',
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red),
                        onPressed: () =>
                            _showDeleteExpenseConfirmation(expense),
                        tooltip: 'Delete',
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Utilities':
        return Icons.electric_bolt;
      case 'Rent':
        return Icons.home;
      case 'Supplies':
        return Icons.shopping_bag;
      case 'Equipment':
        return Icons.build;
      case 'Staff Salary':
        return Icons.people;
      case 'Marketing':
        return Icons.campaign;
      case 'Maintenance':
        return Icons.handyman;
      default:
        return Icons.category;
    }
  }

  Future<void> _fetchOperationalExpenses() async {
    setState(() {});

    try {
      final expenses = await ApiService.fetchOperationalExpenses();
      setState(() {
        operationalExpenses.clear();
        for (var expense in expenses) {
          operationalExpenses.add({
            'date': DateTime.parse(expense['date']),
            'expenseCategory': expense['expense_category'],
            'description': expense['description'],
            'vendorPayee': expense['vendor_payee'],
            'paidAmount': double.parse(expense['paid_amount']),
            'totalAmount': double.parse(expense['total_amount']),
            'balance': double.parse(expense['balance']),
            'id': expense['id'].toString(),
          });
        }
      });
    } catch (e) {
      setState(() {
        // Handle error - you might want to show a snackbar or error message
        print('Error fetching operational expenses: $e');
      });
    }
  }

  Future<void> _fetchProductPurchases() async {
    setState(() {});

    try {
      final purchases = await ApiService.fetchProductPurchases();
      setState(() {
        productsPurchase.clear();
        for (var purchase in purchases) {
          productsPurchase.add({
            'date': DateTime.parse(purchase['date']),
            'category': purchase['category'],
            'item': purchase['item'],
            'rate': double.parse(purchase['rate']),
            'quantity': purchase['quantity'],
            'amount': double.parse(purchase['amount']),
            'paidAmount': double.parse(purchase['paid_amount']),
            'balance': double.parse(purchase['balance']),
            'id': purchase['id'].toString(),
          });
        }
      });
    } catch (e) {
      setState(() {
        print('Error fetching product purchases: $e');
      });
    }
  }

  void _showEditProductDialog(Map<String, dynamic> product) {
    final categoryController = TextEditingController(text: product['category']);
    final itemController = TextEditingController(text: product['item']);
    final rateController =
        TextEditingController(text: product['rate'].toString());
    final quantityController =
        TextEditingController(text: product['quantity'].toString());
    final paidAmountController =
        TextEditingController(text: product['paidAmount'].toString());
    _selectedDate = product['date'];

    final _formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Colors.white,
          title: Text(
            'Edit Product Purchase',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2030),
                      );
                      if (picked != null && picked != _selectedDate) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        DateFormat('dd/MM/yyyy').format(_selectedDate),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Category field
                  _buildTextField(
                    controller: categoryController,
                    label: 'Category',
                  ),
                  const SizedBox(height: 16),

                  // Item field
                  _buildTextField(
                    controller: itemController,
                    label: 'Item',
                  ),
                  const SizedBox(height: 16),

                  // Rate and Quantity in a row
                  Row(
                    children: [
                      Expanded(
                        child: _buildTextField(
                          controller: rateController,
                          label: 'Rate (₹)',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildTextField(
                          controller: quantityController,
                          label: 'Quantity',
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Paid Amount field
                  _buildTextField(
                    controller: paidAmountController,
                    label: 'Paid Amount (₹)',
                    keyboardType: TextInputType.number,
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.black54),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  final rate = double.parse(rateController.text);
                  final quantity = int.parse(quantityController.text);
                  final amount = rate * quantity;
                  final paidAmount = double.parse(paidAmountController.text);
                  final balance = amount - paidAmount;

                  // Format date as YYYY-MM-DD
                  final formattedDate = '${_selectedDate.year}-'
                      '${_selectedDate.month.toString().padLeft(2, '0')}-'
                      '${_selectedDate.day.toString().padLeft(2, '0')}';

                  try {
                    await ApiService.updateProductPurchase(
                      id: int.parse(product['id']),
                      date: formattedDate,
                      category: categoryController.text,
                      item: itemController.text,
                      rate: rate,
                      quantity: quantity,
                      amount: amount,
                      paidAmount: paidAmount,
                      balance: balance,
                    );

                    // Refresh the list after updating
                    _fetchProductPurchases();

                    Navigator.pop(context);
                    _showSuccessMessage(
                        'Product purchase updated successfully');
                  } catch (e) {
                    _showErrorMessage('Failed to update product purchase: $e');
                  }
                }
              },
              style: _getButtonStyle(),
              child: Text(
                'Update Purchase',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteProductConfirmation(Map<String, dynamic> product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Product Purchase'),
        content: Text('Are you sure you want to delete this product purchase?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await ApiService.deleteProductPurchase(
                    int.parse(product['id']));

                // Refresh the list after deleting
                _fetchProductPurchases();

                Navigator.pop(context);
                _showSuccessMessage('Product purchase deleted successfully');
              } catch (e) {
                _showErrorMessage('Failed to delete product purchase: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showEditExpenseDialog(Map<String, dynamic> expense) {
    final categoryController =
        TextEditingController(text: expense['expenseCategory']);
    final descriptionController =
        TextEditingController(text: expense['description']);
    final vendorController =
        TextEditingController(text: expense['vendorPayee']);
    final totalAmountController =
        TextEditingController(text: expense['totalAmount'].toString());
    final paidAmountController =
        TextEditingController(text: expense['paidAmount'].toString());
    _selectedDate = expense['date'];

    final _formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          backgroundColor: Colors.white,
          title: Text(
            'Edit Operational Expense',
            style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          ),
          content: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date picker
                  InkWell(
                    onTap: () async {
                      final DateTime? picked = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2030),
                      );
                      if (picked != null && picked != _selectedDate) {
                        setState(() {
                          _selectedDate = picked;
                        });
                      }
                    },
                    child: InputDecorator(
                      decoration: InputDecoration(
                        labelText: 'Date',
                        prefixIcon: const Icon(Icons.calendar_today),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        DateFormat('dd/MM/yyyy').format(_selectedDate),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Category field
                  _buildTextField(
                    controller: categoryController,
                    label: 'Expense Category',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a category';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Description field
                  _buildTextField(
                    controller: descriptionController,
                    label: 'Description',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Vendor/Payee field
                  _buildTextField(
                    controller: vendorController,
                    label: 'Vendor/Payee',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter vendor/payee';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Total Amount field
                  _buildTextField(
                    controller: totalAmountController,
                    label: 'Total Amount (₹)',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter total amount';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Paid Amount field
                  _buildTextField(
                    controller: paidAmountController,
                    label: 'Paid Amount (₹)',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter paid amount';
                      }
                      if (double.tryParse(value) == null) {
                        return 'Invalid number';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(color: Colors.black54),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  final totalAmount = double.parse(totalAmountController.text);
                  final paidAmount = double.parse(paidAmountController.text);
                  final balance = totalAmount - paidAmount;

                  // Format date as YYYY-MM-DD
                  final formattedDate = '${_selectedDate.year}-'
                      '${_selectedDate.month.toString().padLeft(2, '0')}-'
                      '${_selectedDate.day.toString().padLeft(2, '0')}';

                  try {
                    await ApiService.updateOperationalExpense(
                      id: int.parse(expense['id']),
                      date: formattedDate,
                      expenseCategory: categoryController.text,
                      description: descriptionController.text,
                      vendorPayee: vendorController.text,
                      totalAmount: totalAmount,
                      paidAmount: paidAmount,
                      balance: balance,
                    );

                    // Refresh the list after updating
                    _fetchOperationalExpenses();

                    Navigator.pop(context);
                    _showSuccessMessage(
                        'Operational expense updated successfully');
                  } catch (e) {
                    _showErrorMessage(
                        'Failed to update operational expense: $e');
                  }
                }
              },
              style: _getButtonStyle(),
              child: Text(
                'Update Expense',
                style: GoogleFonts.poppins(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteExpenseConfirmation(Map<String, dynamic> expense) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Operational Expense'),
        content:
            Text('Are you sure you want to delete this operational expense?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await ApiService.deleteOperationalExpense(
                    int.parse(expense['id']));

                // Refresh the list after deleting
                _fetchOperationalExpenses();

                Navigator.pop(context);
                _showSuccessMessage('Operational expense deleted successfully');
              } catch (e) {
                _showErrorMessage('Failed to delete operational expense: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
