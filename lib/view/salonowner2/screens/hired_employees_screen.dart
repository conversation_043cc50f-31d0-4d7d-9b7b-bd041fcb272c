import 'package:flutter/material.dart';
import 'package:job/services/api_service.dart';
import 'package:job/view/salonowner2/detils/employee_details_screen.dart';

class HiredEmployeesScreen extends StatefulWidget {
  final List<Map<String, dynamic>>? hiredEmployees;

  const HiredEmployeesScreen({Key? key, this.hiredEmployees}) : super(key: key);

  @override
  State<HiredEmployeesScreen> createState() => _HiredEmployeesScreenState();
}

class _HiredEmployeesScreenState extends State<HiredEmployeesScreen> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _apiHiredEmployees = [];
  bool _isLoading = true;
  String _errorMessage = '';

  // Add a set to track employee IDs from the main employer page
  Set<int> _existingEmployeeIds = {};

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });

    // If hiredEmployees is provided, use it, otherwise fetch from API
    if (widget.hiredEmployees != null) {
      _isLoading = false;
      // Extract IDs from provided employees to avoid duplicates
      _extractExistingEmployeeIds(widget.hiredEmployees!);
    } else {
      // First get existing employees from main page, then fetch hired employees
      _getExistingEmployees().then((_) => _fetchHiredEmployees());
    }
  }

  // Method to get existing employees from the main employer page
  Future<void> _getExistingEmployees() async {
    try {
      final existingEmployees = await ApiService.fetchSalonEmployees();
      setState(() {
        // Extract IDs from existing employees
        _existingEmployeeIds = existingEmployees
            .where((employee) => employee['id'] != null)
            .map<int>((employee) => employee['id'] as int)
            .toSet();
        print('Existing employee IDs: $_existingEmployeeIds');
      });
    } catch (e) {
      print('Error fetching existing employees: $e');
      // Continue with empty set if this fails
    }
  }

  // Helper method to extract IDs from provided employees
  void _extractExistingEmployeeIds(List<Map<String, dynamic>> employees) {
    _existingEmployeeIds =
        employees.map<int>((employee) => employee['id'] as int).toSet();
  }

  Future<void> _fetchHiredEmployees() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final hiredEmployees = await ApiService.fetchHiredEmployees();

      // Create a list to store employees that are not already employed
      List<Map<String, dynamic>> filteredEmployees = [];

      // Filter out employees that are already in the main employer page
      for (var employee in hiredEmployees) {
        final employeeId = employee['id'];
        final isExisting =
            employeeId != null && _existingEmployeeIds.contains(employeeId);

        // Also check if the employee is already employed at your salon
        final isAlreadyEmployed = employee['is_already_employed'] == true;

        if (isExisting) {
          print(
              'Filtering out employee: ${employee['applicant_details']?['name'] ?? 'Unknown'} (ID: $employeeId) - Already in employer page');
          continue;
        }

        if (isAlreadyEmployed) {
          print(
              'Filtering out employee: ${employee['applicant_details']?['name'] ?? 'Unknown'} (ID: $employeeId) - Already employed at salon');
          continue;
        }

        // Add to filtered list
        filteredEmployees.add(employee);
      }

      setState(() {
        _apiHiredEmployees = filteredEmployees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
      print('Error fetching hired employees: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Get the list of employees to display (either from widget prop or API)
  List<Map<String, dynamic>> get allEmployees {
    return widget.hiredEmployees ?? _apiHiredEmployees;
  }

  // Filter employees based on search query
  List<Map<String, dynamic>> get filteredEmployees {
    if (_searchQuery.isEmpty) {
      return allEmployees;
    } else {
      return allEmployees.where((employee) {
        // Get name from appropriate field based on data source
        final name = employee.containsKey('applicant_details')
            ? employee['applicant_details']['name'].toString()
            : employee['name'].toString();

        // Get position from appropriate field based on data source
        final position = employee.containsKey('job_title')
            ? employee['job_title'].toString()
            : employee['position'].toString();

        return name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            position.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }
  }

  // Format employee data for display
  Map<String, dynamic> _formatEmployeeData(Map<String, dynamic> employee) {
    if (employee.containsKey('applicant_details')) {
      // API data format
      final applicantDetails = employee['applicant_details'];
      return {
        'id': employee['id'],
        'jobseeker_id': employee['jobseeker_id'],
        'name': applicantDetails['name'] ?? 'Unknown',
        'position': employee['job_title'] ?? 'Unknown Position',
        'image': applicantDetails['profile_picture'] ??
            'https://img.icons8.com/ios-filled/50/user.png',
        'contact_no': applicantDetails['contact_no'] ?? 'Unknown',
        'email': applicantDetails['email'] ?? 'Unknown',
        'experience': '${applicantDetails['years_of_experience'] ?? 0} years',
        'original_data': employee,
      };
    } else {
      // Local data format (already formatted)
      return employee;
    }
  }

  void _showAddEmployeeDialog(
      BuildContext context, Map<String, dynamic> employee) {
    final TextEditingController jobTitleController =
        TextEditingController(text: employee['position']);
    final TextEditingController salaryController = TextEditingController();
    final TextEditingController messageController = TextEditingController(
        text:
            "We would like to offer you a position at our salon based on your profile.");
    bool isSubmitting = false;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('Add as Official Employee'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Employee: ${employee['name']}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: jobTitleController,
                      decoration: const InputDecoration(
                        labelText: 'Job Title',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: salaryController,
                      decoration: const InputDecoration(
                        labelText: 'Salary (₹)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: messageController,
                      decoration: const InputDecoration(
                        labelText: 'Message',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isSubmitting
                      ? null
                      : () async {
                          // Validate inputs
                          if (jobTitleController.text.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('Please enter a job title')),
                            );
                            return;
                          }
                          if (salaryController.text.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('Please enter a salary')),
                            );
                            return;
                          }

                          // Set loading state
                          setDialogState(() {
                            isSubmitting = true;
                          });

                          try {
                            // Get jobseeker ID from employee data

                            // Create joining request using ApiService

                            // Close dialog
                            Navigator.of(context).pop();

                            // Show success message
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Row(
                                    children: [
                                      const Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          '${employee['name']} added as official employee',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  behavior: SnackBarBehavior.floating,
                                  backgroundColor: Colors.green.shade700,
                                  elevation: 6,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          } catch (e) {
                            // Reset loading state
                            setDialogState(() {
                              isSubmitting = false;
                            });

                            // Show error message
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Failed to add official employee: ${e.toString().replaceAll('Exception: ', '')}',
                                  ),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black,
                    foregroundColor: Colors.white,
                  ),
                  child: isSubmitting
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text('Add Employee'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Selected Employees',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black),
            onPressed: _fetchHiredEmployees,
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search bar
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search employees...',
                    prefixIcon: const Icon(Icons.search, color: Colors.grey),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Employee count
              Text(
                '${filteredEmployees.length} Selected Employees',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),

              // Employee list
              Expanded(
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _errorMessage.isNotEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 48,
                                  color: Colors.red[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Error loading employees',
                                  style: TextStyle(
                                    color: Colors.grey[800],
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  _errorMessage,
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                ElevatedButton(
                                  onPressed: _fetchHiredEmployees,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.black,
                                    foregroundColor: Colors.white,
                                  ),
                                  child: const Text('Retry'),
                                ),
                              ],
                            ),
                          )
                        : filteredEmployees.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.person_off_outlined,
                                      size: 48,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'No selected employees found',
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : ListView.builder(
                                itemCount: filteredEmployees.length,
                                itemBuilder: (context, index) {
                                  final employeeData = _formatEmployeeData(
                                    filteredEmployees[index],
                                  );

                                  // Check if employee is already employed at the salon
                                  final originalData =
                                      employeeData['original_data'];
                                  final isAlreadyEmployed =
                                      originalData != null &&
                                          originalData['is_already_employed'] ==
                                              true;

                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.1),
                                          spreadRadius: 1,
                                          blurRadius: 4,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                                    ),
                                    child: ListTile(
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                      leading: CircleAvatar(
                                        radius: 24,
                                        backgroundImage: NetworkImage(
                                          employeeData['image'],
                                        ),
                                      ),
                                      title: Text(
                                        employeeData['name'],
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 16,
                                        ),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            employeeData['position'],
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: 14,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'Phone: ${employeeData['contact_no'] ?? '+91 ${9876540000 + index}'}',
                                            style: TextStyle(
                                              color: Colors.grey[500],
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                      trailing: !isAlreadyEmployed
                                          ? IconButton(
                                              icon: const Icon(
                                                Icons.person_add,
                                                color: Colors.blue,
                                              ),
                                              onPressed: () =>
                                                  _showAddEmployeeDialog(
                                                      context, employeeData),
                                              tooltip:
                                                  'Add as Official Employee',
                                            )
                                          : null,
                                      isThreeLine: true,
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                EmployeeDetailsScreen(
                                              employee: employeeData,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                              ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
