import 'package:flutter/material.dart';
import 'package:job/services/api_service.dart';

class EditVacancyScreen extends StatefulWidget {
  final Map<String, dynamic> vacancy;

  const EditVacancyScreen({Key? key, required this.vacancy}) : super(key: key);

  @override
  State<EditVacancyScreen> createState() => _EditVacancyScreenState();
}

class _EditVacancyScreenState extends State<EditVacancyScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for text fields
  late TextEditingController _titleController;
  late TextEditingController _locationController;
  late TextEditingController _jobSummaryController;
  late TextEditingController _payMinController;
  late TextEditingController _payMaxController;
  late TextEditingController _scheduleController;
  late TextEditingController _commuteInfoController;
  late TextEditingController _educationController;
  late TextEditingController _experienceController;

  String _jobType = 'FULL_TIME';
  String _workLocation = 'In person';
  bool _isLoading = false;
  bool _isActive = true;
  String _gender = 'anyone'; // lowercase value to match API

  // Lists for dynamic fields
  List<String> _responsibilities = [];
  List<String> _benefits = [];
  List<String> _supplementalPay = [];
  List<String> _languages = ['English', 'Hindi', 'Malayalam'];

  final List<String> _genderOptions = [
    'anyone',
    'male',
    'female'
  ]; // lowercase values to match API

  @override
  void initState() {
    super.initState();

    // Initialize controllers with existing data
    _titleController =
        TextEditingController(text: widget.vacancy['title'] ?? '');
    _locationController =
        TextEditingController(text: widget.vacancy['location'] ?? '');
    _jobSummaryController =
        TextEditingController(text: widget.vacancy['job_summary'] ?? '');
    _payMinController = TextEditingController(
        text: (widget.vacancy['pay_min'] ?? 0).toString());
    _payMaxController = TextEditingController(
        text: (widget.vacancy['pay_max'] ?? 0).toString());
    _scheduleController =
        TextEditingController(text: widget.vacancy['schedule'] ?? '');
    _commuteInfoController =
        TextEditingController(text: widget.vacancy['commute_info'] ?? '');
    _educationController =
        TextEditingController(text: widget.vacancy['education'] ?? '');
    _experienceController =
        TextEditingController(text: widget.vacancy['experience'] ?? '');

    // Initialize dropdown values
    _jobType = widget.vacancy['job_type'] ?? 'FULL_TIME';
    _workLocation = widget.vacancy['work_location'] ?? 'In person';
    _isActive = widget.vacancy['is_active'] ?? true;
    _gender = widget.vacancy['gender'] ?? 'anyone';

    // Initialize dynamic lists
    _responsibilities =
        List<String>.from(widget.vacancy['key_responsibilities'] ?? []);
    if (_responsibilities.isEmpty) _responsibilities = ['', ''];

    _benefits = List<String>.from(widget.vacancy['benefits'] ?? []);
    if (_benefits.isEmpty) _benefits = ['', ''];

    _supplementalPay =
        List<String>.from(widget.vacancy['supplemental_pay'] ?? []);
    if (_supplementalPay.isEmpty) _supplementalPay = ['', ''];

    _languages = List<String>.from(
        widget.vacancy['languages'] ?? ['English', 'Hindi', 'Malayalam']);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _locationController.dispose();
    _jobSummaryController.dispose();
    _payMinController.dispose();
    _payMaxController.dispose();
    _scheduleController.dispose();
    _commuteInfoController.dispose();
    _educationController.dispose();
    _experienceController.dispose();
    super.dispose();
  }

  // Add or remove dynamic fields

  Future<void> _updateVacancy() async {
    if (_formKey.currentState!.validate()) {
      try {
        setState(() {
          _isLoading = true;
        });

        // Call API to update job
        final response = await ApiService.updateJob(
          jobId: widget.vacancy['id'],
          title: _titleController.text,
          location: _locationController.text,
          jobType: _jobType,
          jobSummary: _jobSummaryController.text,
          keyResponsibilities:
              _responsibilities.where((r) => r.isNotEmpty).toList(),
          benefits: _benefits.where((b) => b.isNotEmpty).toList(),
          payMin: double.tryParse(_payMinController.text) ?? 0,
          payMax: double.tryParse(_payMaxController.text) ?? 0,
          schedule: _scheduleController.text,
          supplementalPay: _supplementalPay.where((p) => p.isNotEmpty).toList(),
          commuteInfo: _commuteInfoController.text,
          education: _educationController.text,
          experience: _experienceController.text,
          languages: _languages,
          workLocation: _workLocation,
          isActive: _isActive,
          gender: _gender,
        );

        // Return to previous screen with update action
        Navigator.pop(context, {'action': 'update', 'data': response});
      } catch (e) {
        print('Error updating job: $e');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update vacancy: $e'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Edit Vacancy')),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Expertise Area
                    const Text(
                      'Expertise Area',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _titleController,
                      decoration: InputDecoration(
                        hintText: 'Enter expertise area',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an expertise area';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Location
                    const Text(
                      'Location',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        hintText: 'Enter job location',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a location';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Job Summary
                    const Text(
                      'Job Summary',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _jobSummaryController,
                      decoration: const InputDecoration(
                        hintText: 'Enter job summary',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a job summary';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Experience

                    const SizedBox(height: 16),

                    // Gender
                    const Text(
                      'Gender Preference',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _gender,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      items: _genderOptions.map((String gender) {
                        return DropdownMenuItem<String>(
                          value: gender,
                          child: Text(gender[0].toUpperCase() +
                              gender.substring(
                                  1)), // Capitalize first letter for display
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            _gender = newValue;
                          });
                        }
                      },
                    ),

                    const SizedBox(height: 24),

                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updateVacancy,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text('Update Vacancy'),
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
