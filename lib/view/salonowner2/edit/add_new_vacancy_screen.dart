import 'package:flutter/material.dart';
import 'package:job/services/api_service.dart';

class AddNewVacancyScreen extends StatefulWidget {
  const AddNewVacancyScreen({Key? key}) : super(key: key);

  @override
  State<AddNewVacancyScreen> createState() => _AddNewVacancyScreenState();
}

class _AddNewVacancyScreenState extends State<AddNewVacancyScreen> {
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  final TextEditingController _locationController = TextEditingController();
  final TextEditingController _genderController = TextEditingController();
  final TextEditingController _experienceController = TextEditingController();
  final TextEditingController _jobSummaryController = TextEditingController();

  String _jobType = 'FULL_TIME';
  String _workLocation = 'In person';
  bool _isLoading = false;
  String selectedGender = 'anyone';

  // Specialization related variables
  List<Map<String, dynamic>> _specializations = [];
  String? _selectedSpecializationName;
  bool _isLoadingSpecializations = false;

  // Lists for dynamic fields
  List<String> _responsibilities = ['', ''];
  List<String> _benefits = ['', ''];
  List<String> _supplementalPay = ['', ''];
  List<String> _languages = ['English', 'Hindi', 'Malayalam'];

  @override
  void initState() {
    super.initState();
    _fetchSpecializations();
  }

  // Fetch specializations from API
  Future<void> _fetchSpecializations() async {
    setState(() {
      _isLoadingSpecializations = true;
    });

    try {
      final specializationsData = await ApiService.fetchSpecializations();
      setState(() {
        _specializations = specializationsData;
        _isLoadingSpecializations = false;
      });
    } catch (e) {
      print('Error fetching specializations: $e');
      setState(() {
        _isLoadingSpecializations = false;
      });
    }
  }

  Future<void> _submitVacancy() async {
    if (_formKey.currentState!.validate()) {
      try {
        setState(() {
          _isLoading = true;
        });

        // Convert gender to the format expected by the API
        String genderCode;
        switch (selectedGender) {
          case 'male':
            genderCode = 'male';
            break;
          case 'female':
            genderCode = 'female';
            break;
          case 'anyone':
            genderCode = 'anyone';
            break;
          default:
            genderCode = 'anyone';
        }

        // Prepare data to send to API
        final Map<String, dynamic> vacancyData = {
          "title": _selectedSpecializationName,
          "location": _locationController.text,
          "job_type": _jobType,
          "job_summary": _jobSummaryController.text,
          "key_responsibilities":
              _responsibilities.where((r) => r.isNotEmpty).toList(),
          "benefits": _benefits.where((b) => b.isNotEmpty).toList(),
          "supplemental_pay":
              _supplementalPay.where((p) => p.isNotEmpty).toList(),
          "experience": _experienceController.text,
          "languages": _languages,
          "work_location": _workLocation,
          "gender": genderCode, // Use the converted gender code
        };

        print('Submitting vacancy data: $vacancyData');

        // Call API to create job
        final response = await ApiService.createJob(
          title: _selectedSpecializationName ?? '',
          location: _locationController.text,
          jobType: _jobType,
          jobSummary: _jobSummaryController.text,
          experience: _experienceController.text,
          benefits: [],
          commuteInfo: '',
          gender: genderCode, // Use the converted gender code
        );

        print('Job created successfully: ${response['id']}');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle_outline, color: Colors.white),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Vacancy posted successfully!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green.shade700,
            elevation: 6,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 3),
            margin: const EdgeInsets.only(top: 50, left: 10, right: 10),
          ),
        );

        // Return to previous screen with add action
        Navigator.pop(context, {'action': 'add'});
      } catch (e) {
        print('Error creating job: $e');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to post vacancy: $e'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Add New Vacancy')),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Job Title
                    const Text(
                      'Job Title',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Specialization Dropdown
                    const Text(
                      'Specialization',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _isLoadingSpecializations
                        ? Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Loading specializations...',
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                              ],
                            ),
                          )
                        : DropdownButtonFormField<String>(
                            value: _selectedSpecializationName,
                            decoration: InputDecoration(
                              hintText: 'Select a specialization',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 16,
                              ),
                            ),
                            items: _specializations.map((specialization) {
                              return DropdownMenuItem<String>(
                                value: specialization['name'],
                                child: Text(specialization['name']),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedSpecializationName = value;
                                // Find the corresponding ID
                                _specializations.firstWhere(
                                  (spec) => spec['name'] == value,
                                  orElse: () => {},
                                );
                              });
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a specialization';
                              }
                              return null;
                            },
                          ),
                    const SizedBox(height: 16),
                    //gender
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Previous widgets...

                        const SizedBox(height: 16),

                        // Gender label
                        const Text(
                          'Gender',
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),

                        // Gender selection
                        Column(
                          children: [
                            RadioListTile<String>(
                              title: const Text('Male'),
                              value: 'male',
                              groupValue: selectedGender,
                              onChanged: (value) {
                                setState(() {
                                  selectedGender = value!;
                                });
                              },
                            ),
                            RadioListTile<String>(
                              title: const Text(
                                'Female',
                              ),
                              value: 'female',
                              groupValue: selectedGender,
                              onChanged: (value) {
                                setState(() {
                                  selectedGender = value!;
                                });
                              },
                            ),
                            RadioListTile<String>(
                              title: const Text('Anyone'),
                              value: 'anyone',
                              groupValue: selectedGender,
                              onChanged: (value) {
                                setState(() {
                                  selectedGender = value!;
                                });
                              },
                            ),
                          ],
                        ),
                      ],
                    ),

                    // Location
                    const Text(
                      'Location',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _locationController,
                      decoration: InputDecoration(
                        hintText: 'e.g. Thoppumpady, Kochi',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a location';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Job Summary
                    const Text(
                      'Job Summary',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _jobSummaryController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: 'Briefly describe the job...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a job summary';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // Submit Button
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _submitVacancy,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? const CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 3,
                              )
                            : const Text(
                                'Post Vacancy',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _locationController.dispose();
    _genderController.dispose();
    _jobSummaryController.dispose();
    _experienceController.dispose();

    super.dispose();
  }
}

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return split(' ')
        .map(
          (word) => word.isNotEmpty
              ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
              : '',
        )
        .join(' ');
  }
}
