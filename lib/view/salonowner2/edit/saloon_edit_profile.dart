import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:job/services/api_service.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class SaloonEditProfile extends StatefulWidget {
  const SaloonEditProfile({super.key});

  @override
  State<SaloonEditProfile> createState() => _SaloonEditProfileState();
}

class _SaloonEditProfileState extends State<SaloonEditProfile> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  // Controllers for form fields
  final _salonNameController = TextEditingController();
  final _locationController = TextEditingController();
  final _pinCodeController = TextEditingController();
  final _googleMapLinkController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _contactController = TextEditingController();
  final _aboutController = TextEditingController();

  // Salon type dropdown
  String _selectedSalonType = 'MALE';

  bool _isLoading = false;

  // Image related variables
  File? _selectedProfileImage;
  List<File> _selectedSalonImages = [];
  String? _currentProfilePicture;
  List<Map<String, dynamic>> _currentSalonImages = [];

  // Salon type options
  final List<Map<String, String>> _salonTypeOptions = [
    {'value': 'MALE', 'label': 'Male Salon'},
    {'value': 'FEMALE', 'label': 'Female Salon'},
    {'value': 'UNISEX', 'label': 'Unisex Salon'},
  ];

  @override
  void initState() {
    super.initState();
    _loadSalonProfile();
  }

  Future<void> _loadSalonProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final profileData = await ApiService.fetchSalonProfile();

      setState(() {
        _salonNameController.text = profileData['salon_name'] ?? '';
        _locationController.text = profileData['location'] ?? '';
        _pinCodeController.text = profileData['pin_code'] ?? '';
        _googleMapLinkController.text = profileData['google_map_link'] ?? '';
        _ownerNameController.text = profileData['owner_name'] ?? '';
        _contactController.text = profileData['contact_no'] ?? '';
        _aboutController.text = profileData['about'] ?? '';
        _selectedSalonType = profileData['salon_type'] ?? 'MALE';
        _currentProfilePicture = profileData['profile_picture'];
        _currentSalonImages = List<Map<String, dynamic>>.from(
          profileData['salon_images'] ?? [],
        );
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load profile: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _salonNameController.dispose();
    _locationController.dispose();
    _pinCodeController.dispose();
    _googleMapLinkController.dispose();
    _ownerNameController.dispose();
    _contactController.dispose();
    _aboutController.dispose();
    super.dispose();
  }

  Future<void> _pickProfileImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        final file = File(image.path);
        final fileSize = await file.length();
        print('Profile image file: ${file.path}, size: ${fileSize} bytes');

        // Check if file size is reasonable (less than 10MB)
        if (fileSize > 10 * 1024 * 1024) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Image file too large. Please select an image smaller than 10MB.'),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        setState(() {
          _selectedProfileImage = file;
        });

        print('Selected profile image successfully');
      }
    } catch (e) {
      print('Error picking profile image: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking image: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _pickSalonImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        final List<File> validImages = [];
        for (final xFile in images) {
          final file = File(xFile.path);
          final fileSize = await file.length();
          print('Image file: ${file.path}, size: ${fileSize} bytes');

          // Check if file size is reasonable (less than 10MB)
          if (fileSize > 10 * 1024 * 1024) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'Image file too large. Please select images smaller than 10MB.'),
                backgroundColor: Colors.orange,
              ),
            );
            continue;
          }

          validImages.add(file);
        }

        setState(() {
          _selectedSalonImages.addAll(validImages);
        });

        print('Selected ${validImages.length} valid salon images');
      }
    } catch (e) {
      print('Error picking salon images: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error picking images: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _removeSelectedSalonImage(int index) async {
    setState(() {
      _selectedSalonImages.removeAt(index);
    });
  }

  Future<void> _removeCurrentSalonImage(int imageId) async {
    try {
      setState(() {
        _isLoading = true;
      });

      await ApiService.deleteSalonImage(imageId);

      // Remove from current images list
      setState(() {
        _currentSalonImages.removeWhere((image) => image['id'] == imageId);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Image removed successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to remove image: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _saveProfile() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        print('Starting profile update process...');

        // First update the basic profile information
        print('Updating basic profile information...');
        print('Basic profile updated successfully');

        // Upload images if any are selected
        if (_selectedProfileImage != null || _selectedSalonImages.isNotEmpty) {
          print('Uploading images...');
          await ApiService.uploadSalonProfileWithImages(
            profilePicture: _selectedProfileImage,
            salonImages:
                _selectedSalonImages.isNotEmpty ? _selectedSalonImages : null,
          );
          print('Images uploaded successfully');
        }

        // Reload profile to get updated data
        print('Reloading profile data...');
        await _loadSalonProfile();
        print('Profile reloaded successfully');

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully'),
            backgroundColor: Colors.black,
            duration: Duration(seconds: 2),
          ),
        );

        // Wait for the snackbar to show before navigating back
        Future.delayed(const Duration(seconds: 2), () {
          Navigator.pop(context, true);
        });
      } catch (e) {
        print('Error in _saveProfile: $e');
        String errorMsg = e.toString();
        if (errorMsg.contains('contact_no') &&
            errorMsg.contains('already exists')) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'This contact number is already registered with another salon'),
              backgroundColor: Colors.red,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update profile: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Edit Profile',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.black),
            )
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Profile Image Section
                      Center(
                        child: Column(
                          children: [
                            GestureDetector(
                              onTap: _pickProfileImage,
                              child: Container(
                                width: 100,
                                height: 100,
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                      color: Colors.grey.shade200, width: 2),
                                ),
                                child: _selectedProfileImage != null
                                    ? ClipOval(
                                        child: Image.file(
                                          _selectedProfileImage!,
                                          width: 100,
                                          height: 100,
                                          fit: BoxFit.cover,
                                        ),
                                      )
                                    : _currentProfilePicture != null
                                        ? ClipOval(
                                            child: Image.network(
                                              _currentProfilePicture!,
                                              width: 100,
                                              height: 100,
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return const Icon(
                                                  Icons.business,
                                                  size: 40,
                                                  color: Colors.grey,
                                                );
                                              },
                                            ),
                                          )
                                        : const Icon(
                                            Icons.business,
                                            size: 40,
                                            color: Colors.grey,
                                          ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: _pickProfileImage,
                              child: const Text(
                                'Change Profile Picture',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Salon Profile',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),

                      // Salon Information Section
                      _buildSectionHeader('Salon Information'),
                      const SizedBox(height: 20),

                      // Salon Name
                      _buildModernTextField(
                        controller: _salonNameController,
                        label: 'Salon Name',
                        hint: 'Enter your salon name',
                        icon: Icons.business,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter salon name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Salon Type
                      _buildModernDropdown(
                        label: 'Salon Type',
                        hint: 'Select salon type',
                        icon: Icons.category,
                        value: _selectedSalonType,
                        items: _salonTypeOptions.map((option) {
                          return DropdownMenuItem<String>(
                            value: option['value'],
                            child: Text(option['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedSalonType = value!;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select salon type';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Location
                      _buildModernTextField(
                        controller: _locationController,
                        label: 'Location',
                        hint: 'Enter salon location',
                        icon: Icons.location_on,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter location';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Pin Code
                      _buildModernTextField(
                        controller: _pinCodeController,
                        label: 'Pin Code',
                        hint: 'Enter 6-digit pin code',
                        icon: Icons.pin_drop,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(6),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter pin code';
                          }
                          if (value.length != 6) {
                            return 'Pin code must be 6 digits';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Google Map Link
                      _buildModernTextField(
                        controller: _googleMapLinkController,
                        label: 'Google Map Link',
                        hint: 'Enter Google Maps link',
                        icon: Icons.map,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter Google Map link';
                          }

                          final validFormats = [
                            'maps.google.com',
                            'goo.gl/maps',
                            'maps.app.goo.gl',
                            'g.co/kgs',
                            'google.com/maps',
                            'maps.app',
                          ];

                          bool isValidLink = false;
                          for (final format in validFormats) {
                            if (value.contains(format)) {
                              isValidLink = true;
                              break;
                            }
                          }

                          if (!isValidLink) {
                            return 'Please enter a valid Google Maps link';
                          }

                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // About
                      _buildModernTextField(
                        controller: _aboutController,
                        label: 'About Salon',
                        hint: 'Tell us about your salon (optional)',
                        icon: Icons.info,
                        maxLines: 3,
                        validator: (value) {
                          return null; // Optional field
                        },
                      ),
                      const SizedBox(height: 32),

                      // Salon Images Section
                      _buildSectionHeader('Salon Images'),
                      const SizedBox(height: 20),

                      // Current Salon Images
                      if (_currentSalonImages.isNotEmpty) ...[
                        Text(
                          'Current Images',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          height: 120,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _currentSalonImages.length,
                            itemBuilder: (context, index) {
                              final image = _currentSalonImages[index];
                              return Container(
                                margin: const EdgeInsets.only(right: 12),
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        image['image'],
                                        width: 120,
                                        height: 120,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) {
                                          return Container(
                                            width: 120,
                                            height: 120,
                                            color: Colors.grey.shade200,
                                            child: const Icon(
                                              Icons.image,
                                              color: Colors.grey,
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                    Positioned(
                                      top: 4,
                                      right: 4,
                                      child: GestureDetector(
                                        onTap: () => _removeCurrentSalonImage(
                                            image['id']),
                                        child: Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            color: Colors.white,
                                            size: 16,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],

                      // Selected Salon Images
                      if (_selectedSalonImages.isNotEmpty) ...[
                        Text(
                          'New Images to Upload',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          height: 120,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _selectedSalonImages.length,
                            itemBuilder: (context, index) {
                              return Container(
                                margin: const EdgeInsets.only(right: 12),
                                child: Stack(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.file(
                                        _selectedSalonImages[index],
                                        width: 120,
                                        height: 120,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Positioned(
                                      top: 4,
                                      right: 4,
                                      child: GestureDetector(
                                        onTap: () =>
                                            _removeSelectedSalonImage(index),
                                        child: Container(
                                          padding: const EdgeInsets.all(4),
                                          decoration: const BoxDecoration(
                                            color: Colors.red,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.close,
                                            color: Colors.white,
                                            size: 16,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],

                      // Add Images Button
                      Container(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _pickSalonImages,
                          icon: const Icon(Icons.add_photo_alternate),
                          label: const Text('Add Salon Images'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey.shade100,
                            foregroundColor: Colors.black,
                            elevation: 0,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 32),

                      // Owner Information Section
                      _buildSectionHeader('Owner Information'),
                      const SizedBox(height: 20),

                      // Owner's Name
                      _buildModernTextField(
                        controller: _ownerNameController,
                        label: 'Owner\'s Name',
                        hint: 'Enter owner\'s full name',
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter owner\'s name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Contact Number
                      _buildModernTextField(
                        controller: _contactController,
                        label: 'Contact Number',
                        hint: 'Enter 10-digit mobile number',
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(10),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter contact number';
                          }
                          if (value.length != 10) {
                            return 'Contact number must be 10 digits';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 40),

                      // Debug section (remove in production)
                      if (true) ...[
                        _buildSectionHeader('Debug Information'),
                        const SizedBox(height: 20),
                        Container(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () async {
                              try {
                                print('Testing API endpoints...');
                                final profile =
                                    await ApiService.fetchSalonProfile();
                                print(
                                    'Current profile: ${profile['salon_name']}');
                                print(
                                    'Profile picture: ${profile['profile_picture']}');
                                print(
                                    'Salon images count: ${(profile['salon_images'] as List?)?.length ?? 0}');

                                // Debug salon images structure
                                if (profile['salon_images'] != null) {
                                  final images =
                                      profile['salon_images'] as List;
                                  print('Salon images structure:');
                                  for (int i = 0; i < images.length; i++) {
                                    print('Image $i: ${images[i]}');
                                    if (images[i] is Map) {
                                      print('  - ID: ${images[i]['id']}');
                                      print(
                                          '  - Image URL: ${images[i]['image']}');
                                    }
                                  }
                                }

                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content:
                                        Text('Debug info printed to console'),
                                    backgroundColor: Colors.blue,
                                  ),
                                );
                              } catch (e) {
                                print('Debug error: $e');
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Debug error: $e'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            child: const Text('Test API Connection'),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 40,
          height: 2,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(1),
          ),
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 14,
            ),
            prefixIcon: Icon(icon, color: Colors.grey.shade500, size: 20),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade200),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade200),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.black, width: 1.5),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red.shade300, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red.shade400, width: 1.5),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 16,
            ),
            errorStyle: TextStyle(
              color: Colors.red.shade600,
              fontSize: 12,
            ),
          ),
          validator: validator,
        ),
      ],
    );
  }

  Widget _buildModernDropdown({
    required String label,
    required String hint,
    required IconData icon,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          items: items,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 14,
            ),
            prefixIcon: Icon(icon, color: Colors.grey.shade500, size: 20),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade200),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade200),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.black, width: 1.5),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red.shade300, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red.shade400, width: 1.5),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            contentPadding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 16,
            ),
            errorStyle: TextStyle(
              color: Colors.red.shade600,
              fontSize: 12,
            ),
          ),
          validator: validator,
          icon: Icon(Icons.keyboard_arrow_down, color: Colors.grey.shade500),
          dropdownColor: Colors.white,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
