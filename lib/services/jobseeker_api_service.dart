import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:job/uttilits/jobseeker_api_constants.dart';

class JobseekerApiService {
  // Get auth token from shared preferences
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }

  // Login
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(JobseekerApiConstants.login),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Login failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in login: $e');
      throw Exception('Error during login: $e');
    }
  }

  // Register
  static Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    String? gender,
    String? dateOfBirth,
    String? place,
    List<int>? expertiseAreas,
    String? yearsOfExperience,
    String? socialLink,
  }) async {
    try {
      final Map<String, dynamic> requestBody = {
        'email': email,
        'password': password,
        'password2': password, // Confirm password field
        'name': name,
        'contact_no': phoneNumber,
      };

      // Add optional fields if provided
      if (gender != null) requestBody['gender'] = gender;
      if (dateOfBirth != null) requestBody['date_of_birth'] = dateOfBirth;
      if (place != null) requestBody['place'] = place;
      if (expertiseAreas != null) requestBody['expertise_areas'] = expertiseAreas;
      if (yearsOfExperience != null) requestBody['years_of_experience'] = yearsOfExperience;
      if (socialLink != null) requestBody['social_link'] = socialLink;

      // Empty arrays for files that will be uploaded separately
      requestBody['work_pictures'] = [];

      final response = await http.post(
        Uri.parse(JobseekerApiConstants.register),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Registration failed: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in register: $e');
      throw Exception('Error during registration: $e');
    }
  }

  // Fetch profile
  static Future<Map<String, dynamic>> fetchProfile() async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse(JobseekerApiConstants.profile),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to load profile: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchProfile: $e');
      throw Exception('Error fetching profile: $e');
    }
  }

  // Update profile
  static Future<Map<String, dynamic>> updateProfile({
    String? name,
    String? dateOfBirth,
    String? gender,
    List<int>? expertiseAreas,
    String? yearsOfExperience,
    String? contactNo,
    String? socialLink,
    String? place,
    String? about,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final Map<String, dynamic> requestBody = {};
      
      // Add fields only if they are provided
      if (name != null) requestBody['name'] = name;
      if (dateOfBirth != null) requestBody['date_of_birth'] = dateOfBirth;
      if (gender != null) requestBody['gender'] = gender;
      if (expertiseAreas != null) requestBody['expertise_areas'] = expertiseAreas;
      if (yearsOfExperience != null) requestBody['years_of_experience'] = yearsOfExperience;
      if (contactNo != null) requestBody['contact_no'] = contactNo;
      if (socialLink != null) requestBody['social_link'] = socialLink;
      if (place != null) requestBody['place'] = place;
      if (about != null) requestBody['about'] = about;

      final response = await http.patch(
        Uri.parse(JobseekerApiConstants.updateProfile),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update profile: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateProfile: $e');
      throw Exception('Error updating profile: $e');
    }
  }

  // Upload profile picture
  static Future<Map<String, dynamic>> uploadProfilePicture(File imageFile) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      var request = http.MultipartRequest(
        'PATCH',
        Uri.parse(JobseekerApiConstants.updateProfile),
      );

      request.headers['Authorization'] = 'Token $token';
      request.files.add(
        await http.MultipartFile.fromPath(
          'profile_picture',
          imageFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload profile picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadProfilePicture: $e');
      throw Exception('Error uploading profile picture: $e');
    }
  }

  // Upload work picture
  static Future<Map<String, dynamic>> uploadWorkPicture(File imageFile) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      var request = http.MultipartRequest(
        'POST',
        Uri.parse(JobseekerApiConstants.uploadWorkPicture),
      );

      request.headers['Authorization'] = 'Token $token';
      request.files.add(
        await http.MultipartFile.fromPath(
          'image',
          imageFile.path,
        ),
      );

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload work picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadWorkPicture: $e');
      throw Exception('Error uploading work picture: $e');
    }
  }

  // Fetch jobs
  static Future<List<Map<String, dynamic>>> fetchJobs() async {
    try {
      final response = await http.get(
        Uri.parse(JobseekerApiConstants.jobs),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load jobs: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchJobs: $e');
      throw Exception('Error fetching jobs: $e');
    }
  }

  // Fetch job details
  static Future<Map<String, dynamic>> fetchJobDetails(int jobId) async {
    try {
      final response = await http.get(
        Uri.parse('${JobseekerApiConstants.jobDetails}$jobId/'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to load job details: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchJobDetails: $e');
      throw Exception('Error fetching job details: $e');
    }
  }

  // Apply for job
  static Future<Map<String, dynamic>> applyForJob(int jobId) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse(JobseekerApiConstants.applications),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'job': jobId}),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to apply for job: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in applyForJob: $e');
      throw Exception('Error applying for job: $e');
    }
  }

  // Fetch salons
  static Future<List<Map<String, dynamic>>> fetchSalons() async {
    try {
      // Get token if available (not required but can provide more data if authenticated)
      String? token;
      try {
        token = await _getToken();
      } catch (e) {
        print('Error getting token: $e');
      }

      // Prepare headers with or without token
      final Map<String, String> headers = {
        'Content-Type': 'application/json',
      };

      // Add token if available
      if (token != null) {
        headers['Authorization'] = 'Token $token';
      }

      final response = await http.get(
        Uri.parse(JobseekerApiConstants.salons),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load salons: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchSalons: $e');
      throw Exception('Error fetching salons: $e');
    }
  }

  // Send job request to salon
  static Future<Map<String, dynamic>> sendJobRequest({
    required int salonId,
    String message = 'I am interested in working at your salon.',
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse(JobseekerApiConstants.salonRequests),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'salon_id': salonId,
          'message': message,
        }),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to send job request: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in sendJobRequest: $e');
      throw Exception('Error sending job request: $e');
    }
  }

  // Get applications
  static Future<List<Map<String, dynamic>>> getApplications() async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse(JobseekerApiConstants.applications),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load applications: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in getApplications: $e');
      throw Exception('Error fetching applications: $e');
    }
  }

  // Get salon requests list
  static Future<List<Map<String, dynamic>>> getSalonRequestsList() async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse(JobseekerApiConstants.salonRequestsList),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load salon requests: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in getSalonRequestsList: $e');
      throw Exception('Error fetching salon requests: $e');
    }
  }

  // Get joining requests
  static Future<List<Map<String, dynamic>>> getJoiningRequests() async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse(JobseekerApiConstants.joiningRequests),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load joining requests: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in getJoiningRequests: $e');
      throw Exception('Error fetching joining requests: $e');
    }
  }

  // Respond to salon request
  static Future<Map<String, dynamic>> respondToSalonRequest({
    required int requestId,
    required bool accept,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse('${JobseekerApiConstants.salonRequests}$requestId/respond/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'accept': accept}),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to respond to request: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in respondToSalonRequest: $e');
      throw Exception('Error responding to salon request: $e');
    }
  }

  // Get specializations
  static Future<List<Map<String, dynamic>>> getSpecializations() async {
    try {
      final response = await http.get(
        Uri.parse(JobseekerApiConstants.specializations),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load specializations: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in getSpecializations: $e');
      throw Exception('Error fetching specializations: $e');
    }
  }
}