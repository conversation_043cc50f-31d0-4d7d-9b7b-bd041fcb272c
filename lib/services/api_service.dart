import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:job/uttilits/api_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  // Salon Owner Registration
  static Future<Map<String, dynamic>> registerSalonOwner({
    required String email,
    required String password,
    required String ownerName,
    required String salonName,
    required String salonAddress,
    required String pinCode,
    required String googleMapLink,
    required String phoneNumber,
    required String salonType,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.adminRegister),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
          'password2': password, // Confirm password field
          'salon_name': salonName,
          'location': salonAddress, // Changed from salon_address to location
          'pin_code': pinCode,
          'google_map_link': googleMapLink,
          'owner_name': ownerName, // Changed from ownerName to owner_name
          'contact_no': phoneNumber, // Changed from phone_number to contact_no
          'salon_type': salonType, // Add salon_type to request body
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to register: ${response.body}');
      }
    } catch (e) {
      throw Exception('Registration error: $e');
    }
  }

  // Jobseeker Registration with extended fields
  static Future<Map<String, dynamic>> registerJobseeker({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    String? gender,
    String? dateOfBirth,
    String? place,
    List<int>? expertiseAreas,
    String? yearsOfExperience,
    String? socialLink,
    // Note: profile_picture and resume would be handled separately as file uploads
  }) async {
    try {
      // Build request body with required fields
      final Map<String, dynamic> requestBody = {
        'email': email,
        'password': password,
        'password2': password, // Confirm password field
        'name': name,
        'contact_no': phoneNumber,
      };

      // Add optional fields if provided
      if (gender != null) requestBody['gender'] = gender;
      if (dateOfBirth != null) requestBody['date_of_birth'] = dateOfBirth;
      if (place != null) requestBody['place'] = place;
      if (expertiseAreas != null)
        requestBody['expertise_areas'] = expertiseAreas;
      if (yearsOfExperience != null)
        requestBody['years_of_experience'] = yearsOfExperience;
      if (socialLink != null) requestBody['social_link'] = socialLink;

      // Empty arrays for files that will be uploaded separately
      requestBody['work_pictures'] = [];

      print('Registering jobseeker with data: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse(ApiConstants.jobseekerRegister),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      print('Registration response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to register: ${response.body}');
      }
    } catch (e) {
      print('Registration error: $e');
      throw Exception('Registration error: $e');
    }
  }

  // User Login
  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(ApiConstants.adminLogin),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'username': email, // API expects 'username' field for email
          'password': password,
        }),
      );

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return responseData;
      } else {
        // If there's an error field in the response, throw that as the exception
        if (responseData.containsKey('error')) {
          throw Exception(responseData['error']);
        } else {
          throw Exception('Login failed: ${response.body}');
        }
      }
    } catch (e) {
      throw Exception('Login error: $e');
    }
  }

  // Fetch Training Blogs
  static Future<List<Map<String, dynamic>>> fetchTrainingBlogs() async {
    try {
      print('Fetching training blogs from: ${ApiConstants.trainingBlogs}');

      final response = await http.get(
        Uri.parse(ApiConstants.trainingBlogs),
        headers: {'Content-Type': 'application/json'},
      );

      print('Training blogs response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load training blogs: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchTrainingBlogs: $e');
      throw Exception('Error fetching training blogs: $e');
    }
  }

  // Fetch Training Blog Detail
  static Future<Map<String, dynamic>> fetchTrainingBlogDetail(int id) async {
    try {
      final url = "${ApiConstants.trainingBlogDetail}$id/";
      print('Fetching training blog detail from: $url');
      print('Checking for image data in the response...');

      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      print('Training blog detail response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to load training blog detail: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchTrainingBlogDetail: $e');
      throw Exception('Error fetching training blog detail: $e');
    }
  }

  // Register for Training
  static Future<Map<String, dynamic>> registerForTraining({
    required int trainingId,
    required String ownerName,
    required String salonName,
    required String phoneNo,
    required String pinCode,
    String? location,
  }) async {
    try {
      print('Registering for training with ID: $trainingId');

      final response = await http.post(
        Uri.parse(ApiConstants.trainingRegister),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'training': trainingId,
          'owner_name': ownerName,
          'salon_name': salonName,
          'phone_no': phoneNo,
          'pin_code': pinCode,
          'location': location ?? 'Not specified', // Add location field
        }),
      );

      print('Training registration response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to register for training: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in registerForTraining: $e');
      throw Exception('Error registering for training: $e');
    }
  }

  // Fetch Consultations
  static Future<List<Map<String, dynamic>>> fetchConsultations() async {
    try {
      print('Fetching consultations from: ${ApiConstants.consultations}');

      final response = await http.get(
        Uri.parse(ApiConstants.consultations),
        headers: {'Content-Type': 'application/json'},
      );

      print('Consultations response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load consultations: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchConsultations: $e');
      throw Exception('Error fetching consultations: $e');
    }
  }

  // Fetch Consultation Detail
  static Future<Map<String, dynamic>> fetchConsultationDetail(int id) async {
    try {
      final url = "${ApiConstants.consultationDetail}$id/";
      print('Fetching consultation detail from: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      print('Consultation detail response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to load consultation detail: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchConsultationDetail: $e');
      throw Exception('Error fetching consultation detail: $e');
    }
  }

  // Fetch Products
  static Future<List<Map<String, dynamic>>> fetchProducts() async {
    try {
      print('Fetching products from: ${ApiConstants.products}');

      final response = await http.get(
        Uri.parse(ApiConstants.products),
        headers: {'Content-Type': 'application/json'},
      );

      print('Products response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load products: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchProducts: $e');
      throw Exception('Error fetching products: $e');
    }
  }

  // Fetch Product Detail
  static Future<Map<String, dynamic>> fetchProductDetail(int id) async {
    try {
      final url = "${ApiConstants.productDetail}$id/";
      print('Fetching product detail from: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      print('Product detail response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to load product detail: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchProductDetail: $e');
      throw Exception('Error fetching product detail: $e');
    }
  }

  // Fetch Trending Posts
  static Future<List<Map<String, dynamic>>> fetchTrendingPosts() async {
    try {
      print('Fetching trending posts from: ${ApiConstants.trendingPosts}');

      final response = await http.get(
        Uri.parse(ApiConstants.trendingPosts),
        headers: {'Content-Type': 'application/json'},
      );

      print('Trending posts response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load trending posts: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchTrendingPosts: $e');
      throw Exception('Error fetching trending posts: $e');
    }
  }

  // Fetch Salon Profile
  static Future<Map<String, dynamic>> fetchSalonProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      final response = await http.get(
        Uri.parse(ApiConstants.salonProfile),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
          'Failed to load salon profile: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchSalonProfile: $e');
      throw Exception('Error fetching salon profile: $e');
    }
  }

  // Update Salon Profile
  static Future<Map<String, dynamic>> updateSalonProfile({
    required String salonName,
    required String ownerName,
    required String location,
    required String pinCode,
    required String googleMapLink,
    required String contactNo,
    String? salonType,
    String? about,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Build request body with required fields
      final Map<String, dynamic> requestBody = {
        'salon_name': salonName,
        'owner_name': ownerName,
        'location': location,
        'pin_code': pinCode,
        'google_map_link': googleMapLink,
        'contact_no': contactNo,
      };

      // Add optional fields if provided
      if (salonType != null) requestBody['salon_type'] = salonType;
      if (about != null) requestBody['about'] = about;

      print('Updating salon profile...');
      print('Request URL: ${ApiConstants.salonProfile}');
      print('Request body: ${jsonEncode(requestBody)}');

      final response = await http.patch(
        Uri.parse(ApiConstants.salonProfile),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      print('Update profile response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        // Save updated profile data to shared preferences for quick access
        await prefs.setString('salon_name', responseData['salon_name'] ?? '');
        await prefs.setString('owner_name', responseData['owner_name'] ?? '');

        return responseData;
      } else {
        throw Exception(
          'Failed to update salon profile: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateSalonProfile: $e');
      throw Exception('Error updating salon profile: $e');
    }
  }

  // Get Jobs
  static Future<List<Map<String, dynamic>>> getJobs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching jobs...');

      final response = await http.get(
        Uri.parse(ApiConstants.jobs),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Get jobs response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jobsJson = jsonDecode(response.body);
        return jobsJson.map((job) => job as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to fetch jobs: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in getJobs: $e');
      throw Exception('Error fetching jobs: $e');
    }
  }

  // Delete Job
  static Future<bool> deleteJob(int jobId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Deleting job with ID: $jobId');

      final response = await http.delete(
        Uri.parse('${ApiConstants.vaccancieedit}$jobId/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Delete job response status: ${response.statusCode}');

      if (response.statusCode == 204) {
        return true;
      } else {
        throw Exception(
          'Failed to delete job: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteJob: $e');
      throw Exception('Error deleting job: $e');
    }
  }

  // Create Job
  static Future<Map<String, dynamic>> createJob({
    required String title,
    required String location,
    required String jobType,
    required String jobSummary,
    required List<String> benefits,
    required String commuteInfo,
    required String experience,
    required String gender, // Add gender parameter
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Creating new job...');
      print('Request URL: ${ApiConstants.jobs}');

      final response = await http.post(
        Uri.parse(ApiConstants.addjobs),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'title': title,
          'location': location,
          'job_type': jobType,
          'job_summary': jobSummary,
          'benefits': benefits,
          'commute_info': commuteInfo,
          'experience': experience,
          'gender': gender, // Add gender field to request body
          'is_active': true,
        }),
      );

      print('Create job response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 201 || response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to create job: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in createJob: $e');
      throw Exception('Error creating job: $e');
    }
  }

  // Update Job
  static Future<Map<String, dynamic>> updateJob({
    required int jobId,
    required String title,
    required String location,
    required String jobType,
    required String jobSummary,
    required List<String> keyResponsibilities,
    required List<String> benefits,
    required double payMin,
    required double payMax,
    required String schedule,
    required List<String> supplementalPay,
    required String commuteInfo,
    required String education,
    required String experience,
    required List<String> languages,
    required String workLocation,
    required bool isActive,
    String gender = 'Any', // Added gender parameter with default value
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Debug the gender value
      print('updateJob API call with gender: $gender');

      // Create request body
      final Map<String, dynamic> requestBody = {
        'title': title,
        'location': location,
        'job_type': jobType,
        'job_summary': jobSummary,
        'key_responsibilities': keyResponsibilities,
        'benefits': benefits,
        'pay_min': payMin,
        'pay_max': payMax,
        'schedule': schedule,
        'supplemental_pay': supplementalPay,
        'commute_info': commuteInfo,
        'education': education,
        'experience': experience,
        'languages': languages,
        'work_location': workLocation,
        'is_active': isActive,
        'gender': gender,
      };

      print('Full request body: ${jsonEncode(requestBody)}');

      final response = await http.patch(
        Uri.parse('${ApiConstants.vaccancieedit}$jobId/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      print('Update job response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to update job: ${response.body}');
      }
    } catch (e) {
      print('Error updating job: $e');
      rethrow;
    }
  }

  // Delete Vacancy
  static Future<bool> deleteVacancy(int vacancyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Deleting vacancy with ID: $vacancyId');

      final response = await http.delete(
        Uri.parse('${ApiConstants.vaccancieedit}$vacancyId/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Delete vacancy response status: ${response.statusCode}');

      if (response.statusCode == 204) {
        return true;
      } else if (response.statusCode == 403) {
        // Handle permission error specifically
        final responseData = json.decode(response.body);
        final errorMessage = responseData['detail'] ?? 'Permission denied';
        throw Exception('Permission error: $errorMessage');
      } else {
        throw Exception(
          'Failed to delete vacancy: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteVacancy: $e');
      throw Exception('Error deleting vacancy: $e');
    }
  }

  // Fetch Salon Employees
  static Future<List<Map<String, dynamic>>> fetchSalonEmployees() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching salon employees...');

      final response = await http.get(
        Uri.parse(ApiConstants.salonEmployees),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Get employees response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> employeesJson = jsonDecode(response.body);
        return employeesJson
            .map((employee) => employee as Map<String, dynamic>)
            .toList();
      } else {
        throw Exception(
          'Failed to fetch employees: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchSalonEmployees: $e');
      throw Exception('Error fetching employees: $e');
    }
  }

  // Add this method to ApiService class
  static Future<Map<String, dynamic>> registerExistingEmployee({
    required String email,
    required String name,
    required String contactNo,
    required String jobTitle,
    required String joinDate,
    required String currentSalary,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse(ApiConstants.registerExistingEmployee),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'email': email,
          'name': name,
          'contact_no': contactNo,
          'job_title': jobTitle,
          'join_date': joinDate,
          'current_salary': currentSalary,
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to register employee: ${response.body}');
      }
    } catch (e) {
      print('Error in registerExistingEmployee: $e');
      throw Exception('Error registering employee: $e');
    }
  }

  // Get Applications
  static Future<List<Map<String, dynamic>>> getApplications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching applications...');

      final response = await http.get(
        Uri.parse(ApiConstants.applications),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Get applications response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> applicationsJson = jsonDecode(response.body);
        return applicationsJson
            .map((app) => app as Map<String, dynamic>)
            .toList();
      } else {
        throw Exception(
          'Failed to fetch applications: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in getApplications: $e');
      throw Exception('Error fetching applications: $e');
    }
  }

  // Update Application Status
  static Future<Map<String, dynamic>> updateApplicationStatus({
    required int applicationId,
    required String status,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating application status: $applicationId to $status');

      final response = await http.patch(
        Uri.parse('${ApiConstants.applications}$applicationId/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'status': status.toUpperCase()}),
      );

      print('Update application status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update application status: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateApplicationStatus: $e');
      throw Exception('Error updating application status: $e');
    }
  }

  // Fetch Hired Employees
  static Future<List<Map<String, dynamic>>> fetchHiredEmployees() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching hired employees...');

      final response = await http.get(
        Uri.parse(ApiConstants.hiredEmployees),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Get hired employees response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> hiredEmployeesJson = jsonDecode(response.body);
        return hiredEmployeesJson
            .map((employee) => employee as Map<String, dynamic>)
            .toList();
      } else {
        throw Exception(
          'Failed to fetch hired employees: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchHiredEmployees: $e');
      throw Exception('Error fetching hired employees: $e');
    }
  }

  // Add Official Employee
  static Future<Map<String, dynamic>> addOfficialEmployee({
    required int jobseekerId,
    required String joinDate,
    required String jobTitle,
    required String currentSalary,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Adding official employee...');

      final response = await http.post(
        Uri.parse(ApiConstants.addOfficialEmployee),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'jobseeker_id': jobseekerId,
          'join_date': joinDate,
          'job_title': jobTitle,
          'current_salary': currentSalary,
        }),
      );

      print('Add official employee response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to add official employee: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in addOfficialEmployee: $e');
      throw Exception('Error adding official employee: $e');
    }
  }

  // Submit Employee Exit Evaluation
  static Future<Map<String, dynamic>> submitEmployeeExit({
    required int employeeId,
    required String endDate,
    required double punctualityRating,
    required double communicationRating,
    required double workingSkillRating,
    required double behaviorRating,
    required String exitRemarks,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Submitting employee exit evaluation for ID: $employeeId');

      final response = await http.patch(
        Uri.parse('${ApiConstants.employeeExit}$employeeId/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'end_date': endDate,
          'punctuality_rating': punctualityRating,
          'communication_rating': communicationRating,
          'working_skill_rating': workingSkillRating,
          'behavior_rating': behaviorRating,
          'exit_remarks': exitRemarks,
        }),
      );

      print('Employee exit evaluation response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to submit exit evaluation: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in submitEmployeeExit: $e');
      throw Exception('Error submitting exit evaluation: $e');
    }
  }

  // Add this method to fetch open jobseekers
  static Future<List<Map<String, dynamic>>> fetchOpenJobseekers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching open jobseekers...');

      final response = await http.get(
        Uri.parse(ApiConstants.openJobseekers),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Get open jobseekers response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> jobseekersJson = jsonDecode(response.body);
        return jobseekersJson
            .map((jobseeker) => jobseeker as Map<String, dynamic>)
            .toList();
      } else {
        throw Exception(
          'Failed to fetch open jobseekers: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchOpenJobseekers: $e');
      throw Exception('Error fetching open jobseekers: $e');
    }
  }

  // Fetch specific jobseeker details
  static Future<Map<String, dynamic>> fetchJobseekerDetail(
    int jobseekerId,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching jobseeker details for ID: $jobseekerId');
      final url = "${ApiConstants.jobseekerDetail}$jobseekerId/";

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Jobseeker detail response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to fetch jobseeker details: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchJobseekerDetail: $e');
      throw Exception('Error fetching jobseeker details: $e');
    }
  }

  // Add this method to fetch salon invitations
  static Future<List<Map<String, dynamic>>> getSalonInvitations() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final url = Uri.parse('${ApiConstants.baseUrl}/salon-invitations/');

    try {
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception('Failed to load invitations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching invitations: $e');
    }
  }

  // Add this method to fetch jobseeker's salon invitations
  static Future<List<Map<String, dynamic>>> getJobseekerInvitations() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final url = Uri.parse('${ApiConstants.baseUrl}/salon-invitations/');

    try {
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception('Failed to load invitations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching invitations: $e');
    }
  }

  // Add this method to update salon invitation status
  static Future<Map<String, dynamic>> updateInvitationStatus({
    required int invitationId,
    required String status,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final url =
        Uri.parse('${ApiConstants.baseUrl}/salon-invitations/$invitationId/');

    try {
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'status': status.toUpperCase()}),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
            'Failed to update invitation status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating invitation status: $e');
    }
  }

  // Fetch customer visits for business income
  static Future<List<Map<String, dynamic>>> fetchCustomerVisits() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching customer visits...');

      final response = await http.get(
        Uri.parse(ApiConstants.customerVisits),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Customer visits response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load customer visits: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchCustomerVisits: $e');
      throw Exception('Error fetching customer visits: $e');
    }
  }

  // Add customer visit transaction
  static Future<Map<String, dynamic>> addCustomerVisit({
    required String date,
    required String customerName,
    required String phoneNumber,
    required List<int> services, // Array of service IDs
    required String amount,
    required String paymentMethod,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Adding customer visit...');
      print('Services IDs: $services');

      final response = await http.post(
        Uri.parse(ApiConstants.addCustomerVisit),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'date': date,
          'customer_name': customerName,
          'phone_number': phoneNumber,
          'services': services, // Send as array of service IDs
          'amount': amount,
          'payment_method': paymentMethod,
        }),
      );

      print('Add customer visit response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to add customer visit: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in addCustomerVisit: $e');
      throw Exception('Error adding customer visit: $e');
    }
  }

  // Fetch a specific customer visit by ID
  static Future<Map<String, dynamic>> fetchCustomerVisitById(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching customer visit with ID: $id');

      final response = await http.get(
        Uri.parse('${ApiConstants.customerVisits}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Customer visit detail response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to load customer visit: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchCustomerVisitById: $e');
      throw Exception('Error fetching customer visit: $e');
    }
  }

  // Update a customer visit
  static Future<Map<String, dynamic>> updateCustomerVisit({
    required int id,
    required String date,
    required String customerName,
    required String phoneNumber,
    required List<int> services, // Changed to List<int> to match API
    required String amount,
    required String paymentMethod,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating customer visit with ID: $id');
      print('Services IDs: $services');

      // Create request body matching the API's expected format
      final requestBody = {
        'date': date,
        'customer_name': customerName,
        'phone_number': phoneNumber,
        'services': services, // Send as array of service IDs
        'amount': amount,
        'payment_method': paymentMethod,
      };

      print('Request body: ${jsonEncode(requestBody)}');

      final response = await http.patch(
        Uri.parse('${ApiConstants.customerVisits}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      print('Update customer visit response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update customer visit: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateCustomerVisit: $e');
      throw Exception('Error updating customer visit: $e');
    }
  }

  // Delete a customer visit
  static Future<Map<String, dynamic>> deleteCustomerVisit(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Deleting customer visit with ID: $id');

      final response = await http.delete(
        Uri.parse('${ApiConstants.customerVisits}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Delete customer visit response status: ${response.statusCode}');

      if (response.statusCode == 204) {
        return {'success': true};
      } else {
        throw Exception(
          'Failed to delete customer visit: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteCustomerVisit: $e');
      throw Exception('Error deleting customer visit: $e');
    }
  }

  // Add this method to fetch jobseeker profile
  static Future<Map<String, dynamic>> fetchJobseekerProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching jobseeker profile...');
      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-profile/me/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Jobseeker profile response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to load jobseeker profile: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchJobseekerProfile: $e');
      throw Exception('Error fetching jobseeker profile: $e');
    }
  }

  // Add this method to update jobseeker profile
  static Future<Map<String, dynamic>> updateJobseekerProfile({
    String? name,
    String? dateOfBirth,
    String? gender,
    List<int>? expertiseAreas,
    String? yearsOfExperience,
    String? contactNo,
    String? socialLink,
    String? place,
    String? about,
    // Note: profile_picture and resume would typically be handled separately
    // as they require file uploads
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating jobseeker profile...');

      // Build request body with only the fields that are provided
      final Map<String, dynamic> requestBody = {};

      if (name != null) requestBody['name'] = name;
      if (dateOfBirth != null) requestBody['date_of_birth'] = dateOfBirth;
      if (gender != null) requestBody['gender'] = gender;
      if (expertiseAreas != null)
        requestBody['expertise_areas'] = expertiseAreas;
      if (yearsOfExperience != null)
        requestBody['years_of_experience'] = yearsOfExperience;
      if (contactNo != null) requestBody['contact_no'] = contactNo;
      if (socialLink != null) requestBody['social_link'] = socialLink;
      if (place != null) requestBody['place'] = place;
      if (about != null) requestBody['about'] = about;

      print('Request body: ${jsonEncode(requestBody)}');

      final response = await http.patch(
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-profile/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode(requestBody),
      );

      print('Update jobseeker profile response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update jobseeker profile: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateJobseekerProfile: $e');
      throw Exception('Error updating jobseeker profile: $e');
    }
  }

  // Add this method to upload profile picture
  static Future<Map<String, dynamic>> uploadProfilePicture(
    File imageFile,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Create multipart request
      final request = http.MultipartRequest(
        'PATCH',
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-profile/'),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add file
      request.files.add(
        await http.MultipartFile.fromPath('profile_picture', imageFile.path),
      );

      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('Upload profile picture response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload profile picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadProfilePicture: $e');
      throw Exception('Error uploading profile picture: $e');
    }
  }

  // Add this method to upload resume
  static Future<bool> uploadResumeFile(File resumeFile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final url = Uri.parse('${ApiConstants.baseUrl}/jobseeker/upload-resume');

      // Create multipart request
      var request = http.MultipartRequest('POST', url);

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add file to request
      request.files.add(
        await http.MultipartFile.fromPath('resume', resumeFile.path),
      );

      // Send request
      final response = await request.send();

      return response.statusCode == 200;
    } catch (e) {
      print('Error uploading resume: $e');
      return false;
    }
  }

  // Add methods for file uploads
  static Future<Map<String, dynamic>> uploadProfilePictureMultipart(
      File imageFile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Create multipart request
      var request = http.MultipartRequest(
        'PATCH',
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-profile/'),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add file
      request.files.add(
        await http.MultipartFile.fromPath(
          'profile_picture',
          imageFile.path,
        ),
      );

      // Send request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload profile picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadProfilePicture: $e');
      throw Exception('Error uploading profile picture: $e');
    }
  }

  static Future<Map<String, dynamic>> uploadResumeFileMultipart(
      File resumeFile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Create multipart request
      var request = http.MultipartRequest(
        'PATCH',
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-profile/'),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add file
      request.files.add(
        await http.MultipartFile.fromPath(
          'resume',
          resumeFile.path,
        ),
      );

      // Send request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload resume: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadResumeFile: $e');
      throw Exception('Error uploading resume: $e');
    }
  }

  // Add this method to delete work picture
  static Future<Map<String, dynamic>> deleteWorkPicture(int pictureId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Send DELETE request to the work pictures endpoint
      final response = await http.delete(
        Uri.parse(
            '${ApiConstants.baseUrl}/jobseeker-profile/work-pictures/${pictureId}/'),
        headers: {
          'Authorization': 'Token $token',
        },
      );

      print('Delete work picture response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        // If the response has a body, parse it; otherwise return an empty map
        return response.body.isNotEmpty ? jsonDecode(response.body) : {};
      } else {
        throw Exception(
          'Failed to delete work picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteWorkPicture: $e');
      throw Exception('Error deleting work picture: $e');
    }
  }

  // Upload work picture using the jobseeker profile API
  static Future<Map<String, dynamic>> uploadWorkPicture(File imageFile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Create multipart request
      var request = http.MultipartRequest(
        'PATCH',
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-profile/'),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add file to request - use 'work_pictures' as the field name
      request.files.add(
        await http.MultipartFile.fromPath('work_pictures', imageFile.path),
      );

      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print('Upload work picture response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload work picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadWorkPicture: $e');
      throw Exception('Error uploading work picture: $e');
    }
  }

  // Fetch Specializations (Expertise Areas)
  static Future<List<Map<String, dynamic>>> fetchSpecializations() async {
    try {
      print('Fetching specializations from: ${ApiConstants.specializations}');

      final response = await http.get(
        Uri.parse(ApiConstants.specializations),
        headers: {'Content-Type': 'application/json'},
      );

      print('Specializations response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load specializations: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchSpecializations: $e');
      throw Exception('Error fetching specializations: $e');
    }
  }

  // Toggle jobseeker work status
  static Future<Map<String, dynamic>> toggleJobseekerWorkStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Toggling jobseeker work status...');

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/toggle-jobseeker-status/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Toggle work status response: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to toggle work status: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in toggleJobseekerWorkStatus: $e');
      throw Exception('Error toggling work status: $e');
    }
  }

  // Send job request to salon
  static Future<Map<String, dynamic>> sendJobRequest({
    required int salonId,
    String message = 'I am interested in working at your salon.',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Sending job request to salon ID: $salonId');

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/salon-requests/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'salon_id': salonId,
          'message': message,
        }),
      );

      print('Job request response status: ${response.statusCode}');

      if (response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        return responseData;
      } else {
        // If there's an error field in the response, throw that as the exception
        final responseData = jsonDecode(response.body);
        if (responseData.containsKey('error')) {
          throw Exception(responseData['error']);
        } else {
          throw Exception('Failed to send job request: ${response.body}');
        }
      }
    } catch (e) {
      print('Error in sendJobRequest: $e');
      throw Exception('Error sending job request: $e');
    }
  }

  // Fetch Jobs for Jobseekers
  static Future<List<Map<String, dynamic>>> fetchJobsForJobseekers() async {
    try {
      print(
          'Fetching jobs for jobseekers from: ${ApiConstants.jobsForJobseekers}');

      final response = await http.get(
        Uri.parse(ApiConstants.jobsForJobseekers),
        headers: {'Content-Type': 'application/json'},
      );

      print('Jobs response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load jobs: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchJobsForJobseekers: $e');
      throw Exception('Error fetching jobs: $e');
    }
  }

  // Fetch Job Details by ID
  static Future<Map<String, dynamic>> fetchJobDetailsById(int jobId) async {
    try {
      print('Fetching job details for ID: $jobId');
      final url = "${ApiConstants.jobDetails}$jobId/";

      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      print('Job details response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to load job details: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchJobDetailsById: $e');
      throw Exception('Error fetching job details: $e');
    }
  }

  // Request password reset
  static Future<Map<String, dynamic>> requestPasswordReset({
    required String email,
  }) async {
    try {
      print('Requesting password reset for: $email');

      final response = await http.post(
        Uri.parse(ApiConstants.forgotPassword),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
        }),
      );

      print('Password reset response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to request password reset: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in requestPasswordReset: $e');
      throw Exception('Error requesting password reset: $e');
    }
  }

  // Verify OTP
  static Future<Map<String, dynamic>> verifyOtp({
    required String email,
    required String otp,
  }) async {
    try {
      print('Verifying OTP for email: $email');

      final response = await http.post(
        Uri.parse(ApiConstants.verifyOtp),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'otp': otp,
        }),
      );

      print('OTP verification response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to verify OTP: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in verifyOtp: $e');
      throw Exception('Error verifying OTP: $e');
    }
  }

  // Reset Password
  static Future<Map<String, dynamic>> resetPassword({
    required String email,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      print('Resetting password for email: $email');

      final response = await http.post(
        Uri.parse(ApiConstants.resetPassword),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'new_password': newPassword,
          'confirm_password': confirmPassword,
        }),
      );

      print('Reset password response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to reset password: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in resetPassword: $e');
      throw Exception('Error resetting password: $e');
    }
  }

  // Fetch operational expenses
  static Future<List<Map<String, dynamic>>> fetchOperationalExpenses() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching operational expenses...');

      final response = await http.get(
        Uri.parse(ApiConstants.operationalExpenses),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Operational expenses response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load operational expenses: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchOperationalExpenses: $e');
      throw Exception('Error fetching operational expenses: $e');
    }
  }

  // Add operational expense
  static Future<Map<String, dynamic>> addOperationalExpense({
    required String date,
    required String expenseCategory,
    required String description,
    required String vendorPayee,
    required String totalAmount,
    required String paidAmount,
    required String balance,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Adding operational expense...');
      print('Request body: ${jsonEncode({
            'date': date,
            'expense_category': expenseCategory,
            'description': description,
            'vendor_payee': vendorPayee,
            'total_amount': totalAmount,
            'paid_amount': paidAmount,
            'balance': balance,
          })}');

      final response = await http.post(
        Uri.parse(ApiConstants.operationalExpenses),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'date': date,
          'expense_category': expenseCategory,
          'description': description,
          'vendor_payee': vendorPayee,
          'total_amount': totalAmount,
          'paid_amount': paidAmount,
          'balance': balance,
        }),
      );

      print('Add operational expense response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to add operational expense: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in addOperationalExpense: $e');
      throw Exception('Error adding operational expense: $e');
    }
  }

  // Update operational expense
  static Future<Map<String, dynamic>> updateOperationalExpense({
    required int id,
    required String date,
    required String expenseCategory,
    required String description,
    required String vendorPayee,
    required double totalAmount,
    required double paidAmount,
    required double balance,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating operational expense with ID: $id');

      final response = await http.patch(
        Uri.parse('${ApiConstants.operationalExpenses}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'date': date,
          'expense_category': expenseCategory,
          'description': description,
          'vendor_payee': vendorPayee,
          'total_amount': totalAmount.toString(),
          'paid_amount': paidAmount.toString(),
          'balance': balance.toString(),
        }),
      );

      print(
          'Update operational expense response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update operational expense: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateOperationalExpense: $e');
      throw Exception('Error updating operational expense: $e');
    }
  }

  // Delete operational expense
  static Future<bool> deleteOperationalExpense(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Deleting operational expense with ID: $id');

      final response = await http.delete(
        Uri.parse('${ApiConstants.operationalExpenses}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print(
          'Delete operational expense response status: ${response.statusCode}');

      if (response.statusCode == 204) {
        return true;
      } else {
        throw Exception(
          'Failed to delete operational expense: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteOperationalExpense: $e');
      throw Exception('Error deleting operational expense: $e');
    }
  }

  // Fetch product purchases
  static Future<List<Map<String, dynamic>>> fetchProductPurchases() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching product purchases...');

      final response = await http.get(
        Uri.parse(ApiConstants.productPurchases),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Product purchases response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load product purchases: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchProductPurchases: $e');
      throw Exception('Error fetching product purchases: $e');
    }
  }

  // Add product purchase
  static Future<Map<String, dynamic>> addProductPurchase({
    required String date,
    required String category,
    required String item,
    required String rate,
    required int quantity,
    required String amount,
    required String paidAmount,
    required String balance,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Adding product purchase...');

      final response = await http.post(
        Uri.parse(ApiConstants.productPurchases),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'date': date,
          'category': category,
          'item': item,
          'rate': rate,
          'quantity': quantity,
          'amount': amount,
          'paid_amount': paidAmount,
          'balance': balance,
        }),
      );

      print('Add product purchase response status: ${response.statusCode}');

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to add product purchase: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in addProductPurchase: $e');
      throw Exception('Error adding product purchase: $e');
    }
  }

  // Update product purchase
  static Future<Map<String, dynamic>> updateProductPurchase({
    required int id,
    required String date,
    required String category,
    required String item,
    required double rate,
    required int quantity,
    required double amount,
    required double paidAmount,
    required double balance,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating product purchase with ID: $id');

      final response = await http.patch(
        Uri.parse('${ApiConstants.productPurchases}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'date': date,
          'category': category,
          'item': item,
          'rate': rate.toString(),
          'quantity': quantity,
          'amount': amount.toString(),
          'paid_amount': paidAmount.toString(),
          'balance': balance.toString(),
        }),
      );

      print('Update product purchase response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update product purchase: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateProductPurchase: $e');
      throw Exception('Error updating product purchase: $e');
    }
  }

  // Delete product purchase
  static Future<bool> deleteProductPurchase(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Deleting product purchase with ID: $id');

      final response = await http.delete(
        Uri.parse('${ApiConstants.productPurchases}$id/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Delete product purchase response status: ${response.statusCode}');

      if (response.statusCode == 204) {
        return true;
      } else {
        throw Exception(
          'Failed to delete product purchase: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteProductPurchase: $e');
      throw Exception('Error deleting product purchase: $e');
    }
  }

  // Apply for a job
  static Future<Map<String, dynamic>> applyForJob({
    required int jobId,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Applying for job with ID: $jobId');

      final response = await http.post(
        Uri.parse(ApiConstants.applications),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'job': jobId,
        }),
      );

      print('Job application response status: ${response.statusCode}');

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to apply for job: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in applyForJob: $e');
      throw Exception('Error applying for job: $e');
    }
  }

  // Add this method to fetch jobseeker's requests
  static Future<List<Map<String, dynamic>>> getJobseekerRequests() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final url = Uri.parse('${ApiConstants.baseUrl}/jobseeker-requests/');

    try {
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception('Failed to load requests: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching requests: $e');
    }
  }

  // Add this method to update request status
  static Future<Map<String, dynamic>> updateRequestStatus({
    required int requestId,
    required String status,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final url =
        Uri.parse('${ApiConstants.baseUrl}/jobseeker-requests/$requestId/');

    try {
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'status': status.toUpperCase()}),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception(
            'Failed to update request status: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error updating request status: $e');
    }
  }

  // Add this method to fetch salon requests for jobseekers
  static Future<List<Map<String, dynamic>>> getSalonRequests() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');

    if (token == null) {
      throw Exception('Authentication token not found');
    }

    final url = Uri.parse('${ApiConstants.baseUrl}/salon-requests-list/');

    try {
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
            'Failed to load salon requests: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching salon requests: $e');
    }
  }

  // Fetch joining requests for jobseekers
  static Future<List<Map<String, dynamic>>>
      getJobseekerJoiningRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching joining requests for jobseeker...');
      // Use the base joining requests endpoint
      final url = Uri.parse(ApiConstants.jobseekerJoiningRequests);

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Joining requests response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else if (response.statusCode == 404) {
        print('Joining requests endpoint not found. Check API URL.');
        // Return empty list instead of throwing exception for 404
        return [];
      } else {
        throw Exception(
            'Failed to load joining requests: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in getJobseekerJoiningRequests: $e');
      // Return empty list for any errors to prevent app crashes
      return [];
    }
  }

  // Respond to salon request (for jobseekers responding to salon requests)
  static Future<Map<String, dynamic>> respondToSalonRequest({
    required int requestId,
    required bool accept,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Responding to salon request ID: $requestId with accept: $accept');

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/salon-requests/$requestId/respond/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'accept': accept}),
      );

      print('Salon request response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to respond to salon request: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in respondToSalonRequest: $e');
      throw Exception('Error responding to salon request: $e');
    }
  }

  // Respond to jobseeker request (for salon owners responding to jobseeker requests)
  static Future<Map<String, dynamic>> respondToJobseekerRequest({
    required int requestId,
    required bool accept,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print(
          'Responding to jobseeker request ID: $requestId with accept: $accept');

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/salon-requests/$requestId/respond/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'accept': accept}),
      );

      print('Jobseeker request response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to respond to jobseeker request: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in respondToJobseekerRequest: $e');
      throw Exception('Error responding to jobseeker request: $e');
    }
  }

  // Create joining request
  static Future<Map<String, dynamic>> createJoiningRequest({
    required int jobseekerId,
    required String jobTitle,
    required double salary,
    required String message,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Creating joining request for jobseeker ID: $jobseekerId');

      final response = await http.post(
        Uri.parse(ApiConstants.createJoiningRequest),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'jobseeker_id': jobseekerId,
          'job_title': jobTitle,
          'salary': salary,
          'message': message,
        }),
      );

      print('Joining request response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to create joining request: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in createJoiningRequest: $e');
      throw Exception('Error creating joining request: $e');
    }
  }

  // Fetch salon requests list for jobseekers
  static Future<List<Map<String, dynamic>>>
      getJobseekerSalonRequestsList() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching salon requests list for jobseeker...');
      final url = Uri.parse('${ApiConstants.baseUrl}/salon-requests-list/');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Salon requests list response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
            'Failed to load salon requests list: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in getJobseekerSalonRequestsList: $e');
      // Return empty list for any errors to prevent app crashes
      return [];
    }
  }

  // Fetch salon requests for "Via Request" section
  static Future<List<Map<String, dynamic>>>
      fetchSalonRequestsForViaRequest() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching salon requests for Via Request section...');
      final url = Uri.parse(ApiConstants.salonRequestsList);

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print(
          'Via Request salon requests response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
            'Failed to load salon requests: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in fetchSalonRequestsForViaRequest: $e');
      throw Exception('Error fetching salon requests: $e');
    }
  }

  // Respond to joining request
  static Future<Map<String, dynamic>> respondToJoiningRequest({
    required int requestId,
    required bool accept,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print(
          'Responding to joining request ID: $requestId with accept: $accept');

      final response = await http.post(
        Uri.parse(
            '${ApiConstants.baseUrl}/joining-requests/$requestId/respond/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({'accept': accept}),
      );

      print('Joining request response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to respond to joining request: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in respondToJoiningRequest: $e');
      throw Exception('Error responding to joining request: $e');
    }
  }

  // Fetch joining requests list for jobseekers
  static Future<List<Map<String, dynamic>>> getJoiningRequestsList() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching joining requests list...');
      final url = Uri.parse(ApiConstants.jobseekerJoiningRequests);

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Joining requests list response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
            'Failed to load joining requests: ${response.statusCode}');
      }
    } catch (e) {
      print('Error in getJoiningRequestsList: $e');
      throw Exception('Error fetching joining requests: $e');
    }
  }

  // Add new service
  static Future<Map<String, dynamic>> addNewService({
    required String serviceType,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Adding new service: $serviceType');

      final response = await http.post(
        Uri.parse(ApiConstants.services),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'servicetype': serviceType,
        }),
      );

      print('Add service response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to add service: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in addNewService: $e');
      throw Exception('Error adding service: $e');
    }
  }

  // Update service
  static Future<Map<String, dynamic>> updateService({
    required int serviceId,
    required String serviceType,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Updating service ID: $serviceId with type: $serviceType');

      final response = await http.patch(
        Uri.parse('${ApiConstants.services}$serviceId/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
        body: jsonEncode({
          'servicetype': serviceType,
        }),
      );

      print('Update service response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to update service: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in updateService: $e');
      throw Exception('Error updating service: $e');
    }
  }

  // Fetch all services
  static Future<List<Map<String, dynamic>>> fetchServices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Fetching services...');

      final response = await http.get(
        Uri.parse(ApiConstants.services),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Token $token',
        },
      );

      print('Fetch services response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((item) => item as Map<String, dynamic>).toList();
      } else {
        throw Exception(
          'Failed to load services: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in fetchServices: $e');
      throw Exception('Error fetching services: $e');
    }
  }

  // Upload salon profile picture
  static Future<Map<String, dynamic>> uploadSalonProfilePicture(
    File imageFile,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Uploading salon profile picture...');

      // Create multipart request
      final request = http.MultipartRequest(
        'PATCH',
        Uri.parse(ApiConstants.salonProfile),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add file
      request.files.add(
        await http.MultipartFile.fromPath('profile_picture', imageFile.path),
      );

      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print(
          'Upload salon profile picture response status: ${response.statusCode}');
      print('Upload salon profile picture response body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload salon profile picture: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadSalonProfilePicture: $e');
      throw Exception('Error uploading salon profile picture: $e');
    }
  }

  // Upload salon images
  static Future<Map<String, dynamic>> uploadSalonImages(
    List<File> imageFiles,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Uploading ${imageFiles.length} salon images...');

      // Try the dedicated salon images endpoint first
      try {
        final request = http.MultipartRequest(
          'POST',
          Uri.parse(ApiConstants.salonImages),
        );

        request.headers['Authorization'] = 'Token $token';

        for (int i = 0; i < imageFiles.length; i++) {
          request.files.add(
            await http.MultipartFile.fromPath(
                'uploaded_images', imageFiles[i].path),
          );
        }

        final streamedResponse = await request.send();
        final response = await http.Response.fromStream(streamedResponse);

        print('Upload salon images response status: ${response.statusCode}');
        print('Upload salon images response body: ${response.body}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          return jsonDecode(response.body);
        }
      } catch (e) {
        print(
            'Failed with dedicated endpoint, trying alternative approach: $e');
      }

      // Alternative approach: Use PATCH to salon profile endpoint
      final request = http.MultipartRequest(
        'PATCH',
        Uri.parse(ApiConstants.salonProfile),
      );

      request.headers['Authorization'] = 'Token $token';

      for (int i = 0; i < imageFiles.length; i++) {
        request.files.add(
          await http.MultipartFile.fromPath(
              'uploaded_images', imageFiles[i].path),
        );
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print(
          'Alternative upload salon images response status: ${response.statusCode}');
      print('Alternative upload salon images response body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload salon images: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadSalonImages: $e');
      throw Exception('Error uploading salon images: $e');
    }
  }

  // Delete salon image
  static Future<Map<String, dynamic>> deleteSalonImage(int imageId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Attempting to delete salon image with ID: $imageId');
      print('Delete URL: ${ApiConstants.salonImageDelete}$imageId/');

      final response = await http.delete(
        Uri.parse('${ApiConstants.salonImageDelete}$imageId/'),
        headers: {
          'Authorization': 'Token $token',
        },
      );

      print('Delete salon image response status: ${response.statusCode}');
      print('Delete salon image response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        // If the response has a body, parse it; otherwise return an empty map
        return response.body.isNotEmpty ? jsonDecode(response.body) : {};
      } else {
        throw Exception(
          'Failed to delete salon image: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in deleteSalonImage: $e');
      throw Exception('Error deleting salon image: $e');
    }
  }

  // Upload salon profile with images (comprehensive method)
  static Future<Map<String, dynamic>> uploadSalonProfileWithImages({
    File? profilePicture,
    List<File>? salonImages,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      print('Uploading salon profile with images...');
      print('Profile picture: ${profilePicture != null ? 'Yes' : 'No'}');
      print('Salon images: ${salonImages?.length ?? 0}');

      // Create multipart request
      final request = http.MultipartRequest(
        'PATCH',
        Uri.parse(ApiConstants.salonProfile),
      );

      // Add authorization header
      request.headers['Authorization'] = 'Token $token';

      // Add profile picture if provided
      if (profilePicture != null) {
        request.files.add(
          await http.MultipartFile.fromPath(
              'profile_picture', profilePicture.path),
        );
      }

      // Add salon images if provided
      if (salonImages != null && salonImages.isNotEmpty) {
        for (int i = 0; i < salonImages.length; i++) {
          request.files.add(
            await http.MultipartFile.fromPath(
                'uploaded_images', salonImages[i].path),
          );
        }
      }

      // Send request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      print(
          'Upload salon profile with images response status: ${response.statusCode}');
      print('Upload salon profile with images response body: ${response.body}');

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to upload salon profile with images: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Error in uploadSalonProfileWithImages: $e');
      throw Exception('Error uploading salon profile with images: $e');
    }
  }
}
