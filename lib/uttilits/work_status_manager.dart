import 'package:flutter/foundation.dart';

// Singleton class to manage work status globally
class WorkStatusManager {
  // Singleton instance
  static final WorkStatusManager _instance = WorkStatusManager._internal();

  // Factory constructor to return the same instance
  factory WorkStatusManager() {
    return _instance;
  }

  // Private constructor
  WorkStatusManager._internal();

  // Observable value for work status
  final ValueNotifier<bool> isWorking = ValueNotifier<bool>(false);

  // Method to set work status
  void setWorkStatus(bool status) {
    isWorking.value = status;
    print("Global work status changed to: $status");
  }
}
