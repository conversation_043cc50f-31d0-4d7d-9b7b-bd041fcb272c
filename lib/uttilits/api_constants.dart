class ApiConstants {
  // Base URL
  static const String baseUrl = "http://mountvoq.tqdemo.website/api";

  // Add this constant for media URLs
  static final String baseMediaUrl = baseUrl.split('/api')[0];

  // Jobs endpoint
  static const String jobs = "$baseUrl/salon-jobs/";
  static const String vaccancieedit = "$baseUrl/jobs/"; // Add ID at the end
  static const String addjobs = "$baseUrl/jobs/";

  // Add joining request creation endpoint
  static const String createJoiningRequest =
      "$baseUrl/joining-requests/create/";

  // Add or update the salon profile endpoint
  static const String salonProfile = "$baseUrl/salon-profile/";

  // Auth endpoints
  static const String adminLogin = "$baseUrl/login/";
  static const String adminRegister = "$baseUrl/register/admin/";
  static const String jobseekerRegister = "$baseUrl/register/jobseeker/";
  static const String registerExistingEmployee =
      "$baseUrl/register/existing-employee/";
  static const String forgotPassword = "$baseUrl/forgot-password/";
  static const String verifyOtp = "$baseUrl/verify-otp/";
  static const String resetPassword = "$baseUrl/reset-password/";

  // Blog endpoints
  static const String trainingBlogs = "$baseUrl/blogs/training/";
  static const String trainingBlogDetail =
      "$baseUrl/blogs/training/"; // Add ID at the end
  static const String trainingRegister = "$baseUrl/blogs/training-register/";

  // Consultation endpoints
  static const String consultations = "$baseUrl/blogs/consultations/";
  static const String consultationDetail =
      "$baseUrl/blogs/consultations/"; // Add ID at the end

  // Products endpoints
  static const String products = "$baseUrl/blogs/products/";
  static const String productDetail =
      "$baseUrl/blogs/products/"; // Add ID at the end

  // Trending posts endpoint
  static const String trendingPosts = "$baseUrl/blogs/trending/";

  // Employee endpoints
  static const String salonEmployees = "$baseUrl/salon-employees/";

  // Applications endpoint
  static const String applications = "$baseUrl/applications/";
  static const String salonInvitations = "$baseUrl/salon-invitations/";
  static const String jobseekerRequests = "$baseUrl/jobseeker-requests/";

  // Hired Employees endpoint
  static const String hiredEmployees = "$baseUrl/hired-employees/";

  // Add official employee endpoint
  static const String addOfficialEmployee = "$baseUrl/add-official-employee/";

  // Employee exit evaluation endpoint
  static const String employeeExit = "$baseUrl/employee-exit/";

  // Add this new endpoint for open jobseekers
  static const String openJobseekers = "$baseUrl/open-jobseekers/";

  // Add this new endpoint for specific jobseeker details
  static const String jobseekerDetail =
      "$baseUrl/open-jobseekers/"; // Add ID at the end

  // Add this new endpoint for specific jobseeker details with pictures
  static const String jobseekerDetailWithPictures =
      "$baseUrl/open-jobseekers/"; // Add ID at the end

  // Add this new endpoint for customer visits analytics
  static const String customerVisits = "$baseUrl/analytics/customer-visits/";

  // Add this endpoint for adding customer visits
  static const String addCustomerVisit = "$baseUrl/analytics/customer-visits/";

  // Add this endpoint for work pictures
  static const String jobseekerWorkPictures = "$baseUrl/jobseeker-profile/";

  // Jobseeker profile endpoint
  static const String jobseekerProfile = "$baseUrl/jobseeker-profile/me/";

  // Work pictures upload endpoint
  static const String uploadWorkPicture =
      "$baseUrl/jobseeker-profile/work-pictures/";

  // Specializations endpoint
  static const String specializations = "$baseUrl/specializations/";

  // Add this endpoint for joining requests
  static const String joiningRequests = "$baseUrl/jobs/create/";

  // Add this endpoint for salons list for jobseekers
  static const String salonsForJobseekers = "$baseUrl/salons-for-jobseekers/";
  // Add this endpoint for jobs
  static const String jobsForJobseekers = "$baseUrl/jobs/";

  // Jobs detail

  static const String jobDetails = "$baseUrl/jobs/"; // Add ID at the end

  // Add this new endpoint for operational expenses
  static const String operationalExpenses =
      "$baseUrl/analytics/operational-expenses/";

  // Add this new endpoint for product purchases
  static const String productPurchases =
      "$baseUrl/analytics/product-purchases/";

  // Add joining requests endpoints
  static const String salonJoiningRequests = "$baseUrl/joining-requests/";
  static const String jobseekerJoiningRequests = "$baseUrl/joining-requests/";

  // Add missing endpoint for salon requests list
  static const String salonRequestsList = "$baseUrl/salon-requests-list/";
  static const String salonRequests =
      "$baseUrl/salon-requests/"; // Add ID at the end

  // Add this endpoint for salon requests list for jobseekers
  static const String jobseekerSalonRequestsList =
      "$baseUrl/salon-requests-list/";

  // Add this endpoint for services management
  static const String services = "$baseUrl/analytics/services/";

  // Add salon profile picture and images upload endpoints
  static const String salonProfilePicture = "$baseUrl/salon-profile/picture/";
  static const String salonImages = "$baseUrl/salon-profile/images/";
  static const String salonImageDelete = "$baseUrl/salon-image/";
}
