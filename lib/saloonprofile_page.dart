// import 'package:flutter/material.dart';
// import 'package:job/uttilits/color_const.dart';
// import 'package:job/view/common/welcome_screen/welcome_screen.dart';
// import 'package:job/view/salonowner2/detils/salon_details_screen.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:job/services/api_service.dart';

// class SaloonprofilePage extends StatefulWidget {
//   const SaloonprofilePage({super.key});

//   @override
//   State<SaloonprofilePage> createState() => _SaloonprofilePageState();
// }

// class _SaloonprofilePageState extends State<SaloonprofilePage> {
//   bool _showTitle = false;
//   final ScrollController _scrollController = ScrollController();
//   bool _isLoading = true;
//   String _salonName = "Salon";
//   String _location = "Location";
//   String? _salonImage;

//   @override
//   void initState() {
//     super.initState();
//     _scrollController.addListener(_onScroll);
//     _loadSalonProfile();
//   }

//   Future<void> _loadSalonProfile() async {
//     try {
//       setState(() {
//         _isLoading = true;
//       });

//       final profileData = await ApiService.fetchSalonProfile();
//       print('Salon profile data: $profileData'); // Debug print
//       print('Profile picture field: ${profileData['profile_picture']}');
//       print('Salon images field: ${profileData['salon_images']}');

//       try {
//         setState(() {
//           _salonName = profileData['salon_name'] ?? "Salon";
//           _location = profileData['location'] ?? "Location";

//           // Try multiple possible field names for salon image
//           _salonImage = profileData['profile_picture'] ??
//                   profileData['salon_image'] ??
//                   profileData['logo'] ??
//                   (profileData['salon_images'] != null &&
//                       (profileData['salon_images'] as List).isNotEmpty)
//               ? (profileData['salon_images'][0] is String
//                   ? profileData['salon_images'][0]
//                   : profileData['salon_images'][0]['image'])
//               : null;

//           print('Salon image set to: $_salonImage'); // Debug print
//           print('Salon name set to: $_salonName'); // Debug print
//           print('Location set to: $_location'); // Debug print
//           _isLoading = false;
//         });
//       } catch (e) {
//         print('Error in setState: $e');
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     } catch (e) {
//       print('Error loading salon profile: $e');
//       setState(() {
//         _isLoading = false;
//       });
//     }
//   }

//   @override
//   void dispose() {
//     _scrollController.removeListener(_onScroll);
//     _scrollController.dispose();
//     super.dispose();
//   }

//   void _onScroll() {
//     if (_scrollController.offset > 180 && !_showTitle) {
//       setState(() => _showTitle = true);
//     } else if (_scrollController.offset <= 180 && _showTitle) {
//       setState(() => _showTitle = false);
//     }
//   }

//   void _handleLogout(BuildContext context) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           backgroundColor: Colors.white,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(20),
//           ),
//           title: const Row(
//             children: [
//               Icon(Icons.logout, color: Colors.red),
//               SizedBox(width: 8),
//               Text(
//                 'Logout',
//                 style: TextStyle(
//                   fontWeight: FontWeight.bold,
//                   color: Colors.red,
//                 ),
//               ),
//             ],
//           ),
//           content: const Text(
//             'Are you sure you want to logout from your account?',
//             style: TextStyle(fontSize: 16),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.pop(context),
//               child: Text(
//                 'Cancel',
//                 style: TextStyle(
//                   color: Colors.grey[600],
//                   fontWeight: FontWeight.w600,
//                 ),
//               ),
//             ),
//             ElevatedButton(
//               onPressed: () async {
//                 // Clear user session data
//                 try {
//                   final prefs = await SharedPreferences.getInstance();
//                   await prefs.setBool('is_logged_in', false);
//                   await prefs.remove('user_type');
//                   await prefs.remove('token');
//                   await prefs.remove('user_data');
//                 } catch (e) {
//                   print('Error clearing user data: $e');
//                 }

//                 Navigator.pop(context);

//                 // Show logout success message
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   const SnackBar(
//                     content: Text('Logged out successfully'),
//                     backgroundColor: ColorConstants.black,
//                   ),
//                 );

//                 Navigator.pushAndRemoveUntil(
//                   context,
//                   MaterialPageRoute(
//                     builder: (context) => const WelcomeScreen(),
//                   ),
//                   (route) => false,
//                 );
//               },
//               style: ElevatedButton.styleFrom(
//                 backgroundColor: Colors.red,
//                 shape: RoundedRectangleBorder(
//                   borderRadius: BorderRadius.circular(10),
//                 ),
//               ),
//               child: const Text(
//                 'Logout',
//                 style: TextStyle(
//                   fontWeight: FontWeight.w600,
//                   color: Colors.white,
//                 ),
//               ),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     final size = MediaQuery.of(context).size;

//     // Debug print to check if _salonImage is set
//     print('Build method - _salonImage: $_salonImage');

//     return Scaffold(
//       body: _isLoading
//           ? const Center(child: CircularProgressIndicator())
//           : CustomScrollView(
//               controller: _scrollController,
//               slivers: [
//                 // Enhanced App Bar
//                 SliverAppBar(
//                   expandedHeight: size.height * 0.28,
//                   floating: false,
//                   pinned: true,
//                   backgroundColor: ColorConstants.black,
//                   elevation: 0,
//                   leading: IconButton(
//                     icon: const Icon(
//                       Icons.arrow_back_ios_new,
//                       color: Colors.white,
//                     ),
//                     onPressed: () => Navigator.pop(context),
//                   ),
//                   title: _showTitle
//                       ? Text(
//                           _salonName,
//                           style: const TextStyle(
//                             color: Colors.white,
//                             fontSize: 20,
//                             fontWeight: FontWeight.w600,
//                           ),
//                         )
//                       : null,
//                   flexibleSpace: FlexibleSpaceBar(
//                     background: Stack(
//                       fit: StackFit.expand,
//                       children: [
//                         // Background with gradient overlay
//                         Container(
//                           decoration: BoxDecoration(
//                             gradient: LinearGradient(
//                               begin: Alignment.topCenter,
//                               end: Alignment.bottomCenter,
//                               colors: [
//                                 ColorConstants.black,
//                                 Colors.black.withOpacity(0.8),
//                               ],
//                             ),
//                           ),
//                         ),
//                         // Decorative pattern
//                         Opacity(
//                           opacity: 0.1,
//                           child: Container(decoration: const BoxDecoration()),
//                         ),
//                         // Profile Content
//                         Padding(
//                           padding: const EdgeInsets.symmetric(horizontal: 20),
//                           child: Column(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               // Salon Logo/Image
//                               Container(
//                                 width: 100,
//                                 height: 100,
//                                 decoration: BoxDecoration(
//                                   shape: BoxShape.circle,
//                                   // border: Border.all(
//                                   //   color: Colors.white,
//                                   //   width: 3,
//                                   // ),
//                                   boxShadow: [
//                                     BoxShadow(
//                                       color: Colors.black.withOpacity(0.2),
//                                       blurRadius: 10,
//                                       spreadRadius: 2,
//                                     ),
//                                   ],
//                                 ),
//                                 child: ClipOval(
//                                   child: _salonImage != null
//                                       ? Image.network(
//                                           _salonImage!,
//                                           fit: BoxFit.cover,
//                                           loadingBuilder: (context, child,
//                                               loadingProgress) {
//                                             if (loadingProgress == null)
//                                               return child;
//                                             return Container(
//                                               decoration: BoxDecoration(
//                                                 gradient: LinearGradient(
//                                                   colors: [
//                                                     Colors.grey[300]!,
//                                                     Colors.grey[400]!
//                                                   ],
//                                                   begin: Alignment.topLeft,
//                                                   end: Alignment.bottomRight,
//                                                 ),
//                                               ),
//                                               child: const Center(
//                                                 child:
//                                                     CircularProgressIndicator(
//                                                   color: Colors.white,
//                                                   strokeWidth: 2,
//                                                 ),
//                                               ),
//                                             );
//                                           },
//                                           errorBuilder:
//                                               (context, error, stackTrace) {
//                                             print(
//                                                 'Error loading salon image: $error');
//                                             print(
//                                                 'Image URL that failed: $_salonImage');
//                                             return _buildDefaultSalonImage();
//                                           },
//                                         )
//                                       : _buildDefaultSalonImage(),
//                                 ),
//                               ),
//                               const SizedBox(height: 16),
//                               // Salon Name
//                               Text(
//                                 _salonName,
//                                 style: const TextStyle(
//                                   color: Colors.white,
//                                   fontSize: 28,
//                                   fontWeight: FontWeight.bold,
//                                   letterSpacing: 0.5,
//                                 ),
//                               ),
//                               const SizedBox(height: 8),
//                               // Location with enhanced styling
//                               Container(
//                                 padding: const EdgeInsets.symmetric(
//                                   horizontal: 16,
//                                   vertical: 8,
//                                 ),
//                                 decoration: BoxDecoration(
//                                   color: Colors.white.withOpacity(0.15),
//                                   borderRadius: BorderRadius.circular(20),
//                                   border: Border.all(
//                                     color: Colors.white.withOpacity(0.3),
//                                     width: 1,
//                                   ),
//                                 ),
//                                 child: Row(
//                                   mainAxisSize: MainAxisSize.min,
//                                   children: [
//                                     const Icon(
//                                       Icons.location_on,
//                                       color: Colors.white,
//                                       size: 16,
//                                     ),
//                                     const SizedBox(width: 6),
//                                     Text(
//                                       _location,
//                                       style: const TextStyle(
//                                         color: Colors.white,
//                                         fontSize: 14,
//                                         fontWeight: FontWeight.w500,
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),

//                 // Salon Stats

//                 // Profile Menu with enhanced styling
//                 SliverList(
//                   delegate: SliverChildListDelegate([
//                     const SizedBox(height: 10),
//                     _buildMenuSection('Account', [
//                       _buildMenuItem(
//                         context,
//                         icon: Icons.business,
//                         title: 'Salon Details',
//                         subtitle: 'Update your salon information',
//                         iconColor: Colors.orange,
//                         onTap: () {
//                           Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                               builder: (context) => const SalonDetailsScreen(),
//                             ),
//                           );
//                         },
//                       ),
//                     ]),
//                     _buildMenuSection('Settings', [
//                       _buildMenuItem(
//                         context,
//                         icon: Icons.notifications_outlined,
//                         title: 'Notifications',
//                         subtitle: 'Manage your notifications',
//                         iconColor: Colors.purple,
//                       ),
//                     ]),
//                     _buildMenuSection('Support', [
//                       _buildMenuItem(
//                         context,
//                         icon: Icons.logout,
//                         title: 'Logout',
//                         subtitle: 'Sign out from your account',
//                         iconColor: Colors.red,
//                         onTap: () => _handleLogout(context),
//                       ),
//                     ]),
//                     const SizedBox(height: 70),
//                   ]),
//                 ),
//               ],
//             ),
//     );
//   }

//   Widget _buildMenuSection(String title, List<Widget> items) {
//     return Container(
//       margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.circular(16),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withOpacity(0.05),
//             blurRadius: 10,
//             spreadRadius: 0,
//             offset: const Offset(0, 5),
//           ),
//         ],
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Padding(
//             padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
//             child: Text(
//               title,
//               style: const TextStyle(
//                 fontSize: 18,
//                 fontWeight: FontWeight.bold,
//                 color: ColorConstants.black,
//               ),
//             ),
//           ),
//           ...items,
//         ],
//       ),
//     );
//   }

//   Widget _buildMenuItem(
//     BuildContext context, {
//     required IconData icon,
//     required String title,
//     required String subtitle,
//     required Color iconColor,
//     VoidCallback? onTap,
//   }) {
//     return ListTile(
//       contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
//       leading: Container(
//         padding: const EdgeInsets.all(10),
//         decoration: BoxDecoration(
//           color: iconColor.withOpacity(0.1),
//           borderRadius: BorderRadius.circular(12),
//         ),
//         child: Icon(icon, color: iconColor, size: 24),
//       ),
//       title: Text(
//         title,
//         style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
//       ),
//       subtitle: Padding(
//         padding: const EdgeInsets.only(top: 4),
//         child: Text(
//           subtitle,
//           style: TextStyle(fontSize: 14, color: Colors.grey[600]),
//         ),
//       ),
//       trailing: Container(
//         padding: const EdgeInsets.all(8),
//         decoration: BoxDecoration(
//           color: Colors.grey[100],
//           shape: BoxShape.circle,
//         ),
//         child: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
//       ),
//       onTap: onTap,
//     );
//   }

//   Widget _buildDefaultSalonImage() {
//     return Container(
//       decoration: BoxDecoration(
//         gradient: LinearGradient(
//           colors: [Colors.grey[300]!, Colors.grey[400]!],
//           begin: Alignment.topLeft,
//           end: Alignment.bottomRight,
//         ),
//       ),
//       child: ClipOval(
//         child: Image.asset(
//           'assets/images/mountvoq_logo-removebg-preview.png',
//           fit: BoxFit.cover,
//           errorBuilder: (context, error, stackTrace) => Container(
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [Colors.grey[300]!, Colors.grey[400]!],
//                 begin: Alignment.topLeft,
//                 end: Alignment.bottomRight,
//               ),
//             ),
//             child: const Icon(
//               Icons.business,
//               color: Colors.white,
//               size: 50,
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
