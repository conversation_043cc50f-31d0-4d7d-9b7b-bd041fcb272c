{"flutterTools.displayGetxContextMenu": false, "flutterTools.displayMobxContextMenu": false, "flutterTools.displayRiverpodContextMenu": false, "flutterTools.displayModularContextMenu": false, "java.configuration.updateBuildConfiguration": "interactive", "cSpell.words": ["Jobseekeredit", "Saloonhome", "Saloonprofile"], "cmake.sourceDirectory": "/Users/<USER>/Documents/vscode docs/saloon_app/mountvoq 2/linux"}