# MountVoq API Documentation

## 📋 Table of Contents
1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Base URL & Headers](#base-url--headers)
4. [Error Handling](#error-handling)
5. [Endpoints](#endpoints)
6. [Data Models](#data-models)
7. [Examples](#examples)

## 🌐 Overview

The MountVoq API is a RESTful service built with Django REST Framework that provides endpoints for salon management, job posting, and user authentication.

**Base URL**: `http://************:8000/api`

**Content Type**: `application/json`

## 🔐 Authentication

The API uses Token-based authentication. Include the token in the Authorization header:

```
Authorization: Token <your_token_here>
```

### Getting a Token
1. Register or login to get a token
2. Include the token in subsequent requests
3. Tokens expire after 24 hours

## 📡 Base URL & Headers

### Base URL
```
http://************:8000/api
```

### Common Headers
```http
Content-Type: application/json
Accept: application/json
Authorization: Token <token>  # For authenticated requests
```

## ⚠️ Error Handling

### Error Response Format
```json
{
  "error": "Error message",
  "status_code": 400,
  "details": {
    "field_name": ["Specific error message"]
  }
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## 🔗 Endpoints

### Authentication Endpoints

#### 1. Login
**POST** `/api/login/`

**Request Body:**
```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "token": "your_auth_token_here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "name": "User Name",
    "user_type": "salon_owner"
  }
}
```

#### 2. Register Salon Owner
**POST** `/api/register/admin/`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "password2": "password123",
  "salon_name": "Beauty Salon",
  "owner_name": "John Doe",
  "location": "123 Main Street",
  "pin_code": "123456",
  "google_map_link": "https://maps.google.com/...",
  "contact_no": "**********",
  "salon_type": "UNISEX"
}
```

**Response:**
```json
{
  "token": "your_auth_token_here",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "salon_name": "Beauty Salon",
    "user_type": "salon_owner"
  }
}
```

#### 3. Register Job Seeker
**POST** `/api/register/jobseeker/`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "password2": "password123",
  "name": "Jane Smith",
  "contact_no": "**********",
  "gender": "FEMALE",
  "date_of_birth": "1990-01-01",
  "place": "New York",
  "expertise_areas": [1, 2, 3],
  "years_of_experience": "5",
  "social_link": "https://linkedin.com/janesmith"
}
```

**Response:**
```json
{
  "token": "your_auth_token_here",
  "user": {
    "id": 2,
    "email": "<EMAIL>",
    "name": "Jane Smith",
    "user_type": "jobseeker"
  }
}
```

#### 4. Forgot Password
**POST** `/api/forgot-password/`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "OTP sent to your email"
}
```

#### 5. Verify OTP
**POST** `/api/verify-otp/`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Response:**
```json
{
  "message": "OTP verified successfully"
}
```

#### 6. Reset Password
**POST** `/api/reset-password/`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456",
  "new_password": "newpassword123",
  "confirm_password": "newpassword123"
}
```

**Response:**
```json
{
  "message": "Password reset successfully"
}
```

### Salon Profile Endpoints

#### 1. Get Salon Profile
**GET** `/api/salon-profile/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "id": 1,
  "salon_name": "Beauty Salon",
  "owner_name": "John Doe",
  "location": "123 Main Street",
  "pin_code": "123456",
  "google_map_link": "https://maps.google.com/...",
  "contact_no": "**********",
  "salon_type": "UNISEX",
  "about": "Professional beauty salon",
  "profile_picture": "http://************:8000/media/salon_profile.jpg",
  "uploaded_images": [
    "http://************:8000/media/salon_image1.jpg",
    "http://************:8000/media/salon_image2.jpg"
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 2. Update Salon Profile
**PATCH** `/api/salon-profile/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
```
salon_name: Updated Salon Name
owner_name: John Doe
location: 456 New Street
pin_code: 654321
google_map_link: https://maps.google.com/...
contact_no: 0987654321
salon_type: FEMALE
about: Updated salon description
profile_picture: [file]
uploaded_images: [files]
```

**Response:**
```json
{
  "id": 1,
  "salon_name": "Updated Salon Name",
  "owner_name": "John Doe",
  "location": "456 New Street",
  "pin_code": "654321",
  "google_map_link": "https://maps.google.com/...",
  "contact_no": "0987654321",
  "salon_type": "FEMALE",
  "about": "Updated salon description",
  "profile_picture": "http://************:8000/media/new_profile.jpg",
  "uploaded_images": [
    "http://************:8000/media/new_image1.jpg",
    "http://************:8000/media/new_image2.jpg"
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-02T00:00:00Z"
}
```

#### 3. Upload Salon Images
**POST** `/api/salon-profile/images/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
```
uploaded_images: [files]
```

**Response:**
```json
{
  "message": "Images uploaded successfully",
  "images": [
    "http://************:8000/media/salon_image1.jpg",
    "http://************:8000/media/salon_image2.jpg"
  ]
}
```

#### 4. Delete Salon Image
**DELETE** `/api/salon-image/{image_id}/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "message": "Image deleted successfully"
}
```

### Job Management Endpoints

#### 1. Create Job
**POST** `/api/jobs/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Senior Hair Stylist",
  "location": "New York",
  "job_type": "FULL_TIME",
  "job_summary": "We are looking for an experienced hair stylist",
  "benefits": [
    "Health insurance",
    "Paid time off",
    "Professional development"
  ],
  "commute_info": "Easy access via subway",
  "experience": "3+ years experience required",
  "gender": "ANY",
  "is_active": true
}
```

**Response:**
```json
{
  "id": 1,
  "title": "Senior Hair Stylist",
  "location": "New York",
  "job_type": "FULL_TIME",
  "job_summary": "We are looking for an experienced hair stylist",
  "benefits": [
    "Health insurance",
    "Paid time off",
    "Professional development"
  ],
  "commute_info": "Easy access via subway",
  "experience": "3+ years experience required",
  "gender": "ANY",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 2. Get Jobs (Salon Owner)
**GET** `/api/salon-jobs/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "title": "Senior Hair Stylist",
    "location": "New York",
    "job_type": "FULL_TIME",
    "job_summary": "We are looking for an experienced hair stylist",
    "benefits": [
      "Health insurance",
      "Paid time off",
      "Professional development"
    ],
    "commute_info": "Easy access via subway",
    "experience": "3+ years experience required",
    "gender": "ANY",
    "is_active": true,
    "applications_count": 5,
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 3. Get Jobs (Job Seeker)
**GET** `/api/jobs/`

**Headers:**
```http
Authorization: Token <token>
```

**Query Parameters:**
- `location` (optional): Filter by location
- `job_type` (optional): Filter by job type (FULL_TIME, PART_TIME)
- `gender` (optional): Filter by gender requirement (MALE, FEMALE, ANY)

**Response:**
```json
[
  {
    "id": 1,
    "title": "Senior Hair Stylist",
    "location": "New York",
    "job_type": "FULL_TIME",
    "job_summary": "We are looking for an experienced hair stylist",
    "benefits": [
      "Health insurance",
      "Paid time off",
      "Professional development"
    ],
    "commute_info": "Easy access via subway",
    "experience": "3+ years experience required",
    "gender": "ANY",
    "salon_name": "Beauty Salon",
    "salon_location": "123 Main Street",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 4. Get Job Details
**GET** `/api/jobs/{job_id}/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "id": 1,
  "title": "Senior Hair Stylist",
  "location": "New York",
  "job_type": "FULL_TIME",
  "job_summary": "We are looking for an experienced hair stylist",
  "benefits": [
    "Health insurance",
    "Paid time off",
    "Professional development"
  ],
  "commute_info": "Easy access via subway",
  "experience": "3+ years experience required",
  "gender": "ANY",
  "is_active": true,
  "salon": {
    "id": 1,
    "salon_name": "Beauty Salon",
    "owner_name": "John Doe",
    "location": "123 Main Street",
    "contact_no": "**********",
    "salon_type": "UNISEX",
    "profile_picture": "http://************:8000/media/salon_profile.jpg"
  },
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 5. Update Job
**PATCH** `/api/jobs/{job_id}/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Updated Job Title",
  "is_active": false
}
```

**Response:**
```json
{
  "id": 1,
  "title": "Updated Job Title",
  "location": "New York",
  "job_type": "FULL_TIME",
  "job_summary": "We are looking for an experienced hair stylist",
  "benefits": [
    "Health insurance",
    "Paid time off",
    "Professional development"
  ],
  "commute_info": "Easy access via subway",
  "experience": "3+ years experience required",
  "gender": "ANY",
  "is_active": false,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-02T00:00:00Z"
}
```

#### 6. Delete Job
**DELETE** `/api/jobs/{job_id}/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "message": "Job deleted successfully"
}
```

### Application Management Endpoints

#### 1. Create Application
**POST** `/api/applications/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "job_id": 1,
  "cover_letter": "I am interested in this position and have relevant experience."
}
```

**Response:**
```json
{
  "id": 1,
  "job": {
    "id": 1,
    "title": "Senior Hair Stylist",
    "salon_name": "Beauty Salon"
  },
  "cover_letter": "I am interested in this position and have relevant experience.",
  "status": "PENDING",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 2. Get Applications (Salon Owner)
**GET** `/api/applications/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "job": {
      "id": 1,
      "title": "Senior Hair Stylist"
    },
    "applicant": {
      "id": 2,
      "name": "Jane Smith",
      "profile_picture": "http://************:8000/media/profile.jpg",
      "experience": "5 years",
      "specializations": ["Hair Styling", "Color"]
    },
    "cover_letter": "I am interested in this position and have relevant experience.",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 3. Get Applications (Job Seeker)
**GET** `/api/applications/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "job": {
      "id": 1,
      "title": "Senior Hair Stylist",
      "salon_name": "Beauty Salon",
      "location": "New York"
    },
    "cover_letter": "I am interested in this position and have relevant experience.",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 4. Update Application Status
**PATCH** `/api/applications/{application_id}/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "ACCEPTED"
}
```

**Response:**
```json
{
  "id": 1,
  "job": {
    "id": 1,
    "title": "Senior Hair Stylist"
  },
  "applicant": {
    "id": 2,
    "name": "Jane Smith"
  },
  "cover_letter": "I am interested in this position and have relevant experience.",
  "status": "ACCEPTED",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-02T00:00:00Z"
}
```

### Invitation Management Endpoints

#### 1. Send Invitation
**POST** `/api/salon-invitations/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "jobseeker_id": 2,
  "job_id": 1,
  "message": "We would like to invite you for an interview."
}
```

**Response:**
```json
{
  "id": 1,
  "jobseeker": {
    "id": 2,
    "name": "Jane Smith",
    "profile_picture": "http://************:8000/media/profile.jpg"
  },
  "job": {
    "id": 1,
    "title": "Senior Hair Stylist"
  },
  "message": "We would like to invite you for an interview.",
  "status": "PENDING",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 2. Get Invitations (Salon Owner)
**GET** `/api/salon-invitations/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "jobseeker": {
      "id": 2,
      "name": "Jane Smith",
      "profile_picture": "http://************:8000/media/profile.jpg",
      "experience": "5 years"
    },
    "job": {
      "id": 1,
      "title": "Senior Hair Stylist"
    },
    "message": "We would like to invite you for an interview.",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 3. Get Invitations (Job Seeker)
**GET** `/api/salon-invitations/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "salon": {
      "id": 1,
      "salon_name": "Beauty Salon",
      "profile_picture": "http://************:8000/media/salon_profile.jpg"
    },
    "job": {
      "id": 1,
      "title": "Senior Hair Stylist",
      "location": "New York"
    },
    "message": "We would like to invite you for an interview.",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 4. Update Invitation Status
**PATCH** `/api/salon-invitations/{invitation_id}/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "ACCEPTED"
}
```

**Response:**
```json
{
  "id": 1,
  "jobseeker": {
    "id": 2,
    "name": "Jane Smith"
  },
  "job": {
    "id": 1,
    "title": "Senior Hair Stylist"
  },
  "message": "We would like to invite you for an interview.",
  "status": "ACCEPTED",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-02T00:00:00Z"
}
```

### Request Management Endpoints

#### 1. Create Joining Request
**POST** `/api/joining-requests/create/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "job_id": 1,
  "message": "I would like to join your salon team."
}
```

**Response:**
```json
{
  "id": 1,
  "job": {
    "id": 1,
    "title": "Senior Hair Stylist",
    "salon_name": "Beauty Salon"
  },
  "message": "I would like to join your salon team.",
  "status": "PENDING",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 2. Get Salon Requests (Salon Owner)
**GET** `/api/salon-requests-list/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "jobseeker": {
      "id": 2,
      "name": "Jane Smith",
      "profile_picture": "http://************:8000/media/profile.jpg",
      "experience": "5 years",
      "specializations": ["Hair Styling", "Color"]
    },
    "job": {
      "id": 1,
      "title": "Senior Hair Stylist"
    },
    "message": "I would like to join your salon team.",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 3. Get Salon Requests (Job Seeker)
**GET** `/api/salon-requests-list/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "salon": {
      "id": 1,
      "salon_name": "Beauty Salon",
      "profile_picture": "http://************:8000/media/salon_profile.jpg"
    },
    "job": {
      "id": 1,
      "title": "Senior Hair Stylist",
      "location": "New York"
    },
    "message": "I would like to join your salon team.",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 4. Delete Salon Request
**DELETE** `/api/salon-requests/{request_id}/delete/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "message": "Request deleted successfully"
}
```

### Job Seeker Profile Endpoints

#### 1. Get Job Seeker Profile
**GET** `/api/jobseeker-profile/me/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "id": 2,
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "contact_no": "**********",
  "gender": "FEMALE",
  "date_of_birth": "1990-01-01",
  "place": "New York",
  "expertise_areas": [
    {
      "id": 1,
      "name": "Hair Styling"
    },
    {
      "id": 2,
      "name": "Hair Color"
    }
  ],
  "years_of_experience": "5",
  "social_link": "https://linkedin.com/janesmith",
  "profile_picture": "http://************:8000/media/profile.jpg",
  "work_pictures": [
    "http://************:8000/media/work1.jpg",
    "http://************:8000/media/work2.jpg"
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 2. Update Job Seeker Profile
**PATCH** `/api/jobseeker-profile/me/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
```
name: Updated Name
contact_no: 0987654321
place: Los Angeles
years_of_experience: 7
social_link: https://linkedin.com/updatedprofile
profile_picture: [file]
work_pictures: [files]
```

**Response:**
```json
{
  "id": 2,
  "name": "Updated Name",
  "email": "<EMAIL>",
  "contact_no": "0987654321",
  "gender": "FEMALE",
  "date_of_birth": "1990-01-01",
  "place": "Los Angeles",
  "expertise_areas": [
    {
      "id": 1,
      "name": "Hair Styling"
    },
    {
      "id": 2,
      "name": "Hair Color"
    }
  ],
  "years_of_experience": "7",
  "social_link": "https://linkedin.com/updatedprofile",
  "profile_picture": "http://************:8000/media/new_profile.jpg",
  "work_pictures": [
    "http://************:8000/media/new_work1.jpg",
    "http://************:8000/media/new_work2.jpg"
  ],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-02T00:00:00Z"
}
```

#### 3. Upload Work Pictures
**POST** `/api/jobseeker-profile/work-pictures/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
```
work_pictures: [files]
```

**Response:**
```json
{
  "message": "Work pictures uploaded successfully",
  "pictures": [
    "http://************:8000/media/work1.jpg",
    "http://************:8000/media/work2.jpg"
  ]
}
```

### Analytics Endpoints

#### 1. Customer Visits
**GET** `/api/analytics/customer-visits/`

**Headers:**
```http
Authorization: Token <token>
```

**Query Parameters:**
- `start_date` (optional): Start date (YYYY-MM-DD)
- `end_date` (optional): End date (YYYY-MM-DD)

**Response:**
```json
[
  {
    "id": 1,
    "date": "2024-01-01",
    "customer_count": 25,
    "revenue": 1250.00,
    "services": [
      {
        "name": "Hair Cut",
        "count": 15,
        "revenue": 750.00
      },
      {
        "name": "Hair Color",
        "count": 10,
        "revenue": 500.00
      }
    ]
  }
]
```

#### 2. Add Customer Visit
**POST** `/api/analytics/customer-visits/`

**Headers:**
```http
Authorization: Token <token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "date": "2024-01-01",
  "customer_count": 25,
  "revenue": 1250.00,
  "services": [
    {
      "name": "Hair Cut",
      "count": 15,
      "revenue": 750.00
    },
    {
      "name": "Hair Color",
      "count": 10,
      "revenue": 500.00
    }
  ]
}
```

**Response:**
```json
{
  "id": 1,
  "date": "2024-01-01",
  "customer_count": 25,
  "revenue": 1250.00,
  "services": [
    {
      "name": "Hair Cut",
      "count": 15,
      "revenue": 750.00
    },
    {
      "name": "Hair Color",
      "count": 10,
      "revenue": 500.00
    }
  ],
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 3. Operational Expenses
**GET** `/api/analytics/operational-expenses/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "date": "2024-01-01",
    "category": "Rent",
    "amount": 2000.00,
    "description": "Monthly rent payment"
  },
  {
    "id": 2,
    "date": "2024-01-01",
    "category": "Utilities",
    "amount": 300.00,
    "description": "Electricity and water bills"
  }
]
```

#### 4. Product Purchases
**GET** `/api/analytics/product-purchases/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "date": "2024-01-01",
    "product_name": "Shampoo",
    "quantity": 50,
    "unit_price": 10.00,
    "total_amount": 500.00,
    "supplier": "Beauty Supplies Co."
  }
]
```

### Blog Endpoints

#### 1. Training Blogs
**GET** `/api/blogs/training/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "title": "Advanced Hair Coloring Techniques",
    "content": "Learn the latest hair coloring techniques...",
    "image": "http://************:8000/media/training1.jpg",
    "author": "Professional Trainer",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 2. Training Blog Detail
**GET** `/api/blogs/training/{blog_id}/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
{
  "id": 1,
  "title": "Advanced Hair Coloring Techniques",
  "content": "Learn the latest hair coloring techniques...",
  "image": "http://************:8000/media/training1.jpg",
  "author": "Professional Trainer",
  "video_url": "https://youtube.com/watch?v=...",
  "created_at": "2024-01-01T00:00:00Z"
}
```

#### 3. Consultations
**GET** `/api/blogs/consultations/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "title": "Hair Care Consultation",
    "description": "Professional hair care advice",
    "image": "http://************:8000/media/consultation1.jpg",
    "price": 50.00,
    "duration": "60 minutes",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 4. Products
**GET** `/api/blogs/products/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Professional Hair Shampoo",
    "description": "High-quality hair shampoo for all hair types",
    "image": "http://************:8000/media/product1.jpg",
    "price": 25.00,
    "category": "Hair Care",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

#### 5. Trending Posts
**GET** `/api/blogs/trending/`

**Headers:**
```http
Authorization: Token <token>
```

**Response:**
```json
[
  {
    "id": 1,
    "title": "Latest Hair Trends 2024",
    "content": "Discover the hottest hair trends...",
    "image": "http://************:8000/media/trending1.jpg",
    "views": 1500,
    "likes": 120,
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

## 📊 Data Models

### User Model
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "User Name",
  "user_type": "salon_owner|jobseeker",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Salon Profile Model
```json
{
  "id": 1,
  "salon_name": "Beauty Salon",
  "owner_name": "John Doe",
  "location": "123 Main Street",
  "pin_code": "123456",
  "google_map_link": "https://maps.google.com/...",
  "contact_no": "**********",
  "salon_type": "MALE|FEMALE|UNISEX",
  "about": "Salon description",
  "profile_picture": "http://************:8000/media/profile.jpg",
  "uploaded_images": ["http://************:8000/media/image1.jpg"],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Job Model
```json
{
  "id": 1,
  "title": "Job Title",
  "location": "Location",
  "job_type": "FULL_TIME|PART_TIME",
  "job_summary": "Job description",
  "benefits": ["Benefit 1", "Benefit 2"],
  "commute_info": "Commute details",
  "experience": "Experience required",
  "gender": "MALE|FEMALE|ANY",
  "is_active": true,
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### Application Model
```json
{
  "id": 1,
  "job": {
    "id": 1,
    "title": "Job Title",
    "salon_name": "Salon Name"
  },
  "applicant": {
    "id": 2,
    "name": "Applicant Name",
    "profile_picture": "http://************:8000/media/profile.jpg"
  },
  "cover_letter": "Cover letter text",
  "status": "PENDING|ACCEPTED|REJECTED",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

## 💡 Examples

### Complete Authentication Flow

```bash
# 1. Register a salon owner
curl -X POST http://************:8000/api/register/admin/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "password2": "password123",
    "salon_name": "Beauty Salon",
    "owner_name": "John Doe",
    "location": "123 Main Street",
    "pin_code": "123456",
    "google_map_link": "https://maps.google.com/...",
    "contact_no": "**********",
    "salon_type": "UNISEX"
  }'

# Response includes token
{
  "token": "your_auth_token_here",
  "user": {...}
}

# 2. Use token for authenticated requests
curl -X GET http://************:8000/api/salon-profile/ \
  -H "Authorization: Token your_auth_token_here"
```

### File Upload Example

```bash
# Upload salon images
curl -X POST http://************:8000/api/salon-profile/images/ \
  -H "Authorization: Token your_auth_token_here" \
  -F "uploaded_images=@image1.jpg" \
  -F "uploaded_images=@image2.jpg"
```

### Error Handling Example

```bash
# Invalid login attempt
curl -X POST http://************:8000/api/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "wrongpassword"
  }'

# Response
{
  "error": "Invalid credentials",
  "status_code": 400
}
```

---

**API Version**: 1.0.0  
**Last Updated**: December 2024  
**Base URL**: http://************:8000/api 