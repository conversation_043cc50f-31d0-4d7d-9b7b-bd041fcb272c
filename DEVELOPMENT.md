# MountVoq Development Guide

## 🛠️ Development Environment Setup

### Required Tools
- **Flutter SDK**: 3.0.0 or higher
- **Dart SDK**: 3.0.0 or higher
- **IDE**: VS Code or Android Studio
- **Git**: Version control
- **Postman/Insomnia**: API testing

### IDE Configuration

#### VS Code Extensions
```json
{
  "recommendations": [
    "Dart-Code.dart-code",
    "Dart-Code.flutter",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode"
  ]
}
```

#### Android Studio Plugins
- Flutter Plugin
- Dart Plugin
- Material Theme UI

### Environment Variables
Create a `.env` file in the project root:
```env
# API Configuration
API_BASE_URL=http://192.168.1.40:8000/api
API_TIMEOUT=30000

# App Configuration
APP_NAME=MountVoq
APP_VERSION=1.0.0

# Debug Configuration
DEBUG_MODE=true
LOG_LEVEL=debug
```

## 📁 Code Organization

### Directory Structure Guidelines

```
lib/
├── core/                    # Core functionality
│   ├── constants/          # App constants
│   ├── errors/             # Error handling
│   ├── network/            # Network utilities
│   └── utils/              # General utilities
├── data/                   # Data layer
│   ├── models/             # Data models
│   ├── repositories/       # Data repositories
│   └── datasources/        # Data sources
├── domain/                 # Business logic
│   ├── entities/           # Business entities
│   ├── repositories/       # Repository interfaces
│   └── usecases/           # Business use cases
├── presentation/           # UI layer
│   ├── pages/             # Screen pages
│   ├── widgets/            # Reusable widgets
│   ├── controllers/        # UI controllers
│   └── themes/             # App themes
└── main.dart              # App entry point
```

### File Naming Conventions

#### Dart Files
- **snake_case** for file names
- **PascalCase** for class names
- **camelCase** for variables and methods

#### Examples
```
✅ Correct:
- user_profile_screen.dart
- api_service.dart
- salon_owner_model.dart

❌ Incorrect:
- UserProfileScreen.dart
- ApiService.dart
- salonOwnerModel.dart
```

## 🎨 UI/UX Guidelines

### Design System

#### Colors
```dart
// Primary Colors
static const Color primaryColor = Color(0xFF2196F3);
static const Color primaryDarkColor = Color(0xFF1976D2);
static const Color primaryLightColor = Color(0xFFBBDEFB);

// Secondary Colors
static const Color secondaryColor = Color(0xFFFF9800);
static const Color secondaryDarkColor = Color(0xFFF57C00);

// Neutral Colors
static const Color backgroundColor = Color(0xFFF5F5F5);
static const Color surfaceColor = Color(0xFFFFFFFF);
static const Color textPrimaryColor = Color(0xFF212121);
static const Color textSecondaryColor = Color(0xFF757575);
```

#### Typography
```dart
// Text Styles
static const TextStyle heading1 = TextStyle(
  fontSize: 24,
  fontWeight: FontWeight.bold,
  color: textPrimaryColor,
);

static const TextStyle heading2 = TextStyle(
  fontSize: 20,
  fontWeight: FontWeight.w600,
  color: textPrimaryColor,
);

static const TextStyle bodyText = TextStyle(
  fontSize: 16,
  fontWeight: FontWeight.normal,
  color: textPrimaryColor,
);

static const TextStyle caption = TextStyle(
  fontSize: 14,
  fontWeight: FontWeight.normal,
  color: textSecondaryColor,
);
```

#### Spacing
```dart
// Spacing Constants
class Spacing {
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
}
```

### Widget Guidelines

#### Reusable Widgets
```dart
// Custom Button Widget
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final Color? backgroundColor;
  final Color? textColor;

  const CustomButton({
    Key? key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? primaryColor,
        foregroundColor: textColor ?? Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: Spacing.lg,
          vertical: Spacing.md,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(text),
    );
  }
}
```

#### Loading States
```dart
// Loading Widget
class LoadingWidget extends StatelessWidget {
  final String? message;

  const LoadingWidget({Key? key, this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: Spacing.md),
            Text(
              message!,
              style: caption,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
```

#### Error States
```dart
// Error Widget
class ErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const ErrorWidget({
    Key? key,
    required this.message,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: Spacing.md),
          Text(
            message,
            style: bodyText,
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: Spacing.lg),
            CustomButton(
              text: 'Retry',
              onPressed: onRetry!,
            ),
          ],
        ],
      ),
    );
  }
}
```

## 🔧 API Integration

### HTTP Service Pattern

```dart
class ApiService {
  static const String baseUrl = ApiConstants.baseUrl;
  static const Duration timeout = Duration(seconds: 30);

  static final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static Future<Map<String, String>> get _authHeaders async {
    final token = await SharedPreferences.getInstance()
        .then((prefs) => prefs.getString('auth_token'));
    
    if (token != null) {
      return {..._headers, 'Authorization': 'Token $token'};
    }
    return _headers;
  }

  static Future<http.Response> get(String endpoint) async {
    final headers = await _authHeaders;
    return http.get(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    ).timeout(timeout);
  }

  static Future<http.Response> post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final headers = await _authHeaders;
    return http.post(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
      body: jsonEncode(data),
    ).timeout(timeout);
  }

  static Future<http.Response> patch(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final headers = await _authHeaders;
    return http.patch(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
      body: jsonEncode(data),
    ).timeout(timeout);
  }

  static Future<http.Response> delete(String endpoint) async {
    final headers = await _authHeaders;
    return http.delete(
      Uri.parse('$baseUrl$endpoint'),
      headers: headers,
    ).timeout(timeout);
  }
}
```

### Error Handling

```dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  ApiException(this.message, {this.statusCode, this.data});

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

class ApiResponse<T> {
  final T? data;
  final String? message;
  final bool success;
  final int? statusCode;

  ApiResponse({
    this.data,
    this.message,
    required this.success,
    this.statusCode,
  });

  factory ApiResponse.success(T data) {
    return ApiResponse(
      data: data,
      success: true,
    );
  }

  factory ApiResponse.error(String message, {int? statusCode}) {
    return ApiResponse(
      message: message,
      success: false,
      statusCode: statusCode,
    );
  }
}
```

### Repository Pattern

```dart
abstract class UserRepository {
  Future<ApiResponse<User>> login(String email, String password);
  Future<ApiResponse<User>> register(Map<String, dynamic> userData);
  Future<ApiResponse<User>> getProfile();
  Future<ApiResponse<User>> updateProfile(Map<String, dynamic> data);
}

class UserRepositoryImpl implements UserRepository {
  @override
  Future<ApiResponse<User>> login(String email, String password) async {
    try {
      final response = await ApiService.post('/login/', {
        'username': email,
        'password': password,
      });

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final user = User.fromJson(data);
        
        // Save token
        await SharedPreferences.getInstance().then((prefs) {
          prefs.setString('auth_token', data['token']);
        });

        return ApiResponse.success(user);
      } else {
        final errorData = jsonDecode(response.body);
        return ApiResponse.error(
          errorData['message'] ?? 'Login failed',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      return ApiResponse.error('Network error: $e');
    }
  }

  // Implement other methods...
}
```

## 📱 State Management

### Provider Pattern

```dart
class AuthProvider extends ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  String? _error;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  Future<bool> login(String email, String password) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await UserRepositoryImpl().login(email, password);
      
      if (response.success) {
        _user = response.data;
        _error = null;
      } else {
        _error = response.message;
      }
    } catch (e) {
      _error = 'An unexpected error occurred';
    }

    _isLoading = false;
    notifyListeners();
    return _user != null;
  }

  Future<void> logout() async {
    await SharedPreferences.getInstance().then((prefs) {
      prefs.remove('auth_token');
    });
    _user = null;
    notifyListeners();
  }
}
```

### Local Storage

```dart
class LocalStorage {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _themeKey = 'app_theme';

  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  static Future<void> saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  static Future<User?> getUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(_userKey);
    if (userData != null) {
      return User.fromJson(jsonDecode(userData));
    }
    return null;
  }

  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
```

## 🧪 Testing

### Unit Tests

```dart
// test/services/api_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:http/http.dart' as http;

class MockHttpClient extends Mock implements http.Client {}

void main() {
  group('ApiService Tests', () {
    late MockHttpClient mockHttpClient;

    setUp(() {
      mockHttpClient = MockHttpClient();
    });

    test('should return user data when login is successful', () async {
      // Arrange
      when(mockHttpClient.post(
        any,
        headers: anyNamed('headers'),
        body: anyNamed('body'),
      )).thenAnswer((_) async => http.Response(
        '{"id": 1, "email": "<EMAIL>", "name": "Test User"}',
        200,
      ));

      // Act
      final result = await ApiService.login('<EMAIL>', 'password');

      // Assert
      expect(result.success, true);
      expect(result.data?.email, '<EMAIL>');
    });
  });
}
```

### Widget Tests

```dart
// test/widgets/login_screen_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

void main() {
  group('LoginScreen Widget Tests', () {
    testWidgets('should show login form', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => AuthProvider(),
            child: const LoginScreen(),
          ),
        ),
      );

      expect(find.byType(TextField), findsNWidgets(2));
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('Login'), findsOneWidget);
    });

    testWidgets('should show error message on invalid login', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (_) => AuthProvider(),
            child: const LoginScreen(),
          ),
        ),
      );

      await tester.enterText(find.byType(TextField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextField).last, 'wrongpassword');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      expect(find.text('Invalid credentials'), findsOneWidget);
    });
  });
}
```

### Integration Tests

```dart
// integration_test/app_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    testWidgets('complete user journey', (WidgetTester tester) async {
      // Launch app
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Navigate to login
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Enter credentials
      await tester.enterText(find.byType(TextField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextField).last, 'password');
      await tester.tap(find.text('Login'));
      await tester.pumpAndSettle();

      // Verify successful login
      expect(find.text('Welcome'), findsOneWidget);
    });
  });
}
```

## 🚀 Performance Optimization

### Image Optimization

```dart
// Optimized image loading
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;

  const OptimizedImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) => const Center(
        child: CircularProgressIndicator(),
      ),
      errorWidget: (context, url, error) => const Icon(
        Icons.error,
        color: Colors.red,
      ),
      memCacheWidth: 800, // Limit memory usage
      memCacheHeight: 800,
    );
  }
}
```

### List Optimization

```dart
// Optimized list with pagination
class OptimizedListView extends StatefulWidget {
  final Future<List<dynamic>> Function(int page) fetchData;
  final Widget Function(BuildContext, dynamic) itemBuilder;

  const OptimizedListView({
    Key? key,
    required this.fetchData,
    required this.itemBuilder,
  }) : super(key: key);

  @override
  State<OptimizedListView> createState() => _OptimizedListViewState();
}

class _OptimizedListViewState extends State<OptimizedListView> {
  final List<dynamic> _items = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    _loadMore();
  }

  Future<void> _loadMore() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newItems = await widget.fetchData(_currentPage);
      if (newItems.isEmpty) {
        _hasMore = false;
      } else {
        _items.addAll(newItems);
        _currentPage++;
      }
    } catch (e) {
      // Handle error
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          _loadMore();
        }
        return true;
      },
      child: ListView.builder(
        itemCount: _items.length + (_hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _items.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }
          return widget.itemBuilder(context, _items[index]);
        },
      ),
    );
  }
}
```

## 🔒 Security Guidelines

### Input Validation

```dart
class InputValidator {
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(email)) {
      return 'Please enter a valid email';
    }
    
    return null;
  }

  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }
    
    if (password.length < 8) {
      return 'Password must be at least 8 characters';
    }
    
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    
    return null;
  }

  static String? validatePhone(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'Phone number is required';
    }
    
    final phoneRegex = RegExp(r'^\+?[\d\s-]{10,}$');
    if (!phoneRegex.hasMatch(phone)) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }
}
```

### Secure Storage

```dart
class SecureStorage {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  static Future<void> saveToken(String token) async {
    await _storage.write(key: 'auth_token', value: token);
  }

  static Future<String?> getToken() async {
    return await _storage.read(key: 'auth_token');
  }

  static Future<void> deleteToken() async {
    await _storage.delete(key: 'auth_token');
  }

  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }
}
```

## 📝 Code Review Checklist

### General
- [ ] Code follows Dart/Flutter conventions
- [ ] Proper error handling implemented
- [ ] No hardcoded values
- [ ] Meaningful variable and function names
- [ ] Comments for complex logic

### UI/UX
- [ ] Responsive design implemented
- [ ] Loading states handled
- [ ] Error states handled
- [ ] Accessibility considerations
- [ ] Consistent styling

### Performance
- [ ] Efficient widget rebuilds
- [ ] Proper use of const constructors
- [ ] Image optimization
- [ ] Memory leak prevention
- [ ] Network request optimization

### Security
- [ ] Input validation
- [ ] Secure data storage
- [ ] API security
- [ ] Error message security

### Testing
- [ ] Unit tests written
- [ ] Widget tests written
- [ ] Integration tests written
- [ ] Test coverage adequate

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] All tests passing
- [ ] Code review completed
- [ ] Performance testing done
- [ ] Security audit completed
- [ ] Documentation updated

### Android
- [ ] Version code updated
- [ ] App signing configured
- [ ] ProGuard rules updated
- [ ] Permissions reviewed
- [ ] Play Store metadata ready

### iOS
- [ ] Version number updated
- [ ] App signing configured
- [ ] Info.plist reviewed
- [ ] App Store metadata ready
- [ ] Privacy policy updated

### Web
- [ ] Build optimized
- [ ] SEO metadata added
- [ ] Analytics configured
- [ ] CDN configured
- [ ] SSL certificate valid

---

**Last Updated**: December 2024  
**Version**: 1.0.0 