# MountVoq Deployment Guide

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Backend Deployment](#backend-deployment)
4. [Frontend Deployment](#frontend-deployment)
5. [Mobile App Deployment](#mobile-app-deployment)
6. [Web Deployment](#web-deployment)
7. [Production Configuration](#production-configuration)
8. [Monitoring & Maintenance](#monitoring--maintenance)
9. [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### Required Tools
- **Flutter SDK**: 3.0.0 or higher
- **Dart SDK**: 3.0.0 or higher
- **Git**: Version control
- **Docker**: Containerization (optional)
- **Cloud Platform Account**: AWS, Google Cloud, or Azure

### System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 10GB+ free space
- **OS**: Windows 10+, macOS 10.14+, or Ubuntu 18.04+

## 🌍 Environment Setup

### 1. Development Environment

```bash
# Clone the repository
git clone <repository-url>
cd mountvoq

# Install Flutter dependencies
flutter pub get

# Verify Flutter installation
flutter doctor

# Check available devices
flutter devices
```

### 2. Environment Variables

Create `.env` file in project root:

```env
# API Configuration
API_BASE_URL=https://your-production-api.com/api
API_TIMEOUT=30000

# App Configuration
APP_NAME=MountVoq
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# Debug Configuration
DEBUG_MODE=false
LOG_LEVEL=info

# Database Configuration
DATABASE_URL=postgresql://user:password@host:port/database

# File Storage
MEDIA_URL=https://your-production-api.com/media/
STATIC_URL=https://your-production-api.com/static/

# Security
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=true
```

### 3. Configuration Files

#### pubspec.yaml
```yaml
name: mountvoq
description: "MountVoq Salon Management Application"
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  # ... other dependencies

flutter:
  uses-material-design: true
  assets:
    - assets/images/
```

#### analysis_options.yaml
```yaml
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    - always_declare_return_types
    - avoid_empty_else
    - avoid_print
    - avoid_unused_constructor_parameters
    - await_only_futures
    - camel_case_types
    - cancel_subscriptions
    - constant_identifier_names
    - control_flow_in_finally
    - directives_ordering
    - empty_catches
    - empty_constructor_bodies
    - empty_statements
    - hash_and_equals
    - implementation_imports
    - library_names
    - library_prefixes
    - non_constant_identifier_names
    - package_api_docs
    - package_names
    - package_prefixed_library_names
    - prefer_const_constructors
    - prefer_final_fields
    - prefer_is_empty
    - prefer_is_not_empty
    - prefer_typing_uninitialized_variables
    - slash_for_doc_comments
    - test_types_in_equals
    - throw_in_finally
    - type_init_formals
    - unnecessary_brace_in_string_interps
    - unnecessary_getters_setters
    - unnecessary_new
    - unnecessary_null_aware_assignments
    - unnecessary_statements
    - unrelated_type_equality_checks
    - use_rethrow_when_possible
    - valid_regexps
```

## 🖥️ Backend Deployment

### 1. Django Backend Setup

#### Requirements
```txt
Django==4.2.0
djangorestframework==3.14.0
django-cors-headers==4.0.0
psycopg2-binary==2.9.6
Pillow==9.5.0
python-decouple==3.8
gunicorn==20.1.0
whitenoise==6.4.0
```

#### Settings Configuration
```python
# settings.py
import os
from decouple import config

DEBUG = config('DEBUG', default=False, cast=bool)
SECRET_KEY = config('SECRET_KEY')
ALLOWED_HOSTS = config('ALLOWED_HOSTS', default='').split(',')

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST'),
        'PORT': config('DB_PORT', default='5432'),
    }
}

# Static files
STATIC_URL = config('STATIC_URL', default='/static/')
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
MEDIA_URL = config('MEDIA_URL', default='/media/')
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "https://your-domain.com",
    "https://www.your-domain.com",
]

# Email configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST')
EMAIL_PORT = config('EMAIL_PORT', cast=int)
EMAIL_HOST_USER = config('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD')
EMAIL_USE_TLS = config('EMAIL_USE_TLS', cast=bool)
```

### 2. Database Setup

#### PostgreSQL Installation
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS
brew install postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

#### Database Creation
```sql
-- Connect to PostgreSQL
sudo -u postgres psql

-- Create database and user
CREATE DATABASE mountvoq_db;
CREATE USER mountvoq_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE mountvoq_db TO mountvoq_user;
ALTER USER mountvoq_user CREATEDB;
```

### 3. Backend Deployment Options

#### Option A: Traditional Server

```bash
# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic --noinput

# Create superuser
python manage.py createsuperuser

# Run with Gunicorn
gunicorn --bind 0.0.0.0:8000 mountvoq.wsgi:application
```

#### Option B: Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

RUN python manage.py collectstatic --noinput

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "mountvoq.wsgi:application"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: mountvoq_db
      POSTGRES_USER: mountvoq_user
      POSTGRES_PASSWORD: your_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  web:
    build: .
    command: gunicorn --bind 0.0.0.0:8000 mountvoq.wsgi:application
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DEBUG=False
      - DATABASE_URL=************************************************/mountvoq_db

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      - web

volumes:
  postgres_data:
  static_volume:
  media_volume:
```

#### Option C: Cloud Deployment (AWS)

```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS
aws configure

# Deploy to Elastic Beanstalk
eb init mountvoq
eb create mountvoq-production
eb deploy
```

## 📱 Frontend Deployment

### 1. Build Configuration

#### Release Build
```bash
# Clean previous builds
flutter clean

# Get dependencies
flutter pub get

# Build for release
flutter build apk --release
flutter build appbundle --release
flutter build ios --release
flutter build web --release
```

#### Build Variants
```bash
# Development build
flutter build apk --debug

# Profile build (for performance testing)
flutter build apk --profile

# Release build with specific flavor
flutter build apk --flavor production --release
```

### 2. Android Deployment

#### Generate Keystore
```bash
# Create keystore
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# Move to android/app/
mv ~/upload-keystore.jks android/app/
```

#### Configure Signing
```gradle
// android/app/build.gradle
android {
    ...
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

```properties
# android/key.properties
storePassword=<password from previous step>
keyPassword=<password from previous step>
keyAlias=upload
storeFile=upload-keystore.jks
```

#### Google Play Store Deployment
```bash
# Build app bundle
flutter build appbundle --release

# Upload to Google Play Console
# 1. Go to Google Play Console
# 2. Create new app or select existing
# 3. Upload app bundle
# 4. Fill in store listing
# 5. Set up content rating
# 6. Configure pricing & distribution
# 7. Submit for review
```

### 3. iOS Deployment

#### Apple Developer Account Setup
1. Enroll in Apple Developer Program
2. Create App ID in Developer Portal
3. Generate certificates and provisioning profiles
4. Configure Xcode project

#### Xcode Configuration
```swift
// ios/Runner/Info.plist
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDisplayName</key>
    <string>MountVoq</string>
    <key>CFBundleIdentifier</key>
    <string>com.mountvoq.app</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>NSCameraUsageDescription</key>
    <string>This app needs camera access to take profile pictures</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>This app needs photo library access to select images</string>
</dict>
</plist>
```

#### App Store Deployment
```bash
# Build iOS app
flutter build ios --release

# Archive in Xcode
# 1. Open ios/Runner.xcworkspace
# 2. Select Product > Archive
# 3. Upload to App Store Connect
# 4. Configure app metadata
# 5. Submit for review
```

### 4. Web Deployment

#### Build Web App
```bash
# Build for web
flutter build web --release

# Optimize for production
flutter build web --release --web-renderer html --dart-define=FLUTTER_WEB_USE_SKIA=true
```

#### Nginx Configuration
```nginx
# nginx.conf
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/mountvoq/web;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /media {
        proxy_pass http://localhost:8000;
    }

    location /static {
        proxy_pass http://localhost:8000;
    }

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

#### Cloud Deployment Options

##### Firebase Hosting
```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase
firebase init hosting

# Deploy
firebase deploy
```

##### Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Deploy
netlify deploy --prod --dir=build/web
```

##### Vercel
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod
```

## 🌐 Production Configuration

### 1. Environment Variables

#### Production .env
```env
# Production API Configuration
API_BASE_URL=https://api.mountvoq.com
API_TIMEOUT=30000

# App Configuration
APP_NAME=MountVoq
APP_VERSION=1.0.0
APP_BUILD_NUMBER=1

# Debug Configuration
DEBUG_MODE=false
LOG_LEVEL=error

# Database Configuration
DATABASE_URL=postgresql://user:password@host:port/database

# File Storage
MEDIA_URL=https://api.mountvoq.com/media/
STATIC_URL=https://api.mountvoq.com/static/

# Security
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=mountvoq.com,www.mountvoq.com,api.mountvoq.com

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=true

# SSL Configuration
SSL_CERTIFICATE_PATH=/etc/letsencrypt/live/mountvoq.com/fullchain.pem
SSL_PRIVATE_KEY_PATH=/etc/letsencrypt/live/mountvoq.com/privkey.pem
```

### 2. SSL Certificate Setup

#### Let's Encrypt
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d mountvoq.com -d www.mountvoq.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Database Backup

#### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="mountvoq_db"
DB_USER="mountvoq_user"

# Create backup
pg_dump -U $DB_USER $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/backup_$DATE.sql

# Remove old backups (keep last 7 days)
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

# Upload to cloud storage (optional)
aws s3 cp $BACKUP_DIR/backup_$DATE.sql.gz s3://mountvoq-backups/
```

#### Cron Job Setup
```bash
# Add to crontab
0 2 * * * /path/to/backup.sh
```

## 📊 Monitoring & Maintenance

### 1. Application Monitoring

#### Log Management
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/mountvoq/django.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO',
    },
}
```

#### Health Check Endpoint
```python
# views.py
from django.http import JsonResponse
from django.db import connection

def health_check(request):
    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)
```

### 2. Performance Monitoring

#### Django Debug Toolbar (Development)
```python
# settings.py
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
    INTERNAL_IPS = ['127.0.0.1']
```

#### Application Performance Monitoring
```python
# Install New Relic or similar
pip install newrelic

# newrelic.ini
[newrelic]
license_key = YOUR_LICENSE_KEY
app_name = MountVoq
```

### 3. Error Tracking

#### Sentry Integration
```python
# settings.py
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration

sentry_sdk.init(
    dsn="YOUR_SENTRY_DSN",
    integrations=[DjangoIntegration()],
    traces_sample_rate=1.0,
    send_default_pii=True
)
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk --release

# Check Flutter version
flutter --version

# Update Flutter
flutter upgrade
```

#### 2. API Connection Issues
```bash
# Test API connectivity
curl -X GET https://api.mountvoq.com/health/

# Check SSL certificate
openssl s_client -connect api.mountvoq.com:443

# Verify DNS resolution
nslookup api.mountvoq.com
```

#### 3. Database Connection Issues
```bash
# Test database connection
psql -h host -U user -d database

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log

# Restart PostgreSQL
sudo systemctl restart postgresql
```

#### 4. File Upload Issues
```bash
# Check file permissions
ls -la /path/to/media/

# Fix permissions
sudo chown -R www-data:www-data /path/to/media/
sudo chmod -R 755 /path/to/media/

# Check disk space
df -h
```

### Performance Optimization

#### 1. Database Optimization
```sql
-- Create indexes
CREATE INDEX idx_jobs_salon_id ON jobs(salon_id);
CREATE INDEX idx_applications_job_id ON applications(job_id);
CREATE INDEX idx_applications_status ON applications(status);

-- Analyze table statistics
ANALYZE jobs;
ANALYZE applications;
```

#### 2. Caching
```python
# settings.py
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}

# views.py
from django.core.cache import cache

def get_jobs(request):
    cache_key = f'jobs_{request.user.id}'
    jobs = cache.get(cache_key)
    
    if jobs is None:
        jobs = Job.objects.filter(salon=request.user.salon)
        cache.set(cache_key, jobs, 300)  # Cache for 5 minutes
    
    return JsonResponse({'jobs': jobs})
```

#### 3. Image Optimization
```python
# Install Pillow-SIMD for better performance
pip install Pillow-SIMD

# Optimize image uploads
from PIL import Image
import io

def optimize_image(image_file):
    img = Image.open(image_file)
    
    # Resize if too large
    if img.width > 1920 or img.height > 1080:
        img.thumbnail((1920, 1080), Image.LANCZOS)
    
    # Convert to JPEG if PNG
    if img.format == 'PNG':
        img = img.convert('RGB')
    
    # Save with compression
    output = io.BytesIO()
    img.save(output, format='JPEG', quality=85, optimize=True)
    output.seek(0)
    
    return output
```

### Security Checklist

- [ ] HTTPS enabled
- [ ] SSL certificate valid
- [ ] Environment variables secured
- [ ] Database credentials protected
- [ ] API rate limiting implemented
- [ ] Input validation in place
- [ ] SQL injection prevention
- [ ] XSS protection enabled
- [ ] CSRF protection enabled
- [ ] File upload validation
- [ ] Error messages sanitized
- [ ] Logs don't contain sensitive data
- [ ] Regular security updates
- [ ] Backup encryption enabled

---

**Deployment Version**: 1.0.0  
**Last Updated**: December 2024  
**Maintained By**: MountVoq Development Team 