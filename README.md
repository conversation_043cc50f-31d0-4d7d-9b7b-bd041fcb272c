# MountVoq - Salon Management Application

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Features](#features)
3. [Architecture](#architecture)
4. [Technology Stack](#technology-stack)
5. [Project Structure](#project-structure)
6. [Setup & Installation](#setup--installation)
7. [API Documentation](#api-documentation)
8. [User Roles & Workflows](#user-roles--workflows)
9. [Key Components](#key-components)
10. [Development Guidelines](#development-guidelines)
11. [Deployment](#deployment)
12. [Troubleshooting](#troubleshooting)

## 🎯 Project Overview

MountVoq is a comprehensive salon management application built with Flutter that connects salon owners with job seekers in the beauty and wellness industry. The application provides a complete ecosystem for job posting, application management, employee recruitment, and business analytics.

### Core Objectives
- **For Salon Owners**: Streamline hiring processes, manage employees, track business metrics
- **For Job Seekers**: Find salon opportunities, apply for jobs, manage applications
- **For Both**: Facilitate communication and professional networking

## ✨ Features

### 🏢 Salon Owner Features
- **Profile Management**: Complete salon profile with images and details
- **Job Posting**: Create and manage job vacancies
- **Employee Management**: Track hired employees and their performance
- **Recruitment Hub**: Find and invite professionals
- **Business Analytics**: Track income, expenses, and customer visits
- **Training Hub**: Access professional training resources
- **Consultation Services**: Professional consultation booking
- **Product Management**: Salon product catalog

### 👤 Job Seeker Features
- **Profile Management**: Professional profile with work pictures
- **Job Search**: Browse and apply for salon positions
- **Application Tracking**: Monitor application status
- **Invitation Management**: Respond to salon invitations
- **Request System**: Send requests to salons
- **Joining Requests**: Handle job offers and joining requests

### 🔧 System Features
- **Authentication**: Secure login/registration system
- **Real-time Updates**: Live status updates
- **File Management**: Image and document uploads
- **Push Notifications**: Status change notifications
- **Responsive Design**: Cross-platform compatibility

## 🏗️ Architecture

### Frontend Architecture
```
lib/
├── main.dart                 # Application entry point
├── services/                 # API services and business logic
├── view/                     # UI components and screens
│   ├── common/              # Shared components
│   ├── jobseeker/           # Job seeker specific screens
│   └── salonowner2/         # Salon owner specific screens
├── model/                   # Data models
├── controllers/             # State management
├── utils/                   # Utility functions
└── uttilits/               # Constants and configurations
```

### Backend Integration
- **RESTful API**: Django-based backend
- **Authentication**: Token-based authentication
- **File Storage**: Media file management
- **Database**: PostgreSQL with Django ORM

## 🛠️ Technology Stack

### Frontend
- **Framework**: Flutter 3.0+
- **Language**: Dart
- **State Management**: Provider/SetState
- **UI Components**: Material Design 3
- **HTTP Client**: http package
- **Local Storage**: SharedPreferences
- **Image Handling**: image_picker, file_picker
- **Charts**: fl_chart
- **Navigation**: ReorderableGridView

### Backend
- **Framework**: Django REST Framework
- **Database**: PostgreSQL
- **Authentication**: Django Token Authentication
- **File Storage**: Django Media Files
- **API**: RESTful endpoints

### Development Tools
- **IDE**: VS Code / Android Studio
- **Version Control**: Git
- **Package Manager**: pub (Flutter)
- **Build Tools**: Flutter CLI

## 📁 Project Structure

```
mountvoq/
├── lib/
│   ├── main.dart                    # App entry point
│   ├── services/
│   │   ├── api_service.dart         # Main API service
│   │   └── jobseeker_api_service.dart # Jobseeker specific APIs
│   ├── view/
│   │   ├── common/
│   │   │   ├── home_screen/         # Shared home components
│   │   │   ├── loginregister/       # Authentication screens
│   │   │   ├── splash_screen/       # App splash screen
│   │   │   └── welcome_screen/      # Welcome screen
│   │   ├── jobseeker/
│   │   │   ├── details/             # Jobseeker detail screens
│   │   │   ├── edit/                # Profile editing
│   │   │   ├── screens/             # Main jobseeker screens
│   │   │   └── jhome_page.dart      # Jobseeker home
│   │   └── salonowner2/
│   │       ├── detils/              # Salon detail screens
│   │       ├── edit/                # Salon editing screens
│   │       ├── screens/             # Main salon owner screens
│   │       └── shome_page.dart      # Salon owner home
│   ├── model/                       # Data models
│   ├── controllers/                 # Business logic
│   ├── utils/                       # Utility functions
│   └── uttilits/
│       ├── api_constants.dart       # API endpoints
│       ├── color_const.dart         # Color definitions
│       ├── image_const.dart         # Image assets
│       └── work_status_manager.dart # Status management
├── assets/
│   └── images/                      # App images and icons
├── android/                         # Android configuration
├── ios/                             # iOS configuration
├── web/                             # Web configuration
└── pubspec.yaml                     # Dependencies
```

## 🚀 Setup & Installation

### Prerequisites
- Flutter SDK 3.0+
- Dart SDK 3.0+
- Android Studio / VS Code
- Git

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd mountvoq
   ```

2. **Install Dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure API Endpoints**
   - Update `lib/uttilits/api_constants.dart`
   - Set the correct backend URL

4. **Run the Application**
   ```bash
   flutter run
   ```

### Environment Configuration

1. **API Configuration**
   ```dart
   // lib/uttilits/api_constants.dart
   static const String baseUrl = "http://your-backend-url:8000/api";
   ```

2. **Platform Configuration**
   - Android: Update `android/app/build.gradle`
   - iOS: Update `ios/Runner/Info.plist`

## 📡 API Documentation

### Authentication Endpoints

#### Login
```
POST /api/login/
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password"
}
```

#### Register Salon Owner
```
POST /api/register/admin/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password",
  "password2": "password",
  "salon_name": "Salon Name",
  "owner_name": "Owner Name",
  "location": "Address",
  "pin_code": "123456",
  "google_map_link": "https://maps.google.com/...",
  "contact_no": "1234567890",
  "salon_type": "MALE|FEMALE|UNISEX"
}
```

#### Register Job Seeker
```
POST /api/register/jobseeker/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password",
  "password2": "password",
  "name": "Full Name",
  "contact_no": "1234567890",
  "gender": "MALE|FEMALE",
  "date_of_birth": "1990-01-01",
  "place": "City",
  "expertise_areas": [1, 2, 3],
  "years_of_experience": "5",
  "social_link": "https://linkedin.com/..."
}
```

### Salon Management Endpoints

#### Get Salon Profile
```
GET /api/salon-profile/
Authorization: Token <token>
```

#### Update Salon Profile
```
PATCH /api/salon-profile/
Authorization: Token <token>
Content-Type: multipart/form-data

{
  "salon_name": "Updated Name",
  "owner_name": "Owner Name",
  "location": "Address",
  "pin_code": "123456",
  "google_map_link": "https://maps.google.com/...",
  "contact_no": "1234567890",
  "salon_type": "UNISEX",
  "about": "Salon description",
  "profile_picture": <file>,
  "uploaded_images": [<files>]
}
```

#### Upload Salon Images
```
POST /api/salon-profile/images/
Authorization: Token <token>
Content-Type: multipart/form-data

{
  "uploaded_images": [<files>]
}
```

#### Delete Salon Image
```
DELETE /api/salon-image/{id}/
Authorization: Token <token>
```

### Job Management Endpoints

#### Create Job
```
POST /api/jobs/
Authorization: Token <token>
Content-Type: application/json

{
  "title": "Job Title",
  "location": "Location",
  "job_type": "FULL_TIME|PART_TIME",
  "job_summary": "Job description",
  "benefits": ["Benefit 1", "Benefit 2"],
  "commute_info": "Commute details",
  "experience": "Experience required",
  "gender": "MALE|FEMALE|ANY",
  "is_active": true
}
```

#### Get Jobs
```
GET /api/jobs/
Authorization: Token <token>
```

#### Update Job
```
PATCH /api/jobs/{id}/
Authorization: Token <token>
```

#### Delete Job
```
DELETE /api/jobs/{id}/
Authorization: Token <token>
```

### Application Management Endpoints

#### Get Applications
```
GET /api/applications/
Authorization: Token <token>
```

#### Create Application
```
POST /api/applications/
Authorization: Token <token>
Content-Type: application/json

{
  "job_id": 1,
  "cover_letter": "Cover letter text"
}
```

### Invitation Management Endpoints

#### Get Invitations
```
GET /api/salon-invitations/
Authorization: Token <token>
```

#### Update Invitation Status
```
PATCH /api/salon-invitations/{id}/
Authorization: Token <token>
Content-Type: application/json

{
  "status": "accepted|rejected"
}
```

### Request Management Endpoints

#### Get Salon Requests
```
GET /api/salon-requests-list/
Authorization: Token <token>
```

#### Delete Salon Request
```
DELETE /api/salon-requests/{id}/delete/
Authorization: Token <token>
```

### Analytics Endpoints

#### Customer Visits
```
GET /api/analytics/customer-visits/
Authorization: Token <token>
```

#### Operational Expenses
```
GET /api/analytics/operational-expenses/
Authorization: Token <token>
```

#### Product Purchases
```
GET /api/analytics/product-purchases/
Authorization: Token <token>
```

## 👥 User Roles & Workflows

### Salon Owner Workflow

1. **Registration & Profile Setup**
   - Register with salon details
   - Upload salon images
   - Complete profile information

2. **Job Management**
   - Create job postings
   - Review applications
   - Manage job status

3. **Employee Management**
   - Send invitations to professionals
   - Track hired employees
   - Manage employee performance

4. **Business Analytics**
   - Monitor customer visits
   - Track expenses and income
   - Analyze business metrics

### Job Seeker Workflow

1. **Registration & Profile Setup**
   - Register with personal details
   - Upload work pictures
   - Set expertise areas

2. **Job Search & Application**
   - Browse available jobs
   - Apply for positions
   - Track application status

3. **Communication**
   - Respond to invitations
   - Send requests to salons
   - Handle joining requests

## 🔑 Key Components

### Authentication System
- **Token-based authentication**
- **Secure password handling**
- **Session management**
- **Role-based access control**

### File Management
- **Image upload for profiles**
- **Work picture management**
- **Document handling**
- **Media file storage**

### State Management
- **Local state with setState**
- **SharedPreferences for persistence**
- **API state management**
- **Error handling**

### UI Components
- **Custom app bars**
- **Responsive layouts**
- **Loading states**
- **Error states**
- **Confirmation dialogs**

## 📝 Development Guidelines

### Code Style
- Follow Dart/Flutter conventions
- Use meaningful variable names
- Add comments for complex logic
- Maintain consistent formatting

### File Organization
- Group related files in directories
- Use descriptive file names
- Separate UI, business logic, and data layers

### Error Handling
- Implement try-catch blocks
- Show user-friendly error messages
- Log errors for debugging
- Handle network failures gracefully

### Performance
- Optimize image loading
- Implement lazy loading
- Minimize API calls
- Use efficient data structures

## 🚀 Deployment

### Android Deployment
1. **Build APK**
   ```bash
   flutter build apk --release
   ```

2. **Build App Bundle**
   ```bash
   flutter build appbundle --release
   ```

### iOS Deployment
1. **Build iOS App**
   ```bash
   flutter build ios --release
   ```

2. **Archive in Xcode**
   - Open `ios/Runner.xcworkspace`
   - Archive and upload to App Store

### Web Deployment
1. **Build Web App**
   ```bash
   flutter build web --release
   ```

2. **Deploy to Server**
   - Upload build/web contents
   - Configure web server

## 🔧 Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Check network connectivity
   - Verify API endpoint URLs
   - Ensure backend is running

2. **Image Loading Issues**
   - Check file permissions
   - Verify image formats
   - Ensure proper error handling

3. **Authentication Problems**
   - Clear app data
   - Check token expiration
   - Verify login credentials

4. **Build Errors**
   - Run `flutter clean`
   - Update dependencies
   - Check Flutter version

### Debug Tools
- **Flutter Inspector**: UI debugging
- **Network Inspector**: API debugging
- **Performance Profiler**: Performance analysis
- **Logs**: Console debugging

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: [Project Wiki]
- **Issues**: [GitHub Issues]

## 📄 License

This project is proprietary software. All rights reserved.

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**Maintained By**: MountVoq Development Team
